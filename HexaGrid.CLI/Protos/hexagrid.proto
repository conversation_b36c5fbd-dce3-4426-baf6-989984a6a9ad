syntax = "proto3";

option csharp_namespace = "HexaGrid.CLI.Grpc";

package hexagrid;

// HexaGrid Controller Service
service HexaGridController {
  // Agent registration and heartbeat
  rpc RegisterAgent(RegisterAgentRequest) returns (RegisterAgentResponse);
  rpc SendHeartbeat(HeartbeatRequest) returns (HeartbeatResponse);
  rpc UnregisterAgent(UnregisterAgentRequest) returns (UnregisterAgentResponse);
  
  // Workflow management
  rpc SubmitWorkflow(SubmitWorkflowRequest) returns (SubmitWorkflowResponse);
  rpc GetWorkflowStatus(GetWorkflowStatusRequest) returns (GetWorkflowStatusResponse);
  rpc CancelWorkflow(CancelWorkflowRequest) returns (CancelWorkflowResponse);
  
  // Node execution
  rpc GetPendingWork(GetPendingWorkRequest) returns (GetPendingWorkResponse);
  rpc ReportNodeResult(ReportNodeResultRequest) returns (ReportNodeResultResponse);
  
  // Cluster information
  rpc GetClusterInfo(GetClusterInfoRequest) returns (GetClusterInfoResponse);
}

// HexaGrid Agent Service (for peer-to-peer communication)
service HexaGridAgent {
  rpc ExecuteNode(ExecuteNodeRequest) returns (ExecuteNodeResponse);
  rpc GetAgentStatus(GetAgentStatusRequest) returns (GetAgentStatusResponse);
}

// Agent Registration
message RegisterAgentRequest {
  string node_name = 1;
  string cluster_token = 2;
  AgentInfo agent_info = 3;
}

message RegisterAgentResponse {
  bool success = 1;
  string error_message = 2;
  string agent_id = 3;
  ClusterConfig cluster_config = 4;
}

message AgentInfo {
  string version = 1;
  string platform = 2;
  string architecture = 3;
  map<string, string> labels = 4;
  ResourceInfo resources = 5;
}

message ResourceInfo {
  double cpu_cores = 1;
  int64 memory_bytes = 2;
  int64 storage_bytes = 3;
  double cpu_usage_percent = 4;
  int64 memory_used_bytes = 5;
  int64 storage_used_bytes = 6;
}

message ClusterConfig {
  string cluster_id = 1;
  repeated string controller_endpoints = 2;
  int32 heartbeat_interval_seconds = 3;
  int32 work_poll_interval_seconds = 4;
}

// Heartbeat
message HeartbeatRequest {
  string agent_id = 1;
  string cluster_token = 2;
  AgentStatus status = 3;
  ResourceInfo resources = 4;
}

message HeartbeatResponse {
  bool success = 1;
  string error_message = 2;
  repeated string pending_commands = 3;
}

message AgentStatus {
  AgentState state = 1;
  int32 active_nodes = 2;
  int64 last_activity = 3;
  string version = 4;
}

enum AgentState {
  UNKNOWN = 0;
  STARTING = 1;
  READY = 2;
  BUSY = 3;
  DRAINING = 4;
  OFFLINE = 5;
}

// Agent Unregistration
message UnregisterAgentRequest {
  string agent_id = 1;
  string cluster_token = 2;
}

message UnregisterAgentResponse {
  bool success = 1;
  string error_message = 2;
}

// Workflow Submission
message SubmitWorkflowRequest {
  string cluster_token = 1;
  WorkflowDefinition workflow = 2;
  WorkflowOptions options = 3;
}

message SubmitWorkflowResponse {
  bool success = 1;
  string error_message = 2;
  string workflow_id = 3;
}

message WorkflowDefinition {
  string name = 1;
  string description = 2;
  repeated NodeDefinition nodes = 3;
  repeated ConnectionDefinition connections = 4;
  map<string, string> variables = 5;
}

message NodeDefinition {
  string id = 1;
  string type = 2;
  map<string, string> inputs = 3;
  map<string, string> outputs = 4;
  NodeConstraints constraints = 5;
}

message ConnectionDefinition {
  string from_node_id = 1;
  string from_port = 2;
  string to_node_id = 3;
  string to_port = 4;
  string condition = 5;
}

message NodeConstraints {
  repeated string required_labels = 1;
  repeated string preferred_labels = 2;
  ResourceRequirements resources = 3;
}

message ResourceRequirements {
  double cpu_cores = 1;
  int64 memory_bytes = 2;
  int64 storage_bytes = 3;
}

message WorkflowOptions {
  string namespace = 1;
  bool stop_on_first_error = 2;
  int32 timeout_seconds = 3;
  int32 retry_count = 4;
  map<string, string> labels = 5;
}

// Workflow Status
message GetWorkflowStatusRequest {
  string cluster_token = 1;
  string workflow_id = 2;
}

message GetWorkflowStatusResponse {
  bool success = 1;
  string error_message = 2;
  WorkflowStatus status = 3;
}

message WorkflowStatus {
  string workflow_id = 1;
  string name = 2;
  WorkflowState state = 3;
  int64 submitted_at = 4;
  int64 started_at = 5;
  int64 completed_at = 6;
  int32 total_nodes = 7;
  int32 completed_nodes = 8;
  int32 failed_nodes = 9;
  repeated NodeStatus node_statuses = 10;
  string error_message = 11;
}

enum WorkflowState {
  WORKFLOW_UNKNOWN = 0;
  WORKFLOW_SUBMITTED = 1;
  WORKFLOW_RUNNING = 2;
  WORKFLOW_COMPLETED = 3;
  WORKFLOW_FAILED = 4;
  WORKFLOW_CANCELLED = 5;
}

message NodeStatus {
  string node_id = 1;
  NodeState state = 2;
  string assigned_agent = 3;
  int64 started_at = 4;
  int64 completed_at = 5;
  string error_message = 6;
  map<string, string> outputs = 7;
}

enum NodeState {
  NODE_UNKNOWN = 0;
  NODE_PENDING = 1;
  NODE_RUNNING = 2;
  NODE_COMPLETED = 3;
  NODE_FAILED = 4;
  NODE_CANCELLED = 5;
}

// Workflow Cancellation
message CancelWorkflowRequest {
  string cluster_token = 1;
  string workflow_id = 2;
}

message CancelWorkflowResponse {
  bool success = 1;
  string error_message = 2;
}

// Work Distribution
message GetPendingWorkRequest {
  string agent_id = 1;
  string cluster_token = 2;
  int32 max_nodes = 3;
}

message GetPendingWorkResponse {
  bool success = 1;
  string error_message = 2;
  repeated WorkItem work_items = 3;
}

message WorkItem {
  string workflow_id = 1;
  string node_id = 2;
  NodeDefinition node_definition = 3;
  map<string, string> context = 4;
  int64 assigned_at = 5;
}

// Node Result Reporting
message ReportNodeResultRequest {
  string agent_id = 1;
  string cluster_token = 2;
  string workflow_id = 3;
  string node_id = 4;
  NodeExecutionResult result = 5;
}

message ReportNodeResultResponse {
  bool success = 1;
  string error_message = 2;
}

message NodeExecutionResult {
  bool success = 1;
  string error_message = 2;
  map<string, string> outputs = 3;
  int64 started_at = 4;
  int64 completed_at = 5;
  int32 attempt_count = 6;
  repeated string logs = 7;
}

// Node Execution (Agent-to-Agent)
message ExecuteNodeRequest {
  string workflow_id = 1;
  string node_id = 2;
  NodeDefinition node_definition = 3;
  map<string, string> context = 4;
}

message ExecuteNodeResponse {
  bool success = 1;
  string error_message = 2;
  NodeExecutionResult result = 3;
}

// Agent Status
message GetAgentStatusRequest {
  string requesting_agent = 1;
}

message GetAgentStatusResponse {
  bool success = 1;
  string error_message = 2;
  AgentStatus status = 3;
  ResourceInfo resources = 4;
}

// Cluster Information
message GetClusterInfoRequest {
  string cluster_token = 1;
}

message GetClusterInfoResponse {
  bool success = 1;
  string error_message = 2;
  ClusterInfo cluster_info = 3;
}

message ClusterInfo {
  string cluster_id = 1;
  string version = 2;
  int32 total_agents = 3;
  int32 ready_agents = 4;
  int32 active_workflows = 5;
  repeated AgentInfo agents = 6;
}
