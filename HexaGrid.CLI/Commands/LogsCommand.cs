using System.CommandLine;

namespace HexaGrid.CLI.Commands;

public static class LogsCommand
{
    public static Command CreateCommand()
    {
        var command = new Command("logs", "View workflow execution logs");

        var workflowArgument = new Argument<string?>(
            name: "workflow",
            description: "Workflow ID or name");

        var serverOption = new Option<string>(
            aliases: ["--server", "-s"],
            getDefaultValue: () => "localhost:6443",
            description: "Controller server address");

        var namespaceOption = new Option<string>(
            aliases: ["--namespace", "-n"],
            getDefaultValue: () => "default",
            description: "Namespace to query");

        var followOption = new Option<bool>(
            aliases: ["--follow", "-f"],
            description: "Follow log output");

        var tailOption = new Option<int>(
            aliases: ["--tail"],
            getDefaultValue: () => 100,
            description: "Number of lines to show from the end");

        var nodeOption = new Option<string?>(
            aliases: ["--node"],
            description: "Show logs for specific node only");

        var sinceOption = new Option<string?>(
            aliases: ["--since"],
            description: "Show logs since timestamp (e.g., '2h', '30m', '2023-01-01T10:00:00Z')");

        command.AddArgument(workflowArgument);
        command.AddOption(serverOption);
        command.AddOption(namespaceOption);
        command.AddOption(followOption);
        command.AddOption(tailOption);
        command.AddOption(nodeOption);
        command.AddOption(sinceOption);

        command.SetHandler(async (workflow, server, ns, follow, tail, node, since, verbose) =>
        {
            await ShowLogsAsync(workflow, server, ns, follow, tail, node, since, verbose);
        }, workflowArgument, serverOption, namespaceOption, followOption, tailOption, 
           nodeOption, sinceOption, new Option<bool>("--verbose"));

        return command;
    }

    private static async Task ShowLogsAsync(string? workflow, string server, string ns, 
        bool follow, int tail, string? node, string? since, bool verbose)
    {
        if (string.IsNullOrEmpty(workflow))
        {
            Console.WriteLine("🔍 Available workflows:");
            await ShowAvailableWorkflowsAsync(server, ns);
            return;
        }

        Console.WriteLine($"📋 Showing logs for workflow: {workflow}");
        if (!string.IsNullOrEmpty(node))
        {
            Console.WriteLine($"   Node filter: {node}");
        }
        if (!string.IsNullOrEmpty(since))
        {
            Console.WriteLine($"   Since: {since}");
        }
        Console.WriteLine($"   Tail: {tail} lines");
        Console.WriteLine();

        // TODO: Implement actual log streaming
        // For now, simulate log output
        var logEntries = GenerateSampleLogs(workflow, node, tail);

        foreach (var entry in logEntries)
        {
            var color = entry.Level switch
            {
                "ERROR" => ConsoleColor.Red,
                "WARN" => ConsoleColor.Yellow,
                "INFO" => ConsoleColor.White,
                "DEBUG" => ConsoleColor.Gray,
                _ => ConsoleColor.White
            };

            Console.ForegroundColor = color;
            Console.WriteLine($"{entry.Timestamp:yyyy-MM-dd HH:mm:ss.fff} [{entry.Level}] {entry.NodeId}: {entry.Message}");
            Console.ResetColor();
        }

        if (follow)
        {
            Console.WriteLine();
            Console.WriteLine("📡 Following logs (Ctrl+C to stop)...");
            
            // Simulate real-time log streaming
            var cancellationTokenSource = new CancellationTokenSource();
            Console.CancelKeyPress += (_, e) =>
            {
                e.Cancel = true;
                cancellationTokenSource.Cancel();
            };

            try
            {
                await FollowLogsAsync(workflow, node, cancellationTokenSource.Token);
            }
            catch (OperationCanceledException)
            {
                Console.WriteLine();
                Console.WriteLine("📋 Log following stopped");
            }
        }
    }

    private static async Task ShowAvailableWorkflowsAsync(string server, string ns)
    {
        // TODO: Implement actual workflow listing
        await Task.Delay(300);

        var workflows = new[]
        {
            new { Id = "wf-123abc", Name = "data-processing", Status = "Running", Age = "5m" },
            new { Id = "wf-456def", Name = "backup-job", Status = "Completed", Age = "1h" },
            new { Id = "wf-789ghi", Name = "analytics-pipeline", Status = "Failed", Age = "30m" }
        };

        Console.WriteLine("ID         NAME                 STATUS      AGE");
        Console.WriteLine("--         ----                 ------      ---");
        foreach (var wf in workflows)
        {
            Console.WriteLine($"{wf.Id,-10} {wf.Name,-19} {wf.Status,-10} {wf.Age}");
        }
        Console.WriteLine();
        Console.WriteLine("Usage: hexagrid logs <workflow-id-or-name>");
    }

    private static List<LogEntry> GenerateSampleLogs(string workflow, string? nodeFilter, int tail)
    {
        var logs = new List<LogEntry>();
        var random = new Random();
        var baseTime = DateTime.UtcNow.AddMinutes(-30);

        var nodes = new[] { "node-1", "node-2", "node-3", "node-4" };
        var levels = new[] { "INFO", "DEBUG", "WARN", "ERROR" };
        var messages = new[]
        {
            "Node execution started",
            "Processing input data",
            "Connecting to external service",
            "Data transformation completed",
            "Sending output to next node",
            "Node execution completed successfully",
            "Retrying failed operation",
            "Connection timeout occurred",
            "Invalid input data format",
            "Memory usage: 45%"
        };

        for (int i = 0; i < Math.Min(tail * 2, 200); i++)
        {
            var nodeId = nodes[random.Next(nodes.Length)];
            
            // Apply node filter if specified
            if (!string.IsNullOrEmpty(nodeFilter) && nodeId != nodeFilter)
                continue;

            logs.Add(new LogEntry
            {
                Timestamp = baseTime.AddSeconds(i * 2),
                Level = levels[random.Next(levels.Length)],
                NodeId = nodeId,
                Message = messages[random.Next(messages.Length)]
            });
        }

        return logs.OrderBy(l => l.Timestamp).TakeLast(tail).ToList();
    }

    private static async Task FollowLogsAsync(string workflow, string? nodeFilter, CancellationToken cancellationToken)
    {
        var random = new Random();
        var nodes = new[] { "node-1", "node-2", "node-3", "node-4" };
        var levels = new[] { "INFO", "DEBUG", "WARN" };
        var messages = new[]
        {
            "Processing batch 1234",
            "Heartbeat sent to controller",
            "Cache hit for key: user_data_456",
            "Background cleanup completed",
            "Metrics updated",
            "Health check passed"
        };

        while (!cancellationToken.IsCancellationRequested)
        {
            await Task.Delay(random.Next(1000, 3000), cancellationToken);

            var nodeId = nodes[random.Next(nodes.Length)];
            
            // Apply node filter if specified
            if (!string.IsNullOrEmpty(nodeFilter) && nodeId != nodeFilter)
                continue;

            var entry = new LogEntry
            {
                Timestamp = DateTime.UtcNow,
                Level = levels[random.Next(levels.Length)],
                NodeId = nodeId,
                Message = messages[random.Next(messages.Length)]
            };

            var color = entry.Level switch
            {
                "WARN" => ConsoleColor.Yellow,
                "INFO" => ConsoleColor.White,
                "DEBUG" => ConsoleColor.Gray,
                _ => ConsoleColor.White
            };

            Console.ForegroundColor = color;
            Console.WriteLine($"{entry.Timestamp:yyyy-MM-dd HH:mm:ss.fff} [{entry.Level}] {entry.NodeId}: {entry.Message}");
            Console.ResetColor();
        }
    }
}

public class LogEntry
{
    public DateTime Timestamp { get; set; }
    public string Level { get; set; } = string.Empty;
    public string NodeId { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
}
