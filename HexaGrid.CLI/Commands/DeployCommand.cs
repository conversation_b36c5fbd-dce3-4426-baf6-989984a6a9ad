using System.CommandLine;
using System.Text.Json;
using HexaGrid.Core.Graph;
using HexaGrid.CLI.Services;
using HexaGrid.Core.Models;

namespace HexaGrid.CLI.Commands;

public static class DeployCommand
{
    public static Command CreateCommand()
    {
        var command = new Command("deploy", "Deploy a workflow to the cluster");

        var fileArgument = new Argument<string>(
            name: "file",
            description: "Path to workflow definition file (JSON or YAML)");

        var serverOption = new Option<string>(
            aliases: ["--server", "-s"],
            getDefaultValue: () => "localhost:6443",
            description: "Controller server address");

        var namespaceOption = new Option<string>(
            aliases: ["--namespace", "-n"],
            getDefaultValue: () => "default",
            description: "Namespace to deploy to");

        var waitOption = new Option<bool>(
            aliases: ["--wait", "-w"],
            description: "Wait for workflow to complete");

        var timeoutOption = new Option<int>(
            aliases: ["--timeout"],
            getDefaultValue: () => 300,
            description: "Timeout in seconds when waiting");

        command.AddArgument(fileArgument);
        command.AddOption(serverOption);
        command.AddOption(namespaceOption);
        command.AddOption(waitOption);
        command.AddOption(timeoutOption);

        command.SetHandler(async (file, server, ns, wait, timeout, verbose) =>
        {
            await DeployWorkflowAsync(file, server, ns, wait, timeout, verbose);
        }, fileArgument, serverOption, namespaceOption, waitOption, timeoutOption,
           new Option<bool>("--verbose"));

        return command;
    }

    private static async Task DeployWorkflowAsync(string file, string server, string ns, 
        bool wait, int timeout, bool verbose)
    {
        Console.WriteLine("🚀 Deploying Workflow");
        Console.WriteLine("====================");
        Console.WriteLine($"📄 File: {file}");
        Console.WriteLine($"🌐 Server: {server}");
        Console.WriteLine($"📦 Namespace: {ns}");
        Console.WriteLine();

        try
        {
            // Check if file exists
            if (!File.Exists(file))
            {
                Console.WriteLine($"❌ File not found: {file}");
                return;
            }

            // Load and parse workflow
            var content = await File.ReadAllTextAsync(file);
            NodeGraph? workflow = null;

            if (file.EndsWith(".json", StringComparison.OrdinalIgnoreCase))
            {
                workflow = JsonSerializer.Deserialize<NodeGraph>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
            }
            else if (file.EndsWith(".yaml", StringComparison.OrdinalIgnoreCase) || 
                     file.EndsWith(".yml", StringComparison.OrdinalIgnoreCase))
            {
                // TODO: Add YAML support
                Console.WriteLine("❌ YAML support not yet implemented. Please use JSON format.");
                return;
            }
            else
            {
                Console.WriteLine("❌ Unsupported file format. Use .json, .yaml, or .yml");
                return;
            }

            if (workflow == null)
            {
                Console.WriteLine("❌ Failed to parse workflow definition");
                return;
            }

            Console.WriteLine($"📊 Workflow: {workflow.Name}");
            Console.WriteLine($"   Nodes: {workflow.Nodes.Count}");
            Console.WriteLine($"   Connections: {workflow.Connections.Count}");
            Console.WriteLine();

            // Validate workflow
            Console.WriteLine("🔍 Validating workflow...");
            var validationResult = ValidateWorkflow(workflow);
            if (!validationResult.IsValid)
            {
                Console.WriteLine("❌ Workflow validation failed:");
                foreach (var error in validationResult.Errors)
                {
                    Console.WriteLine($"   • {error}");
                }
                return;
            }
            Console.WriteLine("✅ Workflow validation passed");

            // Deploy to cluster
            Console.WriteLine("📤 Submitting to cluster...");
            var deploymentResult = await SubmitWorkflowAsync(workflow, server, ns);
            
            if (!deploymentResult.Success)
            {
                Console.WriteLine($"❌ Deployment failed: {deploymentResult.ErrorMessage}");
                return;
            }

            Console.WriteLine($"✅ Workflow deployed successfully");
            Console.WriteLine($"   Workflow ID: {deploymentResult.WorkflowId}");
            Console.WriteLine($"   Status: {deploymentResult.Status}");

            if (wait)
            {
                Console.WriteLine();
                Console.WriteLine($"⏳ Waiting for completion (timeout: {timeout}s)...");
                await WaitForCompletionAsync(deploymentResult.WorkflowId!, server, timeout);
            }
            else
            {
                Console.WriteLine();
                Console.WriteLine("💡 To monitor progress:");
                Console.WriteLine($"   hexagrid logs --workflow {deploymentResult.WorkflowId} --server {server}");
                Console.WriteLine($"   hexagrid get workflows --server {server}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Deployment error: {ex.Message}");
            if (verbose)
            {
                Console.WriteLine($"   {ex.StackTrace}");
            }
        }
    }

    private static WorkflowValidationResult ValidateWorkflow(NodeGraph workflow)
    {
        var result = new WorkflowValidationResult();

        // Basic validation
        if (string.IsNullOrEmpty(workflow.Name))
        {
            result.Errors.Add("Workflow name is required");
        }

        if (workflow.Nodes.Count == 0)
        {
            result.Errors.Add("Workflow must contain at least one node");
        }

        // Check for duplicate node IDs
        var duplicateIds = workflow.Nodes.GroupBy(n => n.Id)
            .Where(g => g.Count() > 1)
            .Select(g => g.Key);

        foreach (var id in duplicateIds)
        {
            result.Errors.Add($"Duplicate node ID: {id}");
        }

        // Validate connections
        foreach (var connection in workflow.Connections)
        {
            if (!workflow.Nodes.Any(n => n.Id == connection.FromNodeId))
            {
                result.Errors.Add($"Connection references non-existent source node: {connection.FromNodeId}");
            }

            if (!workflow.Nodes.Any(n => n.Id == connection.ToNodeId))
            {
                result.Errors.Add($"Connection references non-existent target node: {connection.ToNodeId}");
            }
        }

        // Check for cycles (basic check)
        try
        {
            workflow.GetExecutionOrder();
        }
        catch (InvalidOperationException ex) when (ex.Message.Contains("Circular dependency"))
        {
            result.Errors.Add("Workflow contains circular dependencies");
        }

        result.IsValid = result.Errors.Count == 0;
        return result;
    }

    private static async Task<DeploymentResult> SubmitWorkflowAsync(NodeGraph workflow, string server, string ns)
    {
        // TODO: Implement actual cluster communication
        // For now, simulate deployment
        await Task.Delay(1000);

        return new DeploymentResult
        {
            Success = true,
            WorkflowId = Guid.NewGuid().ToString(),
            Status = "Submitted"
        };
    }

    private static async Task WaitForCompletionAsync(string workflowId, string server, int timeoutSeconds)
    {
        // TODO: Implement actual status polling
        var startTime = DateTime.UtcNow;
        var timeout = TimeSpan.FromSeconds(timeoutSeconds);

        while (DateTime.UtcNow - startTime < timeout)
        {
            await Task.Delay(2000);
            Console.WriteLine($"   Status: Running... ({(DateTime.UtcNow - startTime).TotalSeconds:F0}s elapsed)");
            
            // Simulate completion after 10 seconds
            if ((DateTime.UtcNow - startTime).TotalSeconds > 10)
            {
                Console.WriteLine("✅ Workflow completed successfully");
                return;
            }
        }

        Console.WriteLine("⏰ Timeout reached - workflow may still be running");
    }
}

public class WorkflowValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
}

public class DeploymentResult
{
    public bool Success { get; set; }
    public string? WorkflowId { get; set; }
    public string? Status { get; set; }
    public string? ErrorMessage { get; set; }
}
