using System.CommandLine;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using HexaGrid.CLI.Services;

namespace HexaGrid.CLI.Commands;

public static class ServerCommand
{
    public static Command CreateCommand()
    {
        var command = new Command("server", "Start HexaGrid controller (control plane)");

        var portOption = new Option<int>(
            aliases: ["--port", "-p"],
            getDefaultValue: () => 6443,
            description: "Port to listen on");

        var dataDirectoryOption = new Option<string>(
            aliases: ["--data-dir", "-d"],
            getDefaultValue: () => "./hexagrid-data",
            description: "Directory to store cluster data");

        var bindAddressOption = new Option<string>(
            aliases: ["--bind-address", "-b"],
            getDefaultValue: () => "0.0.0.0",
            description: "Address to bind to");

        var tokenOption = new Option<string?>(
            aliases: ["--token", "-t"],
            description: "Cluster token for agent authentication");

        command.AddOption(portOption);
        command.AddOption(dataDirectoryOption);
        command.AddOption(bindAddressOption);
        command.AddOption(tokenOption);

        command.SetHandler(async (port, dataDir, bindAddress, token, verbose, config) =>
        {
            await RunServerAsync(port, dataDir, bindAddress, token, verbose, config);
        }, portOption, dataDirectoryOption, bindAddressOption, tokenOption,
           new Option<bool>("--verbose"), new Option<string?>("--config"));

        return command;
    }

    private static async Task RunServerAsync(int port, string dataDir, string bindAddress, 
        string? token, bool verbose, string? config)
    {
        Console.WriteLine("🔷 HexaGrid Controller");
        Console.WriteLine("=====================");
        Console.WriteLine($"📍 Bind Address: {bindAddress}:{port}");
        Console.WriteLine($"📂 Data Directory: {dataDir}");
        Console.WriteLine($"🔑 Token: {(string.IsNullOrEmpty(token) ? "Auto-generated" : "Provided")}");
        Console.WriteLine();

        // Create data directory if it doesn't exist
        Directory.CreateDirectory(dataDir);

        // Generate token if not provided
        if (string.IsNullOrEmpty(token))
        {
            token = GenerateClusterToken();
            Console.WriteLine($"🔐 Generated cluster token: {token}");
            Console.WriteLine("   Use this token when starting agents:");
            Console.WriteLine($"   hexagrid agent --server {bindAddress}:{port} --token {token}");
            Console.WriteLine();
        }

        // Build and run the host
        var builder = Host.CreateDefaultBuilder();
        
        builder.ConfigureServices(services =>
        {
            services.AddSingleton<IClusterStateService, ClusterStateService>();
            services.AddSingleton<INodeDiscoveryService, NodeDiscoveryService>();
            services.AddSingleton<IWorkflowExecutionService, WorkflowExecutionService>();
            services.AddHostedService<ControllerService>();
            
            // Configure controller settings
            services.Configure<ControllerOptions>(options =>
            {
                options.Port = port;
                options.DataDirectory = dataDir;
                options.BindAddress = bindAddress;
                options.ClusterToken = token;
            });
        });

        builder.ConfigureLogging(logging =>
        {
            logging.ClearProviders();
            logging.AddConsole();
            logging.SetMinimumLevel(verbose ? LogLevel.Debug : LogLevel.Information);
        });

        var host = builder.Build();

        // Handle graceful shutdown
        var cancellationTokenSource = new CancellationTokenSource();
        Console.CancelKeyPress += (_, e) =>
        {
            e.Cancel = true;
            cancellationTokenSource.Cancel();
        };

        try
        {
            Console.WriteLine("▶️ Starting controller...");
            await host.RunAsync(cancellationTokenSource.Token);
        }
        catch (OperationCanceledException)
        {
            Console.WriteLine("🛑 Controller stopped");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Controller failed: {ex.Message}");
            throw;
        }
    }

    private static string GenerateClusterToken()
    {
        return Convert.ToBase64String(System.Security.Cryptography.RandomNumberGenerator.GetBytes(32));
    }
}

public class ControllerOptions
{
    public int Port { get; set; } = 6443;
    public string DataDirectory { get; set; } = "./hexagrid-data";
    public string BindAddress { get; set; } = "0.0.0.0";
    public string ClusterToken { get; set; } = string.Empty;
}
