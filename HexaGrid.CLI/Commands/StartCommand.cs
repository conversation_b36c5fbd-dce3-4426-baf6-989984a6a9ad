using System.CommandLine;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using HexaGrid.CLI.Services;

namespace HexaGrid.CLI.Commands;

public static class StartCommand
{
    public static Command CreateCommand()
    {
        var command = new Command("start", "🚀 Start HexaGrid (easiest way to begin)")
        {
            Description = "Starts HexaGrid in single-node mode - perfect for getting started!\n\n" +
                         "This command will:\n" +
                         "• Start the HexaGrid controller\n" +
                         "• Start a local agent\n" +
                         "• Set up everything with sensible defaults\n" +
                         "• Generate security tokens automatically\n\n" +
                         "No configuration needed - just run and go!"
        };

        var portOption = new Option<int>(
            aliases: ["--port", "-p"],
            getDefaultValue: () => 6443,
            description: "Port to run on (default: 6443)");

        var dataDirectoryOption = new Option<string>(
            aliases: ["--data-dir", "-d"],
            getDefaultValue: () => "./hexagrid-data",
            description: "Where to store data (default: ./hexagrid-data)");

        var webUIOption = new Option<bool>(
            aliases: ["--web-ui"],
            getDefaultValue: () => true,
            description: "Start web dashboard (default: true)");

        var webPortOption = new Option<int>(
            aliases: ["--web-port"],
            getDefaultValue: () => 8080,
            description: "Web dashboard port (default: 8080)");

        command.AddOption(portOption);
        command.AddOption(dataDirectoryOption);
        command.AddOption(webUIOption);
        command.AddOption(webPortOption);

        command.SetHandler(async (port, dataDir, webUI, webPort, verbose) =>
        {
            await StartHexaGridAsync(port, dataDir, webUI, webPort, verbose);
        }, portOption, dataDirectoryOption, webUIOption, webPortOption, 
           new Option<bool>("--verbose"));

        return command;
    }

    private static async Task StartHexaGridAsync(int port, string dataDir, bool webUI, int webPort, bool verbose)
    {
        Console.WriteLine("🔷 Starting HexaGrid");
        Console.WriteLine("===================");
        Console.WriteLine();
        
        // Show what we're doing
        Console.WriteLine("🏗️ Setting up HexaGrid with these settings:");
        Console.WriteLine($"   📍 API Port: {port}");
        Console.WriteLine($"   📂 Data Directory: {dataDir}");
        Console.WriteLine($"   🌐 Web Dashboard: {(webUI ? $"http://localhost:{webPort}" : "Disabled")}");
        Console.WriteLine();

        try
        {
            // Create data directory
            if (!Directory.Exists(dataDir))
            {
                Directory.CreateDirectory(dataDir);
                Console.WriteLine($"✅ Created data directory: {dataDir}");
            }

            // Generate cluster token
            var token = GenerateClusterToken();
            var tokenFile = Path.Combine(dataDir, "cluster-token.txt");
            await File.WriteAllTextAsync(tokenFile, token);
            Console.WriteLine($"🔐 Generated cluster token (saved to {tokenFile})");

            // Show startup progress
            Console.WriteLine();
            Console.WriteLine("🚀 Starting services...");
            
            // Build and configure the host
            var builder = Host.CreateDefaultBuilder();
            
            builder.ConfigureServices(services =>
            {
                services.AddSingleton<IClusterStateService, ClusterStateService>();
                services.AddSingleton<INodeDiscoveryService, NodeDiscoveryService>();
                services.AddSingleton<IWorkflowExecutionService, WorkflowExecutionService>();
                services.AddSingleton<IConfigurationService, ConfigurationService>();
                
                // Start both controller and agent in the same process
                services.AddHostedService<ControllerService>();
                services.AddHostedService<AgentService>();
                
                if (webUI)
                {
                    services.AddHostedService<WebDashboardService>();
                }
                
                // Configure controller
                services.Configure<ControllerOptions>(options =>
                {
                    options.Port = port;
                    options.DataDirectory = dataDir;
                    options.BindAddress = "127.0.0.1"; // Local only for single-node
                    options.ClusterToken = token;
                });
                
                // Configure agent
                services.Configure<AgentOptions>(options =>
                {
                    options.ServerAddress = $"127.0.0.1:{port}";
                    options.ClusterToken = token;
                    options.NodeName = "local-agent";
                    options.DataDirectory = Path.Combine(dataDir, "agent");
                    options.Labels = new Dictionary<string, string>
                    {
                        { "mode", "single-node" },
                        { "role", "worker" }
                    };
                });

                // Configure web dashboard
                if (webUI)
                {
                    services.Configure<WebDashboardOptions>(options =>
                    {
                        options.Port = webPort;
                        options.BindAddress = "127.0.0.1";
                    });
                }
            });

            builder.ConfigureLogging(logging =>
            {
                logging.ClearProviders();
                logging.AddConsole();
                logging.SetMinimumLevel(verbose ? LogLevel.Debug : LogLevel.Information);
            });

            var host = builder.Build();

            // Handle graceful shutdown
            var cancellationTokenSource = new CancellationTokenSource();
            Console.CancelKeyPress += (_, e) =>
            {
                e.Cancel = true;
                Console.WriteLine();
                Console.WriteLine("🛑 Shutting down HexaGrid...");
                cancellationTokenSource.Cancel();
            };

            // Show success message
            Console.WriteLine("✅ Controller started");
            Console.WriteLine("✅ Local agent started");
            if (webUI)
            {
                Console.WriteLine($"✅ Web dashboard started at http://localhost:{webPort}");
            }
            Console.WriteLine();
            
            // Show helpful next steps
            Console.WriteLine("🎉 HexaGrid is ready!");
            Console.WriteLine();
            Console.WriteLine("Next steps:");
            Console.WriteLine($"  📄 Create a sample workflow:  hexagrid init");
            Console.WriteLine($"  🚀 Deploy a workflow:         hexagrid deploy my-workflow.yaml");
            Console.WriteLine($"  📊 Check status:              hexagrid status");
            Console.WriteLine($"  📋 View logs:                 hexagrid logs");
            if (webUI)
            {
                Console.WriteLine($"  🌐 Open web dashboard:        http://localhost:{webPort}");
            }
            Console.WriteLine();
            Console.WriteLine("Press Ctrl+C to stop HexaGrid");
            Console.WriteLine();

            // Run the host
            await host.RunAsync(cancellationTokenSource.Token);
        }
        catch (OperationCanceledException)
        {
            Console.WriteLine("✅ HexaGrid stopped cleanly");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Failed to start HexaGrid: {ex.Message}");
            if (verbose)
            {
                Console.WriteLine($"   Details: {ex}");
            }
            Console.WriteLine();
            Console.WriteLine("💡 Troubleshooting tips:");
            Console.WriteLine($"   • Make sure port {port} is not in use");
            Console.WriteLine($"   • Check that you have write access to {dataDir}");
            Console.WriteLine($"   • Try running with --verbose for more details");
        }
    }

    private static string GenerateClusterToken()
    {
        return Convert.ToBase64String(System.Security.Cryptography.RandomNumberGenerator.GetBytes(32));
    }
}

// Additional options classes
public class WebDashboardOptions
{
    public int Port { get; set; } = 8080;
    public string BindAddress { get; set; } = "127.0.0.1";
}
