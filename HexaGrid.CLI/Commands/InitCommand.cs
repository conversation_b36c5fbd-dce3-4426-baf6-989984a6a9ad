using System.CommandLine;
using System.Text.Json;

namespace HexaGrid.CLI.Commands;

public static class InitCommand
{
    public static Command CreateCommand()
    {
        var command = new Command("init", "🏗️ Create a sample workflow to get started")
        {
            Description = "Creates example workflow files to help you get started with HexaGrid.\n\n" +
                         "This command will create:\n" +
                         "• hello-world.yaml - A simple first workflow\n" +
                         "• data-processing.yaml - A more complex example\n" +
                         "• README.md - Explanation of the workflows\n\n" +
                         "Perfect for learning how HexaGrid works!"
        };

        var nameOption = new Option<string>(
            aliases: ["--name", "-n"],
            getDefaultValue: () => "hello-world",
            description: "Name of the workflow to create");

        var typeOption = new Option<string>(
            aliases: ["--type", "-t"],
            getDefaultValue: () => "simple",
            description: "Type of workflow (simple, data-processing, web-scraping, notification)");

        var outputDirOption = new Option<string>(
            aliases: ["--output", "-o"],
            getDefaultValue: () => "./workflows",
            description: "Directory to create workflows in");

        var forceOption = new Option<bool>(
            aliases: ["--force", "-f"],
            description: "Overwrite existing files");

        command.AddOption(nameOption);
        command.AddOption(typeOption);
        command.AddOption(outputDirOption);
        command.AddOption(forceOption);

        command.SetHandler(async (name, type, outputDir, force) =>
        {
            await CreateSampleWorkflowAsync(name, type, outputDir, force);
        }, nameOption, typeOption, outputDirOption, forceOption);

        return command;
    }

    private static async Task CreateSampleWorkflowAsync(string name, string type, string outputDir, bool force)
    {
        Console.WriteLine("🏗️ Creating Sample Workflow");
        Console.WriteLine("===========================");
        Console.WriteLine($"📄 Name: {name}");
        Console.WriteLine($"🏷️ Type: {type}");
        Console.WriteLine($"📂 Output: {outputDir}");
        Console.WriteLine();

        try
        {
            // Create output directory
            if (!Directory.Exists(outputDir))
            {
                Directory.CreateDirectory(outputDir);
                Console.WriteLine($"✅ Created directory: {outputDir}");
            }

            // Generate workflow based on type
            var workflow = type.ToLower() switch
            {
                "simple" or "hello-world" => CreateSimpleWorkflow(name),
                "data-processing" => CreateDataProcessingWorkflow(name),
                "web-scraping" => CreateWebScrapingWorkflow(name),
                "notification" => CreateNotificationWorkflow(name),
                _ => CreateSimpleWorkflow(name)
            };

            // Write workflow file
            var workflowFile = Path.Combine(outputDir, $"{name}.yaml");
            if (File.Exists(workflowFile) && !force)
            {
                Console.WriteLine($"❌ File already exists: {workflowFile}");
                Console.WriteLine("   Use --force to overwrite, or choose a different name");
                return;
            }

            await File.WriteAllTextAsync(workflowFile, workflow);
            Console.WriteLine($"✅ Created workflow: {workflowFile}");

            // Create README
            var readmeFile = Path.Combine(outputDir, "README.md");
            var readme = CreateReadme(type);
            await File.WriteAllTextAsync(readmeFile, readme);
            Console.WriteLine($"✅ Created documentation: {readmeFile}");

            // Show next steps
            Console.WriteLine();
            Console.WriteLine("🎉 Sample workflow created successfully!");
            Console.WriteLine();
            Console.WriteLine("Next steps:");
            Console.WriteLine($"  📖 Read the guide:        cat {readmeFile}");
            Console.WriteLine($"  ✅ Validate the workflow: hexagrid validate {workflowFile}");
            Console.WriteLine($"  🚀 Run the workflow:      hexagrid deploy {workflowFile}");
            Console.WriteLine($"  📊 Check status:          hexagrid status");
            Console.WriteLine();
            Console.WriteLine("💡 Tip: Edit the YAML file to customize your workflow!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Failed to create workflow: {ex.Message}");
        }
    }

    private static string CreateSimpleWorkflow(string name)
    {
        return $@"# HexaGrid Workflow: {name}
# This is a simple ""Hello World"" workflow to get you started

name: {name}
description: A simple workflow that prints messages and waits

nodes:
  - id: start
    type: PrintNode
    inputs:
      message: ""🚀 Starting workflow: {name}""

  - id: wait
    type: WaitNode
    inputs:
      duration: 2
      message: ""⏳ Waiting 2 seconds...""

  - id: process
    type: PrintNode
    inputs:
      message: ""⚙️ Processing data...""

  - id: finish
    type: PrintNode
    inputs:
      message: ""✅ Workflow completed successfully!""

connections:
  - from: start
    to: wait
  - from: wait
    to: process
  - from: process
    to: finish

# Variables you can customize
variables:
  environment: ""development""
  debug: true";
    }

    private static string CreateDataProcessingWorkflow(string name)
    {
        return $@"# HexaGrid Workflow: {name}
# A more complex workflow showing data processing patterns

name: {name}
description: Process data through multiple transformation steps

nodes:
  - id: load-data
    type: FileNode
    inputs:
      action: ""read""
      path: ""./data/input.csv""

  - id: validate-data
    type: ConditionalNode
    inputs:
      condition: ""data.length > 0""

  - id: transform-data
    type: MathNode
    inputs:
      operation: ""aggregate""
      function: ""sum""

  - id: save-results
    type: FileNode
    inputs:
      action: ""write""
      path: ""./data/output.json""

  - id: send-notification
    type: EmailNode
    inputs:
      to: ""<EMAIL>""
      subject: ""Data processing completed""

  - id: cleanup
    type: FileNode
    inputs:
      action: ""delete""
      path: ""./data/temp/*""

connections:
  - from: load-data
    to: validate-data
  - from: validate-data
    to: transform-data
    condition: ""success""
  - from: transform-data
    to: save-results
  - from: save-results
    to: send-notification
  - from: send-notification
    to: cleanup

variables:
  batch_size: 1000
  retry_count: 3";
    }

    private static string CreateWebScrapingWorkflow(string name)
    {
        return $@"# HexaGrid Workflow: {name}
# Web scraping and data collection workflow

name: {name}
description: Scrape websites and process the collected data

nodes:
  - id: fetch-urls
    type: HttpRequestNode
    inputs:
      url: ""https://api.example.com/urls""
      method: ""GET""

  - id: scrape-pages
    type: LoopNode
    inputs:
      items: ""{{{{ fetch-urls.response.urls }}}}""

  - id: extract-data
    type: HttpRequestNode
    inputs:
      url: ""{{{{ item }}}}""
      method: ""GET""

  - id: parse-content
    type: VariableNode
    inputs:
      extract: ""title, description, price""

  - id: store-data
    type: DatabaseNode
    inputs:
      action: ""insert""
      table: ""products""

  - id: generate-report
    type: FileNode
    inputs:
      action: ""write""
      path: ""./reports/scraping-report.html""
      template: ""report-template.html""

connections:
  - from: fetch-urls
    to: scrape-pages
  - from: scrape-pages
    to: extract-data
  - from: extract-data
    to: parse-content
  - from: parse-content
    to: store-data
  - from: store-data
    to: generate-report

variables:
  delay_between_requests: 1000
  max_retries: 3
  user_agent: ""HexaGrid Bot 1.0""";
    }

private static string CreateNotificationWorkflow(string name)
    {
        return $@"# HexaGrid Workflow: {name}
# Send notifications through multiple channels

name: {name}
description: Send notifications via email, Slack, and webhooks

nodes:
  - id: prepare-message
    type: VariableNode
    inputs:
      title: ""System Alert""
      message: ""Important notification from HexaGrid""
      priority: ""high""

  - id: send-email
    type: EmailNode
    inputs:
      to: ""<EMAIL>""
      subject: ""{{{{ prepare-message.title }}}}""
      body: ""{{{{ prepare-message.message }}}}""

  - id: send-slack
    type: HttpRequestNode
    inputs:
      url: ""https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK""
      method: ""POST""
      body: |
        {{
          ""text"": ""{{{{ prepare-message.title }}}}"",
          ""attachments"": [
            {{
              ""color"": ""danger"",
              ""text"": ""{{{{ prepare-message.message }}}}""
            }}
          ]
        }}

  - id: log-notification
    type: FileNode
    inputs:
      action: ""append""
      path: ""./logs/notifications.log""
      content: ""{{{{ timestamp }}}} - {{{{ prepare-message.title }}}}""

  - id: update-dashboard
    type: HttpRequestNode
    inputs:
      url: ""https://dashboard.company.com/api/alerts""
      method: ""POST""
      headers:
        Authorization: ""Bearer YOUR_API_TOKEN""

connections:
  - from: prepare-message
    to: send-email
  - from: prepare-message
    to: send-slack
  - from: send-email
    to: log-notification
  - from: send-slack
    to: update-dashboard

variables:
  environment: ""production""
  notification_level: ""critical""";
    }

    private static string CreateReadme(string type)
    {
        return @"# HexaGrid Workflow Examples

Welcome to HexaGrid! This directory contains sample workflows to help you get started.

## What is HexaGrid?

HexaGrid is a workflow automation system that lets you connect different tasks (called ""nodes"") together to create powerful automated processes. Think of it like connecting LEGO blocks - each block does one thing, but together they can build amazing things!

## Understanding Workflows

A workflow is made up of:

- **Nodes**: Individual tasks (like ""send email"", ""process file"", ""wait 5 seconds"")
- **Connections**: How data flows between nodes
- **Variables**: Settings you can customize

## Available Node Types

HexaGrid comes with these built-in nodes:

### Basic Nodes
- **PrintNode**: Display messages
- **WaitNode**: Pause for a specified time
- **VariableNode**: Store and manipulate data

### File Operations
- **FileNode**: Read, write, or delete files

### Network Operations
- **HttpRequestNode**: Make web requests
- **EmailNode**: Send emails

### Logic & Control
- **ConditionalNode**: Make decisions based on conditions
- **LoopNode**: Repeat tasks multiple times
- **BranchNode**: Split workflow into parallel paths

### Data Processing
- **MathNode**: Perform calculations
- **DatabaseNode**: Work with databases

## Running Your First Workflow

1. **Start HexaGrid** (if not already running):
   ```bash
   hexagrid start
   ```

2. **Validate your workflow**:
   ```bash
   hexagrid validate hello-world.yaml
   ```

3. **Deploy and run**:
   ```bash
   hexagrid deploy hello-world.yaml
   ```

4. **Check the status**:
   ```bash
   hexagrid status
   ```

5. **View logs**:
   ```bash
   hexagrid logs
   ```

## Customizing Workflows

You can edit the YAML files to:

- Change messages and settings
- Add new nodes
- Modify connections
- Update variables

## Need Help?

- Run `hexagrid --help` for command help
- Visit https://hexagrid.dev/docs for full documentation
- Check out more examples at https://hexagrid.dev/examples

Happy automating!";
    }
}
