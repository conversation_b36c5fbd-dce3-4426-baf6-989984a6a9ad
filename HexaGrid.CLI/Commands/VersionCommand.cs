using System.CommandLine;
using System.Reflection;

namespace HexaGrid.CLI.Commands;

public static class VersionCommand
{
    public static Command CreateCommand()
    {
        var command = new Command("version", "Show version information");

        var outputOption = new Option<string>(
            aliases: ["--output", "-o"],
            getDefaultValue: () => "text",
            description: "Output format (text, json)");

        var clientOnlyOption = new Option<bool>(
            aliases: ["--client"],
            description: "Show client version only");

        command.AddOption(outputOption);
        command.AddOption(clientOnlyOption);

        command.SetHandler(async (output, clientOnly, server) =>
        {
            await ShowVersionAsync(output, clientOnly, server);
        }, outputOption, clientOnlyOption, new Option<string>("--server"));

        return command;
    }

    private static async Task ShowVersionAsync(string output, bool clientOnly, string? server)
    {
        var clientVersion = GetClientVersion();

        if (output == "json")
        {
            object versionInfo;

            if (!clientOnly && !string.IsNullOrEmpty(server))
            {
                var serverVersion = await GetServerVersionAsync(server);
                versionInfo = new
                {
                    Client = clientVersion,
                    Server = serverVersion
                };
            }
            else
            {
                versionInfo = new
                {
                    Client = clientVersion
                };
            }

            Console.WriteLine(System.Text.Json.JsonSerializer.Serialize(versionInfo, new System.Text.Json.JsonSerializerOptions { WriteIndented = true }));
        }
        else
        {
            Console.WriteLine("🔷 HexaGrid Version Information");
            Console.WriteLine("===============================");
            Console.WriteLine();
            
            Console.WriteLine("Client:");
            Console.WriteLine($"  Version:    {clientVersion.Version}");
            Console.WriteLine($"  Build Date: {clientVersion.BuildDate}");
            Console.WriteLine($"  Git Commit: {clientVersion.GitCommit}");
            Console.WriteLine($"  Go Version: {clientVersion.GoVersion}");
            Console.WriteLine($"  Platform:   {clientVersion.Platform}");

            if (!clientOnly && !string.IsNullOrEmpty(server))
            {
                Console.WriteLine();
                Console.WriteLine($"Server ({server}):");
                
                try
                {
                    var serverVersion = await GetServerVersionAsync(server);
                    Console.WriteLine($"  Version:    {serverVersion.Version}");
                    Console.WriteLine($"  Build Date: {serverVersion.BuildDate}");
                    Console.WriteLine($"  Git Commit: {serverVersion.GitCommit}");
                    Console.WriteLine($"  Uptime:     {serverVersion.Uptime}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"  Status:     ❌ Unable to connect ({ex.Message})");
                }
            }
        }
    }

    private static VersionInfo GetClientVersion()
    {
        var assembly = Assembly.GetExecutingAssembly();
        var version = assembly.GetName().Version?.ToString() ?? "unknown";
        var buildDate = GetBuildDate(assembly);
        
        return new VersionInfo
        {
            Version = version,
            BuildDate = buildDate,
            GitCommit = GetGitCommit(),
            GoVersion = Environment.Version.ToString(),
            Platform = $"{Environment.OSVersion.Platform} {Environment.OSVersion.Version}"
        };
    }

    private static async Task<VersionInfo> GetServerVersionAsync(string server)
    {
        // TODO: Implement actual server version API call
        // For now, simulate server response
        await Task.Delay(500);

        return new VersionInfo
        {
            Version = "1.0.0",
            BuildDate = "2024-01-15T10:30:00Z",
            GitCommit = "abc123def456",
            Uptime = "2h 15m 30s"
        };
    }

    private static string GetBuildDate(Assembly assembly)
    {
        try
        {
            var attribute = assembly.GetCustomAttribute<AssemblyMetadataAttribute>();
            if (attribute?.Key == "BuildDate")
            {
                return attribute.Value ?? "unknown";
            }

            // Fallback to app directory creation time for single-file apps
            var appDirectory = AppContext.BaseDirectory;
            if (!string.IsNullOrEmpty(appDirectory) && Directory.Exists(appDirectory))
            {
                return Directory.GetCreationTimeUtc(appDirectory).ToString("yyyy-MM-ddTHH:mm:ssZ");
            }
        }
        catch
        {
            // Ignore errors
        }

        return "unknown";
    }

    private static string GetGitCommit()
    {
        try
        {
            var assembly = Assembly.GetExecutingAssembly();
            var attribute = assembly.GetCustomAttribute<AssemblyMetadataAttribute>();
            if (attribute?.Key == "GitCommit")
            {
                return attribute.Value ?? "unknown";
            }
        }
        catch
        {
            // Ignore errors
        }

        return "unknown";
    }
}

public class VersionInfo
{
    public string Version { get; set; } = string.Empty;
    public string BuildDate { get; set; } = string.Empty;
    public string GitCommit { get; set; } = string.Empty;
    public string? GoVersion { get; set; }
    public string? Platform { get; set; }
    public string? Uptime { get; set; }
}
