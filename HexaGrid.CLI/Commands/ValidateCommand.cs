using System.CommandLine;
using System.Text.Json;
using HexaGrid.Core.Graph;
using HexaGrid.Core.Models;

namespace HexaGrid.CLI.Commands;

public static class ValidateCommand
{
    public static Command CreateCommand()
    {
        var command = new Command("validate", "✅ Check if a workflow file is valid")
        {
            Description = "Validates a workflow file to make sure it's correctly formatted and will run properly.\n\n" +
                         "This command checks:\n" +
                         "• YAML/JSON syntax is correct\n" +
                         "• All required fields are present\n" +
                         "• Node types exist and are valid\n" +
                         "• Connections are properly defined\n" +
                         "• No circular dependencies exist\n\n" +
                         "Run this before deploying to catch errors early!"
        };

        var fileArgument = new Argument<string>(
            name: "file",
            description: "Path to the workflow file to validate");

        var strictOption = new Option<bool>(
            aliases: ["--strict"],
            description: "Enable strict validation (more thorough checks)");

        var fixOption = new Option<bool>(
            aliases: ["--fix"],
            description: "Attempt to automatically fix common issues");

        var outputOption = new Option<string>(
            aliases: ["--output", "-o"],
            getDefaultValue: () => "text",
            description: "Output format (text, json)");

        command.AddArgument(fileArgument);
        command.AddOption(strictOption);
        command.AddOption(fixOption);
        command.AddOption(outputOption);

        command.SetHandler(async (file, strict, fix, output) =>
        {
            await ValidateWorkflowAsync(file, strict, fix, output);
        }, fileArgument, strictOption, fixOption, outputOption);

        return command;
    }

    private static async Task ValidateWorkflowAsync(string file, bool strict, bool fix, string output)
    {
        if (output == "text")
        {
            Console.WriteLine("✅ Validating Workflow");
            Console.WriteLine("======================");
            Console.WriteLine($"📄 File: {file}");
            Console.WriteLine($"🔍 Mode: {(strict ? "Strict" : "Standard")}");
            Console.WriteLine();
        }

        var validationResult = new ValidationResult();

        try
        {
            // Check if file exists
            if (!File.Exists(file))
            {
                validationResult.AddError("FILE_NOT_FOUND", $"File not found: {file}");
                await OutputResultAsync(validationResult, output);
                return;
            }

            // Read and parse file
            var content = await File.ReadAllTextAsync(file);
            NodeGraph? workflow = null;

            if (file.EndsWith(".json", StringComparison.OrdinalIgnoreCase))
            {
                workflow = await ValidateJsonWorkflowAsync(content, validationResult);
            }
            else if (file.EndsWith(".yaml", StringComparison.OrdinalIgnoreCase) || 
                     file.EndsWith(".yml", StringComparison.OrdinalIgnoreCase))
            {
                workflow = await ValidateYamlWorkflowAsync(content, validationResult);
            }
            else
            {
                validationResult.AddError("UNSUPPORTED_FORMAT", 
                    "Unsupported file format. Use .json, .yaml, or .yml");
                await OutputResultAsync(validationResult, output);
                return;
            }

            if (workflow == null)
            {
                await OutputResultAsync(validationResult, output);
                return;
            }

            // Validate workflow structure
            await ValidateWorkflowStructureAsync(workflow, validationResult, strict);

            // Validate nodes
            await ValidateNodesAsync(workflow, validationResult, strict);

            // Validate connections
            await ValidateConnectionsAsync(workflow, validationResult, strict);

            // Check for circular dependencies
            await ValidateExecutionOrderAsync(workflow, validationResult);

            // Apply fixes if requested
            if (fix && validationResult.HasWarnings)
            {
                await ApplyFixesAsync(workflow, file, validationResult);
            }

            await OutputResultAsync(validationResult, output);
        }
        catch (Exception ex)
        {
            validationResult.AddError("VALIDATION_FAILED", $"Validation failed: {ex.Message}");
            await OutputResultAsync(validationResult, output);
        }
    }

    private static async Task<NodeGraph?> ValidateJsonWorkflowAsync(string content, ValidationResult result)
    {
        try
        {
            var workflow = JsonSerializer.Deserialize<NodeGraph>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            if (workflow != null)
            {
                result.AddSuccess("JSON_PARSING", "JSON syntax is valid");
            }

            return workflow;
        }
        catch (JsonException ex)
        {
            result.AddError("JSON_SYNTAX", $"Invalid JSON syntax: {ex.Message}");
            return null;
        }
    }

    private static async Task<NodeGraph?> ValidateYamlWorkflowAsync(string content, ValidationResult result)
    {
        // TODO: Implement YAML parsing
        // For now, suggest using JSON
        result.AddWarning("YAML_NOT_IMPLEMENTED", 
            "YAML support is coming soon. Please use JSON format for now.");
        
        // Try to provide helpful conversion suggestion
        result.AddInfo("CONVERSION_TIP", 
            "You can convert YAML to JSON using online tools or 'yq' command line tool");
        
        return null;
    }

    private static async Task ValidateWorkflowStructureAsync(NodeGraph workflow, ValidationResult result, bool strict)
    {
        // Check required fields
        if (string.IsNullOrEmpty(workflow.Name))
        {
            result.AddError("MISSING_NAME", "Workflow must have a name");
        }
        else
        {
            result.AddSuccess("WORKFLOW_NAME", $"Workflow name: '{workflow.Name}'");
        }

        if (workflow.Nodes.Count == 0)
        {
            result.AddError("NO_NODES", "Workflow must contain at least one node");
        }
        else
        {
            result.AddSuccess("NODE_COUNT", $"Found {workflow.Nodes.Count} nodes");
        }

        // Check for duplicate node IDs
        var duplicateIds = workflow.Nodes.GroupBy(n => n.Id)
            .Where(g => g.Count() > 1)
            .Select(g => g.Key);

        foreach (var id in duplicateIds)
        {
            result.AddError("DUPLICATE_NODE_ID", $"Duplicate node ID: {id}");
        }

        if (strict)
        {
            // Additional strict checks
            if (string.IsNullOrEmpty(workflow.Description))
            {
                result.AddWarning("MISSING_DESCRIPTION", "Consider adding a workflow description");
            }

            if (workflow.Variables.Count == 0)
            {
                result.AddInfo("NO_VARIABLES", "No variables defined (this is optional)");
            }
        }

        await Task.CompletedTask;
    }

    private static async Task ValidateNodesAsync(NodeGraph workflow, ValidationResult result, bool strict)
    {
        var validNodeTypes = new[]
        {
            "PrintNode", "WaitNode", "MathNode", "ConditionalNode", "VariableNode",
            "BranchNode", "LoopNode", "HttpRequestNode", "FileNode", "DatabaseNode", "EmailNode"
        };

        foreach (var node in workflow.Nodes)
        {
            // Check node ID
            if (string.IsNullOrEmpty(node.Id))
            {
                result.AddError("MISSING_NODE_ID", "Node is missing an ID");
                continue;
            }

            // Check node type
            if (string.IsNullOrEmpty(node.Type))
            {
                result.AddError("MISSING_NODE_TYPE", $"Node '{node.Id}' is missing a type");
                continue;
            }

            if (!validNodeTypes.Contains(node.Type))
            {
                result.AddError("INVALID_NODE_TYPE", 
                    $"Node '{node.Id}' has invalid type '{node.Type}'. " +
                    $"Valid types: {string.Join(", ", validNodeTypes)}");
            }
            else
            {
                result.AddSuccess("VALID_NODE", $"Node '{node.Id}' ({node.Type}) is valid");
            }

            // Validate node-specific requirements
            await ValidateNodeSpecificAsync(node, result, strict);
        }
    }

    private static async Task ValidateNodeSpecificAsync(Node node, ValidationResult result, bool strict)
    {
        // Node-specific validation rules
        switch (node.Type)
        {
            case "WaitNode":
                if (!node.Inputs.ContainsKey("duration"))
                {
                    result.AddError("MISSING_DURATION", $"WaitNode '{node.Id}' requires 'duration' input");
                }
                break;

            case "HttpRequestNode":
                if (!node.Inputs.ContainsKey("url"))
                {
                    result.AddError("MISSING_URL", $"HttpRequestNode '{node.Id}' requires 'url' input");
                }
                break;

            case "FileNode":
                if (!node.Inputs.ContainsKey("action") || !node.Inputs.ContainsKey("path"))
                {
                    result.AddError("MISSING_FILE_PARAMS", 
                        $"FileNode '{node.Id}' requires 'action' and 'path' inputs");
                }
                break;

            case "EmailNode":
                var requiredEmailFields = new[] { "to", "subject" };
                foreach (var field in requiredEmailFields)
                {
                    if (!node.Inputs.ContainsKey(field))
                    {
                        result.AddError("MISSING_EMAIL_FIELD", 
                            $"EmailNode '{node.Id}' requires '{field}' input");
                    }
                }
                break;
        }

        await Task.CompletedTask;
    }

    private static async Task ValidateConnectionsAsync(NodeGraph workflow, ValidationResult result, bool strict)
    {
        foreach (var connection in workflow.Connections)
        {
            // Check source node exists
            if (!workflow.Nodes.Any(n => n.Id == connection.FromNodeId))
            {
                result.AddError("INVALID_CONNECTION_SOURCE", 
                    $"Connection references non-existent source node: {connection.FromNodeId}");
            }

            // Check target node exists
            if (!workflow.Nodes.Any(n => n.Id == connection.ToNodeId))
            {
                result.AddError("INVALID_CONNECTION_TARGET", 
                    $"Connection references non-existent target node: {connection.ToNodeId}");
            }

            // Check for self-connections
            if (connection.FromNodeId == connection.ToNodeId)
            {
                result.AddWarning("SELF_CONNECTION", 
                    $"Node '{connection.FromNodeId}' connects to itself");
            }
        }

        if (workflow.Connections.Count > 0)
        {
            result.AddSuccess("CONNECTIONS", $"Found {workflow.Connections.Count} connections");
        }

        await Task.CompletedTask;
    }

    private static async Task ValidateExecutionOrderAsync(NodeGraph workflow, ValidationResult result)
    {
        try
        {
            var executionOrder = workflow.GetExecutionOrder();
            result.AddSuccess("EXECUTION_ORDER", 
                $"Execution order validated - {executionOrder.Count} nodes will execute");
        }
        catch (InvalidOperationException ex) when (ex.Message.Contains("Circular dependency"))
        {
            result.AddError("CIRCULAR_DEPENDENCY", "Workflow contains circular dependencies");
        }

        await Task.CompletedTask;
    }

    private static async Task ApplyFixesAsync(NodeGraph workflow, string file, ValidationResult result)
    {
        // TODO: Implement automatic fixes for common issues
        result.AddInfo("AUTO_FIX", "Automatic fixes are not yet implemented");
        await Task.CompletedTask;
    }

    private static async Task OutputResultAsync(ValidationResult result, string output)
    {
        if (output == "json")
        {
            var jsonResult = new
            {
                valid = result.IsValid,
                errors = result.Errors,
                warnings = result.Warnings,
                successes = result.Successes,
                info = result.Info
            };

            Console.WriteLine(JsonSerializer.Serialize(jsonResult, new JsonSerializerOptions { WriteIndented = true }));
        }
        else
        {
            // Text output
            if (result.IsValid)
            {
                Console.WriteLine("🎉 Validation Passed!");
                Console.WriteLine();
            }
            else
            {
                Console.WriteLine("❌ Validation Failed!");
                Console.WriteLine();
            }

            // Show errors
            if (result.Errors.Any())
            {
                Console.WriteLine("❌ Errors:");
                foreach (var error in result.Errors)
                {
                    Console.WriteLine($"   • {error.Message}");
                }
                Console.WriteLine();
            }

            // Show warnings
            if (result.Warnings.Any())
            {
                Console.WriteLine("⚠️ Warnings:");
                foreach (var warning in result.Warnings)
                {
                    Console.WriteLine($"   • {warning.Message}");
                }
                Console.WriteLine();
            }

            // Show successes (only if no errors)
            if (result.IsValid && result.Successes.Any())
            {
                Console.WriteLine("✅ Validation Details:");
                foreach (var success in result.Successes)
                {
                    Console.WriteLine($"   • {success.Message}");
                }
                Console.WriteLine();
            }

            // Show info
            if (result.Info.Any())
            {
                Console.WriteLine("ℹ️ Information:");
                foreach (var info in result.Info)
                {
                    Console.WriteLine($"   • {info.Message}");
                }
                Console.WriteLine();
            }

            // Show next steps
            if (result.IsValid)
            {
                Console.WriteLine("Next steps:");
                Console.WriteLine("  🚀 Deploy the workflow: hexagrid deploy <file>");
                Console.WriteLine("  📊 Check status:        hexagrid status");
            }
            else
            {
                Console.WriteLine("💡 Fix the errors above and run validation again");
            }
        }

        await Task.CompletedTask;
    }
}

public class ValidationResult
{
    public List<ValidationMessage> Errors { get; } = new();
    public List<ValidationMessage> Warnings { get; } = new();
    public List<ValidationMessage> Successes { get; } = new();
    public List<ValidationMessage> Info { get; } = new();

    public bool IsValid => !Errors.Any();
    public bool HasWarnings => Warnings.Any();

    public void AddError(string code, string message) => Errors.Add(new ValidationMessage(code, message));
    public void AddWarning(string code, string message) => Warnings.Add(new ValidationMessage(code, message));
    public void AddSuccess(string code, string message) => Successes.Add(new ValidationMessage(code, message));
    public void AddInfo(string code, string message) => Info.Add(new ValidationMessage(code, message));
}

public class ValidationMessage
{
    public string Code { get; }
    public string Message { get; }

    public ValidationMessage(string code, string message)
    {
        Code = code;
        Message = message;
    }
}
