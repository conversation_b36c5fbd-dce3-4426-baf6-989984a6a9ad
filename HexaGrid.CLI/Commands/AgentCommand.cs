using System.CommandLine;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using HexaGrid.CLI.Services;

namespace HexaGrid.CLI.Commands;

public static class AgentCommand
{
    public static Command CreateCommand()
    {
        var command = new Command("agent", "Start HexaGrid agent (worker node)");

        var serverOption = new Option<string>(
            aliases: ["--server", "-s"],
            description: "Controller server address (e.g., *************:6443)")
        {
            IsRequired = true
        };

        var tokenOption = new Option<string>(
            aliases: ["--token", "-t"],
            description: "Cluster token for authentication")
        {
            IsRequired = true
        };

        var nodeNameOption = new Option<string?>(
            aliases: ["--node-name", "-n"],
            description: "Name for this agent node (auto-generated if not provided)");

        var dataDirectoryOption = new Option<string>(
            aliases: ["--data-dir", "-d"],
            getDefaultValue: () => "./hexagrid-agent-data",
            description: "Directory to store agent data");

        var labelsOption = new Option<string[]>(
            aliases: ["--label", "-l"],
            description: "Labels for this node (key=value format)")
        {
            AllowMultipleArgumentsPerToken = true
        };

        command.AddOption(serverOption);
        command.AddOption(tokenOption);
        command.AddOption(nodeNameOption);
        command.AddOption(dataDirectoryOption);
        command.AddOption(labelsOption);

        command.SetHandler(async (server, token, nodeName, dataDir, labels, verbose, config) =>
        {
            await RunAgentAsync(server, token, nodeName, dataDir, labels, verbose, config);
        }, serverOption, tokenOption, nodeNameOption, dataDirectoryOption, labelsOption,
           new Option<bool>("--verbose"), new Option<string?>("--config"));

        return command;
    }

    private static async Task RunAgentAsync(string server, string token, string? nodeName, 
        string dataDir, string[] labels, bool verbose, string? config)
    {
        Console.WriteLine("🔷 HexaGrid Agent");
        Console.WriteLine("=================");
        
        // Generate node name if not provided
        if (string.IsNullOrEmpty(nodeName))
        {
            nodeName = $"agent-{Environment.MachineName.ToLower()}-{Guid.NewGuid().ToString("N")[..8]}";
        }

        Console.WriteLine($"🏷️ Node Name: {nodeName}");
        Console.WriteLine($"🌐 Controller: {server}");
        Console.WriteLine($"📂 Data Directory: {dataDir}");
        
        // Parse labels
        var nodeLabels = ParseLabels(labels);
        if (nodeLabels.Any())
        {
            Console.WriteLine($"🏷️ Labels: {string.Join(", ", nodeLabels.Select(kv => $"{kv.Key}={kv.Value}"))}");
        }
        Console.WriteLine();

        // Create data directory if it doesn't exist
        Directory.CreateDirectory(dataDir);

        // Build and run the host
        var builder = Host.CreateDefaultBuilder();
        
        builder.ConfigureServices(services =>
        {
            services.AddSingleton<IClusterStateService, ClusterStateService>();
            services.AddSingleton<INodeDiscoveryService, NodeDiscoveryService>();
            services.AddSingleton<IWorkflowExecutionService, WorkflowExecutionService>();
            services.AddHostedService<AgentService>();
            
            // Configure agent settings
            services.Configure<AgentOptions>(options =>
            {
                options.ServerAddress = server;
                options.ClusterToken = token;
                options.NodeName = nodeName;
                options.DataDirectory = dataDir;
                options.Labels = nodeLabels;
            });
        });

        builder.ConfigureLogging(logging =>
        {
            logging.ClearProviders();
            logging.AddConsole();
            logging.SetMinimumLevel(verbose ? LogLevel.Debug : LogLevel.Information);
        });

        var host = builder.Build();

        // Handle graceful shutdown
        var cancellationTokenSource = new CancellationTokenSource();
        Console.CancelKeyPress += (_, e) =>
        {
            e.Cancel = true;
            cancellationTokenSource.Cancel();
        };

        try
        {
            Console.WriteLine("▶️ Starting agent...");
            await host.RunAsync(cancellationTokenSource.Token);
        }
        catch (OperationCanceledException)
        {
            Console.WriteLine("🛑 Agent stopped");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Agent failed: {ex.Message}");
            throw;
        }
    }

    private static Dictionary<string, string> ParseLabels(string[] labels)
    {
        var result = new Dictionary<string, string>();
        
        foreach (var label in labels)
        {
            var parts = label.Split('=', 2);
            if (parts.Length == 2)
            {
                result[parts[0].Trim()] = parts[1].Trim();
            }
            else
            {
                Console.WriteLine($"⚠️ Invalid label format: {label} (expected key=value)");
            }
        }

        return result;
    }
}

public class AgentOptions
{
    public string ServerAddress { get; set; } = string.Empty;
    public string ClusterToken { get; set; } = string.Empty;
    public string NodeName { get; set; } = string.Empty;
    public string DataDirectory { get; set; } = "./hexagrid-agent-data";
    public Dictionary<string, string> Labels { get; set; } = new();
}
