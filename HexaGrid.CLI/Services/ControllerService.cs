using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.DependencyInjection;
using HexaGrid.CLI.Commands;

namespace HexaGrid.CLI.Services;

public class ControllerService : BackgroundService
{
    private readonly ILogger<ControllerService> _logger;
    private readonly ControllerOptions _options;
    private readonly IClusterStateService _clusterState;
    private readonly INodeDiscoveryService _nodeDiscovery;
    private readonly IWorkflowExecutionService _workflowExecution;
    private IWebHost? _grpcHost;

    public ControllerService(
        ILogger<ControllerService> logger,
        IOptions<ControllerOptions> options,
        IClusterStateService clusterState,
        INodeDiscoveryService nodeDiscovery,
        IWorkflowExecutionService workflowExecution)
    {
        _logger = logger;
        _options = options.Value;
        _clusterState = clusterState;
        _nodeDiscovery = nodeDiscovery;
        _workflowExecution = workflowExecution;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("HexaGrid Controller starting on {BindAddress}:{Port}", 
            _options.BindAddress, _options.Port);

        try
        {
            // Initialize data directory
            await InitializeDataDirectoryAsync();

            // Register controller node
            await RegisterControllerNodeAsync();

            // Start node discovery
            await _nodeDiscovery.StartDiscoveryAsync(stoppingToken);

            // Subscribe to node discovery events
            _nodeDiscovery.NodeDiscovered += OnNodeDiscovered;
            _nodeDiscovery.NodeLost += OnNodeLost;

            // Start gRPC server
            await StartGrpcServerAsync(stoppingToken);

            // Start health monitoring
            await StartHealthMonitoringAsync(stoppingToken);

            _logger.LogInformation("HexaGrid Controller started successfully");

            // Keep running until cancellation
            await Task.Delay(Timeout.Infinite, stoppingToken);
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("HexaGrid Controller is shutting down");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "HexaGrid Controller failed to start");
            throw;
        }
        finally
        {
            await CleanupAsync();
        }
    }

    private async Task InitializeDataDirectoryAsync()
    {
        try
        {
            if (!Directory.Exists(_options.DataDirectory))
            {
                Directory.CreateDirectory(_options.DataDirectory);
                _logger.LogInformation("Created data directory: {DataDirectory}", _options.DataDirectory);
            }

            // Initialize database/storage
            var dbPath = Path.Combine(_options.DataDirectory, "hexagrid.db");
            _logger.LogInformation("Using database: {DatabasePath}", dbPath);

            // TODO: Initialize SQLite database for cluster state
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize data directory");
            throw;
        }
    }

    private async Task RegisterControllerNodeAsync()
    {
        var controllerNode = new NodeInfo
        {
            Id = Environment.MachineName + "-controller",
            Name = "controller-" + Environment.MachineName.ToLower(),
            Role = NodeRole.Controller,
            Status = NodeStatus.Ready,
            Address = _options.BindAddress,
            Port = _options.Port,
            RegisteredAt = DateTime.UtcNow,
            LastSeen = DateTime.UtcNow,
            Version = "1.0.0",
            Labels = new Dictionary<string, string>
            {
                { "node-role", "controller" },
                { "hostname", Environment.MachineName }
            }
        };

        await _clusterState.RegisterNodeAsync(controllerNode);
        _logger.LogInformation("Registered controller node: {NodeName}", controllerNode.Name);
    }

    private async Task StartGrpcServerAsync(CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Starting gRPC server on {BindAddress}:{Port}",
                _options.BindAddress, _options.Port);

            var builder = WebApplication.CreateBuilder();

            // Configure services
            builder.Services.AddGrpc();
            builder.Services.AddSingleton(_clusterState);
            builder.Services.AddSingleton(_workflowExecution);
            builder.Services.AddSingleton(provider =>
                new GrpcControllerService(
                    provider.GetRequiredService<ILogger<GrpcControllerService>>(),
                    _clusterState,
                    _workflowExecution,
                    _options.ClusterToken));

            // Configure web host
            builder.WebHost.UseUrls($"http://{_options.BindAddress}:{_options.Port}");

            var app = builder.Build();

            // Configure gRPC services
            app.MapGrpcService<GrpcControllerService>();

            // Add health check endpoint
            app.MapGet("/health", () => new { status = "healthy", timestamp = DateTime.UtcNow });

            // Start the server
            await app.StartAsync(cancellationToken);
            _logger.LogInformation("gRPC server started successfully");

            // Keep running until cancellation
            await app.WaitForShutdownAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start gRPC server");
            throw;
        }
    }

    private async Task StartHealthMonitoringAsync(CancellationToken cancellationToken)
    {
        _ = Task.Run(async () =>
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                try
                {
                    await PerformHealthCheckAsync();
                    await Task.Delay(TimeSpan.FromSeconds(30), cancellationToken);
                }
                catch (OperationCanceledException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error during health monitoring");
                }
            }
        }, cancellationToken);

        await Task.CompletedTask;
    }

    private async Task PerformHealthCheckAsync()
    {
        var clusterState = await _clusterState.GetClusterStateAsync();
        _logger.LogDebug("Health check - Nodes: {TotalNodes}, Ready: {ReadyNodes}, Not Ready: {NotReadyNodes}",
            clusterState.TotalNodes, clusterState.ReadyNodes, clusterState.NotReadyNodes);

        // Update controller node status
        var controllerNodeId = Environment.MachineName + "-controller";
        await _clusterState.UpdateNodeStatusAsync(controllerNodeId, NodeStatus.Ready);
    }

    private void OnNodeDiscovered(object? sender, NodeDiscoveredEventArgs e)
    {
        _logger.LogInformation("Node discovered: {NodeName} ({NodeId}) at {Address}:{Port}",
            e.Node.Name, e.Node.Id, e.Node.Address, e.Node.Port);

        // TODO: Initiate connection to discovered node
    }

    private void OnNodeLost(object? sender, NodeLostEventArgs e)
    {
        _logger.LogWarning("Node lost: {NodeName} ({NodeId})",
            e.Node.Name, e.Node.Id);

        // TODO: Handle node disconnection
        // - Mark workflows on that node as failed
        // - Reschedule pending work to other nodes
    }

    private async Task CleanupAsync()
    {
        try
        {
            _nodeDiscovery.NodeDiscovered -= OnNodeDiscovered;
            _nodeDiscovery.NodeLost -= OnNodeLost;

            await _nodeDiscovery.StopDiscoveryAsync();
            _logger.LogInformation("HexaGrid Controller stopped");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during controller cleanup");
        }
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("HexaGrid Controller is stopping...");
        await base.StopAsync(cancellationToken);
    }
}
