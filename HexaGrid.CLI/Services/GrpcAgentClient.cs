using Grpc.Net.Client;
using HexaGrid.CLI.Grpc;
using Microsoft.Extensions.Logging;

namespace HexaGrid.CLI.Services;

public interface IGrpcAgentClient : IDisposable
{
    Task<bool> RegisterAsync(string nodeName, string clusterToken, AgentInfo agentInfo);
    Task<bool> SendHeartbeatAsync(string agentId, string clusterToken, AgentStatus status, ResourceInfo resources);
    Task<bool> UnregisterAsync(string agentId, string clusterToken);
    Task<string?> SubmitWorkflowAsync(string clusterToken, WorkflowDefinition workflow, WorkflowOptions options);
    Task<Grpc.WorkflowStatus?> GetWorkflowStatusAsync(string clusterToken, string workflowId);
    Task<bool> CancelWorkflowAsync(string clusterToken, string workflowId);
    Task<ClusterInfo?> GetClusterInfoAsync(string clusterToken);
}

public class GrpcAgentClient : IGrpcAgentClient
{
    private readonly ILogger<GrpcAgentClient> _logger;
    private readonly GrpcChannel _channel;
    private readonly HexaGridController.HexaGridControllerClient _client;
    private bool _disposed;

    public GrpcAgentClient(ILogger<GrpcAgentClient> logger, string serverAddress)
    {
        _logger = logger;
        
        // Configure gRPC channel with appropriate settings
        var channelOptions = new GrpcChannelOptions
        {
            // Allow insecure connections for development
            // In production, this should use TLS
            HttpHandler = new HttpClientHandler
            {
                ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => true
            }
        };

        _channel = GrpcChannel.ForAddress($"http://{serverAddress}", channelOptions);
        _client = new HexaGridController.HexaGridControllerClient(_channel);
    }

    public async Task<bool> RegisterAsync(string nodeName, string clusterToken, AgentInfo agentInfo)
    {
        try
        {
            _logger.LogInformation("Registering agent {NodeName} with controller", nodeName);

            var request = new RegisterAgentRequest
            {
                NodeName = nodeName,
                ClusterToken = clusterToken,
                AgentInfo = agentInfo
            };

            var response = await _client.RegisterAgentAsync(request);
            
            if (response.Success)
            {
                _logger.LogInformation("Agent {NodeName} registered successfully with ID {AgentId}", 
                    nodeName, response.AgentId);
                return true;
            }
            else
            {
                _logger.LogError("Failed to register agent {NodeName}: {ErrorMessage}", 
                    nodeName, response.ErrorMessage);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error registering agent {NodeName}", nodeName);
            return false;
        }
    }

    public async Task<bool> SendHeartbeatAsync(string agentId, string clusterToken, 
        AgentStatus status, ResourceInfo resources)
    {
        try
        {
            var request = new HeartbeatRequest
            {
                AgentId = agentId,
                ClusterToken = clusterToken,
                Status = status,
                Resources = resources
            };

            var response = await _client.SendHeartbeatAsync(request);
            
            if (!response.Success)
            {
                _logger.LogWarning("Heartbeat failed for agent {AgentId}: {ErrorMessage}", 
                    agentId, response.ErrorMessage);
            }

            return response.Success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending heartbeat for agent {AgentId}", agentId);
            return false;
        }
    }

    public async Task<bool> UnregisterAsync(string agentId, string clusterToken)
    {
        try
        {
            _logger.LogInformation("Unregistering agent {AgentId}", agentId);

            var request = new UnregisterAgentRequest
            {
                AgentId = agentId,
                ClusterToken = clusterToken
            };

            var response = await _client.UnregisterAgentAsync(request);
            
            if (response.Success)
            {
                _logger.LogInformation("Agent {AgentId} unregistered successfully", agentId);
            }
            else
            {
                _logger.LogError("Failed to unregister agent {AgentId}: {ErrorMessage}", 
                    agentId, response.ErrorMessage);
            }

            return response.Success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error unregistering agent {AgentId}", agentId);
            return false;
        }
    }

    public async Task<string?> SubmitWorkflowAsync(string clusterToken, WorkflowDefinition workflow, 
        WorkflowOptions options)
    {
        try
        {
            _logger.LogInformation("Submitting workflow {WorkflowName}", workflow.Name);

            var request = new SubmitWorkflowRequest
            {
                ClusterToken = clusterToken,
                Workflow = workflow,
                Options = options
            };

            var response = await _client.SubmitWorkflowAsync(request);
            
            if (response.Success)
            {
                _logger.LogInformation("Workflow {WorkflowName} submitted with ID {WorkflowId}", 
                    workflow.Name, response.WorkflowId);
                return response.WorkflowId;
            }
            else
            {
                _logger.LogError("Failed to submit workflow {WorkflowName}: {ErrorMessage}", 
                    workflow.Name, response.ErrorMessage);
                return null;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error submitting workflow {WorkflowName}", workflow.Name);
            return null;
        }
    }

    public async Task<Grpc.WorkflowStatus?> GetWorkflowStatusAsync(string clusterToken, string workflowId)
    {
        try
        {
            var request = new GetWorkflowStatusRequest
            {
                ClusterToken = clusterToken,
                WorkflowId = workflowId
            };

            var response = await _client.GetWorkflowStatusAsync(request);
            
            if (response.Success)
            {
                return response.Status;
            }
            else
            {
                _logger.LogError("Failed to get workflow status for {WorkflowId}: {ErrorMessage}", 
                    workflowId, response.ErrorMessage);
                return null;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting workflow status for {WorkflowId}", workflowId);
            return null;
        }
    }

    public async Task<bool> CancelWorkflowAsync(string clusterToken, string workflowId)
    {
        try
        {
            _logger.LogInformation("Cancelling workflow {WorkflowId}", workflowId);

            var request = new CancelWorkflowRequest
            {
                ClusterToken = clusterToken,
                WorkflowId = workflowId
            };

            var response = await _client.CancelWorkflowAsync(request);
            
            if (response.Success)
            {
                _logger.LogInformation("Workflow {WorkflowId} cancelled successfully", workflowId);
            }
            else
            {
                _logger.LogError("Failed to cancel workflow {WorkflowId}: {ErrorMessage}", 
                    workflowId, response.ErrorMessage);
            }

            return response.Success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling workflow {WorkflowId}", workflowId);
            return false;
        }
    }

    public async Task<ClusterInfo?> GetClusterInfoAsync(string clusterToken)
    {
        try
        {
            var request = new GetClusterInfoRequest
            {
                ClusterToken = clusterToken
            };

            var response = await _client.GetClusterInfoAsync(request);
            
            if (response.Success)
            {
                return response.ClusterInfo;
            }
            else
            {
                _logger.LogError("Failed to get cluster info: {ErrorMessage}", response.ErrorMessage);
                return null;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cluster info");
            return null;
        }
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _channel?.Dispose();
            _disposed = true;
        }
    }
}
