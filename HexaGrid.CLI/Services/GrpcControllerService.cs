using Grpc.Core;
using HexaGrid.CLI.Grpc;
using Microsoft.Extensions.Logging;

namespace HexaGrid.CLI.Services;

public class GrpcControllerService : HexaGridController.HexaGridControllerBase
{
    private readonly ILogger<GrpcControllerService> _logger;
    private readonly IClusterStateService _clusterState;
    private readonly IWorkflowExecutionService _workflowExecution;
    private readonly string _clusterToken;

    public GrpcControllerService(
        ILogger<GrpcControllerService> logger,
        IClusterStateService clusterState,
        IWorkflowExecutionService workflowExecution,
        string clusterToken)
    {
        _logger = logger;
        _clusterState = clusterState;
        _workflowExecution = workflowExecution;
        _clusterToken = clusterToken;
    }

    public override async Task<RegisterAgentResponse> RegisterAgent(
        RegisterAgentRequest request, ServerCallContext context)
    {
        try
        {
            _logger.LogInformation("Agent registration request from {NodeName}", request.NodeName);

            // Validate cluster token
            if (request.ClusterToken != _clusterToken)
            {
                _logger.LogWarning("Invalid cluster token from {NodeName}", request.NodeName);
                return new RegisterAgentResponse
                {
                    Success = false,
                    ErrorMessage = "Invalid cluster token"
                };
            }

            // Create agent info
            var agentId = Guid.NewGuid().ToString();
            var nodeInfo = new NodeInfo
            {
                Id = agentId,
                Name = request.NodeName,
                Role = NodeRole.Agent,
                Status = NodeStatus.Ready,
                Address = context.Peer,
                Port = 0, // gRPC doesn't expose port directly
                RegisteredAt = DateTime.UtcNow,
                LastSeen = DateTime.UtcNow,
                Version = request.AgentInfo.Version,
                Labels = request.AgentInfo.Labels.ToDictionary(kv => kv.Key, kv => kv.Value),
                Resources = new NodeResources
                {
                    CpuUsagePercent = request.AgentInfo.Resources.CpuUsagePercent,
                    MemoryUsedBytes = request.AgentInfo.Resources.MemoryUsedBytes,
                    MemoryTotalBytes = request.AgentInfo.Resources.MemoryBytes,
                    StorageUsedBytes = request.AgentInfo.Resources.StorageUsedBytes,
                    StorageTotalBytes = request.AgentInfo.Resources.StorageBytes
                }
            };

            // Register the agent
            await _clusterState.RegisterNodeAsync(nodeInfo);

            _logger.LogInformation("Agent {NodeName} ({AgentId}) registered successfully", 
                request.NodeName, agentId);

            return new RegisterAgentResponse
            {
                Success = true,
                AgentId = agentId,
                ClusterConfig = new ClusterConfig
                {
                    ClusterId = "hexagrid-cluster",
                    HeartbeatIntervalSeconds = 30,
                    WorkPollIntervalSeconds = 5
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error registering agent {NodeName}", request.NodeName);
            return new RegisterAgentResponse
            {
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }

    public override async Task<HeartbeatResponse> SendHeartbeat(
        HeartbeatRequest request, ServerCallContext context)
    {
        try
        {
            // Validate cluster token
            if (request.ClusterToken != _clusterToken)
            {
                return new HeartbeatResponse
                {
                    Success = false,
                    ErrorMessage = "Invalid cluster token"
                };
            }

            // Update agent status
            var nodeStatus = request.Status.State switch
            {
                AgentState.Ready => NodeStatus.Ready,
                AgentState.Busy => NodeStatus.Ready, // Still ready, just busy
                AgentState.Draining => NodeStatus.NotReady,
                AgentState.Offline => NodeStatus.Disconnected,
                _ => NodeStatus.Unknown
            };

            await _clusterState.UpdateNodeStatusAsync(request.AgentId, nodeStatus);

            _logger.LogDebug("Heartbeat received from agent {AgentId}", request.AgentId);

            return new HeartbeatResponse
            {
                Success = true
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing heartbeat from agent {AgentId}", request.AgentId);
            return new HeartbeatResponse
            {
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }

    public override async Task<UnregisterAgentResponse> UnregisterAgent(
        UnregisterAgentRequest request, ServerCallContext context)
    {
        try
        {
            // Validate cluster token
            if (request.ClusterToken != _clusterToken)
            {
                return new UnregisterAgentResponse
                {
                    Success = false,
                    ErrorMessage = "Invalid cluster token"
                };
            }

            await _clusterState.UnregisterNodeAsync(request.AgentId);
            _logger.LogInformation("Agent {AgentId} unregistered", request.AgentId);

            return new UnregisterAgentResponse
            {
                Success = true
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error unregistering agent {AgentId}", request.AgentId);
            return new UnregisterAgentResponse
            {
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }

    public override async Task<SubmitWorkflowResponse> SubmitWorkflow(
        SubmitWorkflowRequest request, ServerCallContext context)
    {
        try
        {
            // Validate cluster token
            if (request.ClusterToken != _clusterToken)
            {
                return new SubmitWorkflowResponse
                {
                    Success = false,
                    ErrorMessage = "Invalid cluster token"
                };
            }

            // Convert gRPC workflow to internal format
            var workflow = ConvertWorkflowFromGrpc(request.Workflow);
            
            // Submit workflow
            var workflowId = await _workflowExecution.SubmitWorkflowAsync(workflow, 
                new WorkflowSubmissionOptions
                {
                    Namespace = request.Options.Namespace,
                    StopOnFirstError = request.Options.StopOnFirstError,
                    Labels = request.Options.Labels.ToDictionary(kv => kv.Key, kv => kv.Value)
                });

            _logger.LogInformation("Workflow {WorkflowName} submitted with ID {WorkflowId}", 
                request.Workflow.Name, workflowId);

            return new SubmitWorkflowResponse
            {
                Success = true,
                WorkflowId = workflowId
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error submitting workflow {WorkflowName}", request.Workflow.Name);
            return new SubmitWorkflowResponse
            {
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }

    public override async Task<GetWorkflowStatusResponse> GetWorkflowStatus(
        GetWorkflowStatusRequest request, ServerCallContext context)
    {
        try
        {
            // Validate cluster token
            if (request.ClusterToken != _clusterToken)
            {
                return new GetWorkflowStatusResponse
                {
                    Success = false,
                    ErrorMessage = "Invalid cluster token"
                };
            }

            var status = await _workflowExecution.GetWorkflowStatusAsync(request.WorkflowId);
            var grpcStatus = ConvertWorkflowStatusToGrpc(status);

            return new GetWorkflowStatusResponse
            {
                Success = true,
                Status = grpcStatus
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting workflow status for {WorkflowId}", request.WorkflowId);
            return new GetWorkflowStatusResponse
            {
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }

    public override async Task<CancelWorkflowResponse> CancelWorkflow(
        CancelWorkflowRequest request, ServerCallContext context)
    {
        try
        {
            // Validate cluster token
            if (request.ClusterToken != _clusterToken)
            {
                return new CancelWorkflowResponse
                {
                    Success = false,
                    ErrorMessage = "Invalid cluster token"
                };
            }

            var cancelled = await _workflowExecution.CancelWorkflowAsync(request.WorkflowId);

            return new CancelWorkflowResponse
            {
                Success = cancelled
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling workflow {WorkflowId}", request.WorkflowId);
            return new CancelWorkflowResponse
            {
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }

    public override async Task<GetClusterInfoResponse> GetClusterInfo(
        GetClusterInfoRequest request, ServerCallContext context)
    {
        try
        {
            // Validate cluster token
            if (request.ClusterToken != _clusterToken)
            {
                return new GetClusterInfoResponse
                {
                    Success = false,
                    ErrorMessage = "Invalid cluster token"
                };
            }

            var clusterState = await _clusterState.GetClusterStateAsync();
            var nodes = await _clusterState.GetNodesAsync();

            var clusterInfo = new ClusterInfo
            {
                ClusterId = "hexagrid-cluster",
                Version = "1.0.0",
                TotalAgents = clusterState.TotalNodes,
                ReadyAgents = clusterState.ReadyNodes
            };

            return new GetClusterInfoResponse
            {
                Success = true,
                ClusterInfo = clusterInfo
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cluster info");
            return new GetClusterInfoResponse
            {
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }

    // Helper methods for conversion between gRPC and internal types
    private Core.Graph.NodeGraph ConvertWorkflowFromGrpc(WorkflowDefinition grpcWorkflow)
    {
        // TODO: Implement conversion from gRPC WorkflowDefinition to internal NodeGraph
        // This is a simplified version
        return new Core.Graph.NodeGraph(grpcWorkflow.Name)
        {
            Description = grpcWorkflow.Description
        };
    }

    private Grpc.WorkflowStatus ConvertWorkflowStatusToGrpc(WorkflowExecutionStatus internalStatus)
    {
        var grpcStatus = new Grpc.WorkflowStatus
        {
            WorkflowId = internalStatus.Id,
            Name = internalStatus.Name,
            State = internalStatus.Status switch
            {
                Services.WorkflowStatus.Submitted => WorkflowState.WorkflowSubmitted,
                Services.WorkflowStatus.Running => WorkflowState.WorkflowRunning,
                Services.WorkflowStatus.Completed => WorkflowState.WorkflowCompleted,
                Services.WorkflowStatus.Failed => WorkflowState.WorkflowFailed,
                Services.WorkflowStatus.Cancelled => WorkflowState.WorkflowCancelled,
                _ => WorkflowState.WorkflowUnknown
            },
            SubmittedAt = internalStatus.SubmittedAt.Ticks,
            TotalNodes = internalStatus.TotalNodes,
            CompletedNodes = internalStatus.CompletedNodes,
            FailedNodes = internalStatus.FailedNodes,
            ErrorMessage = internalStatus.ErrorMessage ?? ""
        };

        if (internalStatus.StartedAt.HasValue)
            grpcStatus.StartedAt = internalStatus.StartedAt.Value.Ticks;

        if (internalStatus.CompletedAt.HasValue)
            grpcStatus.CompletedAt = internalStatus.CompletedAt.Value.Ticks;

        return grpcStatus;
    }
}
