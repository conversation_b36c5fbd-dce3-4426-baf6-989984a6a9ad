using Microsoft.Extensions.Logging;

namespace HexaGrid.CLI.Services;

public interface IConfigurationService
{
    Task<HexaGridConfiguration> LoadConfigurationAsync(string? configPath = null);
    Task SaveConfigurationAsync(HexaGridConfiguration configuration, string? configPath = null);
    Task<string> GetDefaultConfigPathAsync();
}

public class ConfigurationService : IConfigurationService
{
    private readonly ILogger<ConfigurationService> _logger;

    public ConfigurationService(ILogger<ConfigurationService> logger)
    {
        _logger = logger;
    }

    public async Task<HexaGridConfiguration> LoadConfigurationAsync(string? configPath = null)
    {
        configPath ??= await GetDefaultConfigPathAsync();

        try
        {
            if (File.Exists(configPath))
            {
                var json = await File.ReadAllTextAsync(configPath);
                var config = System.Text.Json.JsonSerializer.Deserialize<HexaGridConfiguration>(json);
                
                if (config != null)
                {
                    _logger.LogInformation("Loaded configuration from {ConfigPath}", configPath);
                    return config;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to load configuration from {ConfigPath}, using defaults", configPath);
        }

        _logger.LogInformation("Using default configuration");
        return new HexaGridConfiguration();
    }

    public async Task SaveConfigurationAsync(HexaGridConfiguration configuration, string? configPath = null)
    {
        configPath ??= await GetDefaultConfigPathAsync();

        try
        {
            var directory = Path.GetDirectoryName(configPath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            var json = System.Text.Json.JsonSerializer.Serialize(configuration, new System.Text.Json.JsonSerializerOptions
            {
                WriteIndented = true
            });

            await File.WriteAllTextAsync(configPath, json);
            _logger.LogInformation("Saved configuration to {ConfigPath}", configPath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save configuration to {ConfigPath}", configPath);
            throw;
        }
    }

    public Task<string> GetDefaultConfigPathAsync()
    {
        var homeDirectory = Environment.GetFolderPath(Environment.SpecialFolder.UserProfile);
        var configPath = Path.Combine(homeDirectory, ".hexagrid", "config.json");
        return Task.FromResult(configPath);
    }
}

public class HexaGridConfiguration
{
    public ClusterConfiguration Cluster { get; set; } = new();
    public LoggingConfiguration Logging { get; set; } = new();
    public SecurityConfiguration Security { get; set; } = new();
    public Dictionary<string, string> DefaultLabels { get; set; } = new();
}

public class ClusterConfiguration
{
    public string DefaultServer { get; set; } = "localhost:6443";
    public string DefaultNamespace { get; set; } = "default";
    public int HeartbeatIntervalSeconds { get; set; } = 30;
    public int DiscoveryIntervalSeconds { get; set; } = 30;
    public int WorkPollIntervalSeconds { get; set; } = 5;
}

public class LoggingConfiguration
{
    public string Level { get; set; } = "Information";
    public bool EnableConsoleLogging { get; set; } = true;
    public bool EnableFileLogging { get; set; } = false;
    public string? LogFilePath { get; set; }
}

public class SecurityConfiguration
{
    public bool EnableTLS { get; set; } = true;
    public string? CertificatePath { get; set; }
    public string? PrivateKeyPath { get; set; }
    public string? CACertificatePath { get; set; }
    public bool SkipCertificateValidation { get; set; } = false;
}
