using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using HexaGrid.CLI.Commands;
using HexaGrid.CLI.Grpc;

namespace HexaGrid.CLI.Services;

public class AgentService : BackgroundService
{
    private readonly ILogger<AgentService> _logger;
    private readonly ILoggerFactory _loggerFactory;
    private readonly AgentOptions _options;
    private readonly IClusterStateService _clusterState;
    private readonly INodeDiscoveryService _nodeDiscovery;
    private readonly IWorkflowExecutionService _workflowExecution;
    private Timer? _heartbeatTimer;
    private IGrpcAgentClient? _grpcClient;
    private string? _agentId;

    public AgentService(
        ILogger<AgentService> logger,
        ILoggerFactory loggerFactory,
        IOptions<AgentOptions> options,
        IClusterStateService clusterState,
        INodeDiscoveryService nodeDiscovery,
        IWorkflowExecutionService workflowExecution)
    {
        _logger = logger;
        _loggerFactory = loggerFactory;
        _options = options.Value;
        _clusterState = clusterState;
        _nodeDiscovery = nodeDiscovery;
        _workflowExecution = workflowExecution;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("HexaGrid Agent {NodeName} starting, connecting to {ServerAddress}", 
            _options.NodeName, _options.ServerAddress);

        try
        {
            // Initialize data directory
            await InitializeDataDirectoryAsync();

            // Connect to controller
            await ConnectToControllerAsync();

            // Register agent node
            await RegisterAgentNodeAsync();

            // Start heartbeat
            StartHeartbeat();

            // Start node discovery (for peer-to-peer communication)
            await _nodeDiscovery.StartDiscoveryAsync(stoppingToken);

            // Start work polling
            await StartWorkPollingAsync(stoppingToken);

            _logger.LogInformation("HexaGrid Agent {NodeName} started successfully", _options.NodeName);

            // Keep running until cancellation
            await Task.Delay(Timeout.Infinite, stoppingToken);
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("HexaGrid Agent {NodeName} is shutting down", _options.NodeName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "HexaGrid Agent {NodeName} failed to start", _options.NodeName);
            throw;
        }
        finally
        {
            await CleanupAsync();
        }
    }

    private async Task InitializeDataDirectoryAsync()
    {
        try
        {
            if (!Directory.Exists(_options.DataDirectory))
            {
                Directory.CreateDirectory(_options.DataDirectory);
                _logger.LogInformation("Created data directory: {DataDirectory}", _options.DataDirectory);
            }

            // Initialize local storage for node data
            var nodeDataPath = Path.Combine(_options.DataDirectory, "node.json");
            _logger.LogInformation("Using node data file: {NodeDataPath}", nodeDataPath);

            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize data directory");
            throw;
        }
    }

    private async Task ConnectToControllerAsync()
    {
        try
        {
            _logger.LogInformation("Connecting to controller at {ServerAddress}", _options.ServerAddress);

            // Create gRPC client
            _grpcClient = new GrpcAgentClient(_loggerFactory.CreateLogger<GrpcAgentClient>(), _options.ServerAddress);

            // Register with controller
            var agentInfo = new AgentInfo
            {
                Version = "1.0.0",
                Platform = Environment.OSVersion.Platform.ToString(),
                Architecture = System.Runtime.InteropServices.RuntimeInformation.ProcessArchitecture.ToString(),
                Resources = await CreateResourceInfoAsync()
            };

            // Add labels
            foreach (var label in _options.Labels)
            {
                agentInfo.Labels.Add(label.Key, label.Value);
            }

            var registered = await _grpcClient.RegisterAsync(_options.NodeName, _options.ClusterToken, agentInfo);
            if (!registered)
            {
                throw new InvalidOperationException("Failed to register with controller");
            }

            // Store agent ID (in a real implementation, this would be returned from registration)
            _agentId = _options.NodeName; // Simplified for now

            _logger.LogInformation("Successfully connected and registered with controller");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to connect to controller");
            throw;
        }
    }

    private async Task RegisterAgentNodeAsync()
    {
        var agentNode = new NodeInfo
        {
            Id = _options.NodeName,
            Name = _options.NodeName,
            Role = NodeRole.Agent,
            Status = NodeStatus.Ready,
            Address = GetLocalIPAddress(),
            Port = 0, // Agent doesn't listen on a specific port
            RegisteredAt = DateTime.UtcNow,
            LastSeen = DateTime.UtcNow,
            Version = "1.0.0",
            Labels = _options.Labels,
            Resources = await GetNodeResourcesAsync()
        };

        await _clusterState.RegisterNodeAsync(agentNode);
        _logger.LogInformation("Registered agent node: {NodeName}", agentNode.Name);

        // Announce to discovery service
        await _nodeDiscovery.AnnounceNodeAsync(new NodeAnnouncement
        {
            NodeId = agentNode.Id,
            NodeName = agentNode.Name,
            Address = agentNode.Address,
            Port = agentNode.Port,
            Role = agentNode.Role,
            Labels = agentNode.Labels,
            Version = agentNode.Version
        });
    }

    private void StartHeartbeat()
    {
        _heartbeatTimer = new Timer(async _ => await SendHeartbeatAsync(), 
            null, TimeSpan.Zero, TimeSpan.FromSeconds(30));
    }

    private async Task SendHeartbeatAsync()
    {
        try
        {
            if (_grpcClient == null || _agentId == null)
            {
                _logger.LogWarning("Cannot send heartbeat - not connected to controller");
                return;
            }

            _logger.LogDebug("Sending heartbeat to controller");

            // Create agent status
            var status = new AgentStatus
            {
                State = AgentState.Ready,
                ActiveNodes = 0, // TODO: Track actual active nodes
                LastActivity = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                Version = "1.0.0"
            };

            // Get current resource info
            var resources = await CreateResourceInfoAsync();

            // Send heartbeat
            var success = await _grpcClient.SendHeartbeatAsync(_agentId, _options.ClusterToken, status, resources);

            if (!success)
            {
                _logger.LogWarning("Heartbeat failed - may need to reconnect");
            }

            // Update local node status
            await _clusterState.UpdateNodeStatusAsync(_options.NodeName, NodeStatus.Ready);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send heartbeat");
        }
    }

    private async Task StartWorkPollingAsync(CancellationToken cancellationToken)
    {
        _ = Task.Run(async () =>
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                try
                {
                    await PollForWorkAsync();
                    await Task.Delay(TimeSpan.FromSeconds(5), cancellationToken);
                }
                catch (OperationCanceledException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error during work polling");
                }
            }
        }, cancellationToken);

        await Task.CompletedTask;
    }

    private async Task PollForWorkAsync()
    {
        _logger.LogDebug("Polling for work from controller");

        // TODO: Implement actual work polling from controller
        // This would include:
        // - Request available work items
        // - Receive node execution tasks
        // - Execute nodes locally
        // - Report results back to controller

        await Task.CompletedTask;
    }

    private string GetLocalIPAddress()
    {
        try
        {
            var host = System.Net.Dns.GetHostEntry(System.Net.Dns.GetHostName());
            var localIP = host.AddressList
                .FirstOrDefault(ip => ip.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork 
                                     && !System.Net.IPAddress.IsLoopback(ip));
            
            return localIP?.ToString() ?? "127.0.0.1";
        }
        catch
        {
            return "127.0.0.1";
        }
    }

    private async Task<NodeResources> GetNodeResourcesAsync()
    {
        try
        {
            // TODO: Implement actual resource monitoring
            // This could use:
            // - System.Diagnostics.PerformanceCounter (Windows)
            // - /proc filesystem (Linux)
            // - Cross-platform libraries like Hardware.Info

            await Task.CompletedTask;

            return new NodeResources
            {
                CpuUsagePercent = Random.Shared.NextDouble() * 100,
                MemoryUsedBytes = Random.Shared.NextInt64(100L * 1024 * 1024, 500L * 1024 * 1024), // 100-500MB
                MemoryTotalBytes = 1024L * 1024 * 1024, // 1GB
                StorageUsedBytes = Random.Shared.NextInt64(1024L * 1024 * 1024, 5L * 1024 * 1024 * 1024), // 1-5GB
                StorageTotalBytes = 20L * 1024 * 1024 * 1024 // 20GB
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get node resources");
            return new NodeResources();
        }
    }

    private async Task<ResourceInfo> CreateResourceInfoAsync()
    {
        var nodeResources = await GetNodeResourcesAsync();

        return new ResourceInfo
        {
            CpuCores = Environment.ProcessorCount,
            MemoryBytes = nodeResources.MemoryTotalBytes,
            StorageBytes = nodeResources.StorageTotalBytes,
            CpuUsagePercent = nodeResources.CpuUsagePercent,
            MemoryUsedBytes = nodeResources.MemoryUsedBytes,
            StorageUsedBytes = nodeResources.StorageUsedBytes
        };
    }

    private async Task CleanupAsync()
    {
        try
        {
            _heartbeatTimer?.Dispose();
            await _nodeDiscovery.StopDiscoveryAsync();

            // Unregister from controller via gRPC
            if (_grpcClient != null && _agentId != null)
            {
                await _grpcClient.UnregisterAsync(_agentId, _options.ClusterToken);
                _grpcClient.Dispose();
            }

            // Unregister from local cluster state
            await _clusterState.UnregisterNodeAsync(_options.NodeName);

            _logger.LogInformation("HexaGrid Agent {NodeName} stopped", _options.NodeName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during agent cleanup");
        }
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("HexaGrid Agent {NodeName} is stopping...", _options.NodeName);
        await base.StopAsync(cancellationToken);
    }
}
