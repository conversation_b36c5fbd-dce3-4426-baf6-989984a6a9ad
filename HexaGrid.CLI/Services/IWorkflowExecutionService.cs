using HexaGrid.Core.Graph;
using Microsoft.Extensions.Logging;

namespace HexaGrid.CLI.Services;

public interface IWorkflowExecutionService
{
    Task<string> SubmitWorkflowAsync(NodeGraph workflow, WorkflowSubmissionOptions? options = null);
    Task<WorkflowExecutionStatus> GetWorkflowStatusAsync(string workflowId);
    Task<IEnumerable<WorkflowExecutionStatus>> GetWorkflowsAsync(string? namespaceFilter = null);
    Task<bool> CancelWorkflowAsync(string workflowId);
    Task<WorkflowExecutionResult> WaitForCompletionAsync(string workflowId, TimeSpan? timeout = null);
    event EventHandler<WorkflowStatusChangedEventArgs>? WorkflowStatusChanged;
}

public class WorkflowExecutionService : IWorkflowExecutionService
{
    private readonly ILogger<WorkflowExecutionService> _logger;
    private readonly IClusterStateService _clusterState;
    private readonly Dictionary<string, WorkflowExecutionStatus> _workflows = new();
    private readonly object _lock = new();

    public event EventHandler<WorkflowStatusChangedEventArgs>? WorkflowStatusChanged;

    public WorkflowExecutionService(ILogger<WorkflowExecutionService> logger, IClusterStateService clusterState)
    {
        _logger = logger;
        _clusterState = clusterState;
    }

    public Task<string> SubmitWorkflowAsync(NodeGraph workflow, WorkflowSubmissionOptions? options = null)
    {
        options ??= new WorkflowSubmissionOptions();
        var workflowId = Guid.NewGuid().ToString();

        var status = new WorkflowExecutionStatus
        {
            Id = workflowId,
            Name = workflow.Name,
            Namespace = options.Namespace,
            Status = WorkflowStatus.Submitted,
            SubmittedAt = DateTime.UtcNow,
            TotalNodes = workflow.Nodes.Count,
            CompletedNodes = 0,
            FailedNodes = 0,
            NodeStatuses = workflow.Nodes.ToDictionary(n => n.Id, n => NodeExecutionStatus.Pending)
        };

        lock (_lock)
        {
            _workflows[workflowId] = status;
        }

        _logger.LogInformation("Workflow {WorkflowName} ({WorkflowId}) submitted to namespace {Namespace}",
            workflow.Name, workflowId, options.Namespace);

        // Start execution asynchronously
        _ = Task.Run(async () => await ExecuteWorkflowAsync(workflowId, workflow, options));

        return Task.FromResult(workflowId);
    }

    public Task<WorkflowExecutionStatus> GetWorkflowStatusAsync(string workflowId)
    {
        lock (_lock)
        {
            if (_workflows.TryGetValue(workflowId, out var status))
            {
                return Task.FromResult(status);
            }
        }

        throw new ArgumentException($"Workflow {workflowId} not found");
    }

    public Task<IEnumerable<WorkflowExecutionStatus>> GetWorkflowsAsync(string? namespaceFilter = null)
    {
        lock (_lock)
        {
            var workflows = _workflows.Values.AsEnumerable();
            
            if (!string.IsNullOrEmpty(namespaceFilter))
            {
                workflows = workflows.Where(w => w.Namespace == namespaceFilter);
            }

            return Task.FromResult(workflows);
        }
    }

    public Task<bool> CancelWorkflowAsync(string workflowId)
    {
        lock (_lock)
        {
            if (_workflows.TryGetValue(workflowId, out var status))
            {
                if (status.Status == WorkflowStatus.Running || status.Status == WorkflowStatus.Submitted)
                {
                    status.Status = WorkflowStatus.Cancelled;
                    status.CompletedAt = DateTime.UtcNow;
                    
                    _logger.LogInformation("Workflow {WorkflowId} cancelled", workflowId);
                    WorkflowStatusChanged?.Invoke(this, new WorkflowStatusChangedEventArgs(status));
                    
                    return Task.FromResult(true);
                }
            }
        }

        return Task.FromResult(false);
    }

    public async Task<WorkflowExecutionResult> WaitForCompletionAsync(string workflowId, TimeSpan? timeout = null)
    {
        timeout ??= TimeSpan.FromMinutes(30);
        var startTime = DateTime.UtcNow;

        while (DateTime.UtcNow - startTime < timeout)
        {
            var status = await GetWorkflowStatusAsync(workflowId);
            
            if (status.Status == WorkflowStatus.Completed)
            {
                return new WorkflowExecutionResult
                {
                    Success = true,
                    WorkflowId = workflowId,
                    Status = status
                };
            }
            
            if (status.Status == WorkflowStatus.Failed || status.Status == WorkflowStatus.Cancelled)
            {
                return new WorkflowExecutionResult
                {
                    Success = false,
                    WorkflowId = workflowId,
                    Status = status,
                    ErrorMessage = status.ErrorMessage
                };
            }

            await Task.Delay(1000);
        }

        return new WorkflowExecutionResult
        {
            Success = false,
            WorkflowId = workflowId,
            ErrorMessage = "Timeout waiting for workflow completion"
        };
    }

    private async Task ExecuteWorkflowAsync(string workflowId, NodeGraph workflow, WorkflowSubmissionOptions options)
    {
        try
        {
            _logger.LogInformation("Starting execution of workflow {WorkflowId}", workflowId);

            // Update status to running
            UpdateWorkflowStatus(workflowId, status =>
            {
                status.Status = WorkflowStatus.Running;
                status.StartedAt = DateTime.UtcNow;
            });

            // TODO: Implement actual distributed execution
            // For now, simulate execution
            var executionOrder = workflow.GetExecutionOrder();
            
            foreach (var node in executionOrder)
            {
                // Check if workflow was cancelled
                var currentStatus = await GetWorkflowStatusAsync(workflowId);
                if (currentStatus.Status == WorkflowStatus.Cancelled)
                {
                    break;
                }

                _logger.LogDebug("Executing node {NodeId} in workflow {WorkflowId}", node.Id, workflowId);

                // Update node status to running
                UpdateWorkflowStatus(workflowId, status =>
                {
                    status.NodeStatuses[node.Id] = NodeExecutionStatus.Running;
                });

                // Simulate node execution
                await Task.Delay(Random.Shared.Next(1000, 3000));

                // Simulate occasional failures
                var success = Random.Shared.NextDouble() > 0.1; // 90% success rate

                UpdateWorkflowStatus(workflowId, status =>
                {
                    if (success)
                    {
                        status.NodeStatuses[node.Id] = NodeExecutionStatus.Completed;
                        status.CompletedNodes++;
                    }
                    else
                    {
                        status.NodeStatuses[node.Id] = NodeExecutionStatus.Failed;
                        status.FailedNodes++;
                    }
                });

                if (!success && options.StopOnFirstError)
                {
                    UpdateWorkflowStatus(workflowId, status =>
                    {
                        status.Status = WorkflowStatus.Failed;
                        status.CompletedAt = DateTime.UtcNow;
                        status.ErrorMessage = $"Node {node.Id} failed execution";
                    });
                    return;
                }
            }

            // Complete workflow
            UpdateWorkflowStatus(workflowId, status =>
            {
                status.Status = status.FailedNodes > 0 ? WorkflowStatus.Failed : WorkflowStatus.Completed;
                status.CompletedAt = DateTime.UtcNow;
            });

            _logger.LogInformation("Workflow {WorkflowId} execution completed", workflowId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing workflow {WorkflowId}", workflowId);

            UpdateWorkflowStatus(workflowId, status =>
            {
                status.Status = WorkflowStatus.Failed;
                status.CompletedAt = DateTime.UtcNow;
                status.ErrorMessage = ex.Message;
            });
        }
    }

    private void UpdateWorkflowStatus(string workflowId, Action<WorkflowExecutionStatus> updateAction)
    {
        lock (_lock)
        {
            if (_workflows.TryGetValue(workflowId, out var status))
            {
                updateAction(status);
                WorkflowStatusChanged?.Invoke(this, new WorkflowStatusChangedEventArgs(status));
            }
        }
    }
}

public class WorkflowSubmissionOptions
{
    public string Namespace { get; set; } = "default";
    public bool StopOnFirstError { get; set; } = true;
    public Dictionary<string, string> Labels { get; set; } = new();
    public TimeSpan? Timeout { get; set; }
}

public class WorkflowExecutionStatus
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Namespace { get; set; } = "default";
    public WorkflowStatus Status { get; set; }
    public DateTime SubmittedAt { get; set; }
    public DateTime? StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public int TotalNodes { get; set; }
    public int CompletedNodes { get; set; }
    public int FailedNodes { get; set; }
    public string? ErrorMessage { get; set; }
    public Dictionary<string, NodeExecutionStatus> NodeStatuses { get; set; } = new();
}

public class WorkflowExecutionResult
{
    public bool Success { get; set; }
    public string WorkflowId { get; set; } = string.Empty;
    public WorkflowExecutionStatus? Status { get; set; }
    public string? ErrorMessage { get; set; }
}

public class WorkflowStatusChangedEventArgs : EventArgs
{
    public WorkflowExecutionStatus Status { get; }

    public WorkflowStatusChangedEventArgs(WorkflowExecutionStatus status)
    {
        Status = status;
    }
}

public enum WorkflowStatus
{
    Submitted,
    Running,
    Completed,
    Failed,
    Cancelled
}

public enum NodeExecutionStatus
{
    Pending,
    Running,
    Completed,
    Failed,
    Cancelled
}
