namespace HexaGrid.CLI.Services;

public interface IClusterStateService
{
    Task<ClusterState> GetClusterStateAsync();
    Task UpdateNodeStatusAsync(string nodeId, NodeStatus status);
    Task RegisterNodeAsync(NodeInfo nodeInfo);
    Task UnregisterNodeAsync(string nodeId);
    Task<IEnumerable<NodeInfo>> GetNodesAsync();
    Task<NodeInfo?> GetNodeAsync(string nodeId);
}

public class ClusterStateService : IClusterStateService
{
    private readonly Dictionary<string, NodeInfo> _nodes = new();
    private readonly object _lock = new();

    public Task<ClusterState> GetClusterStateAsync()
    {
        lock (_lock)
        {
            var state = new ClusterState
            {
                TotalNodes = _nodes.Count,
                ReadyNodes = _nodes.Values.Count(n => n.Status == NodeStatus.Ready),
                NotReadyNodes = _nodes.Values.Count(n => n.Status != NodeStatus.Ready),
                LastUpdated = DateTime.UtcNow
            };

            return Task.FromResult(state);
        }
    }

    public Task UpdateNodeStatusAsync(string nodeId, NodeStatus status)
    {
        lock (_lock)
        {
            if (_nodes.TryGetValue(nodeId, out var node))
            {
                node.Status = status;
                node.LastSeen = DateTime.UtcNow;
            }
        }

        return Task.CompletedTask;
    }

    public Task RegisterNodeAsync(NodeInfo nodeInfo)
    {
        lock (_lock)
        {
            _nodes[nodeInfo.Id] = nodeInfo;
        }

        return Task.CompletedTask;
    }

    public Task UnregisterNodeAsync(string nodeId)
    {
        lock (_lock)
        {
            _nodes.Remove(nodeId);
        }

        return Task.CompletedTask;
    }

    public Task<IEnumerable<NodeInfo>> GetNodesAsync()
    {
        lock (_lock)
        {
            return Task.FromResult(_nodes.Values.AsEnumerable());
        }
    }

    public Task<NodeInfo?> GetNodeAsync(string nodeId)
    {
        lock (_lock)
        {
            _nodes.TryGetValue(nodeId, out var node);
            return Task.FromResult(node);
        }
    }
}

public class ClusterState
{
    public int TotalNodes { get; set; }
    public int ReadyNodes { get; set; }
    public int NotReadyNodes { get; set; }
    public DateTime LastUpdated { get; set; }
}

public class NodeInfo
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public NodeRole Role { get; set; }
    public NodeStatus Status { get; set; }
    public string Address { get; set; } = string.Empty;
    public int Port { get; set; }
    public Dictionary<string, string> Labels { get; set; } = new();
    public NodeResources Resources { get; set; } = new();
    public DateTime RegisteredAt { get; set; }
    public DateTime LastSeen { get; set; }
    public string Version { get; set; } = string.Empty;
}

public class NodeResources
{
    public double CpuUsagePercent { get; set; }
    public long MemoryUsedBytes { get; set; }
    public long MemoryTotalBytes { get; set; }
    public long StorageUsedBytes { get; set; }
    public long StorageTotalBytes { get; set; }
}

public enum NodeRole
{
    Controller,
    Agent
}

public enum NodeStatus
{
    Unknown,
    Ready,
    NotReady,
    Disconnected
}
