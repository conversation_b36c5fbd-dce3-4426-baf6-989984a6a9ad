using Microsoft.Extensions.Logging;

namespace HexaGrid.CLI.Services;

public interface INodeDiscoveryService
{
    Task StartDiscoveryAsync(CancellationToken cancellationToken = default);
    Task StopDiscoveryAsync();
    Task<IEnumerable<DiscoveredNode>> DiscoverNodesAsync();
    Task AnnounceNodeAsync(NodeAnnouncement announcement);
    event EventHandler<NodeDiscoveredEventArgs>? NodeDiscovered;
    event EventHandler<NodeLostEventArgs>? NodeLost;
}

public class NodeDiscoveryService : INodeDiscoveryService
{
    private readonly ILogger<NodeDiscoveryService> _logger;
    private readonly Dictionary<string, DiscoveredNode> _discoveredNodes = new();
    private readonly object _lock = new();
    private Timer? _discoveryTimer;
    private Timer? _heartbeatTimer;

    public event EventHandler<NodeDiscoveredEventArgs>? NodeDiscovered;
    public event EventHandler<NodeLostEventArgs>? NodeLost;

    public NodeDiscoveryService(ILogger<NodeDiscoveryService> logger)
    {
        _logger = logger;
    }

    public Task StartDiscoveryAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting node discovery service");

        // Start discovery timer (every 30 seconds)
        _discoveryTimer = new Timer(async _ => await PerformDiscoveryAsync(), 
            null, TimeSpan.Zero, TimeSpan.FromSeconds(30));

        // Start heartbeat cleanup timer (every 60 seconds)
        _heartbeatTimer = new Timer(async _ => await CleanupStaleNodesAsync(), 
            null, TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));

        return Task.CompletedTask;
    }

    public Task StopDiscoveryAsync()
    {
        _logger.LogInformation("Stopping node discovery service");

        _discoveryTimer?.Dispose();
        _heartbeatTimer?.Dispose();

        return Task.CompletedTask;
    }

    public Task<IEnumerable<DiscoveredNode>> DiscoverNodesAsync()
    {
        lock (_lock)
        {
            return Task.FromResult(_discoveredNodes.Values.AsEnumerable());
        }
    }

    public Task AnnounceNodeAsync(NodeAnnouncement announcement)
    {
        lock (_lock)
        {
            var node = new DiscoveredNode
            {
                Id = announcement.NodeId,
                Name = announcement.NodeName,
                Address = announcement.Address,
                Port = announcement.Port,
                Role = announcement.Role,
                Labels = announcement.Labels,
                LastSeen = DateTime.UtcNow,
                Version = announcement.Version
            };

            var isNewNode = !_discoveredNodes.ContainsKey(announcement.NodeId);
            _discoveredNodes[announcement.NodeId] = node;

            if (isNewNode)
            {
                _logger.LogInformation("Discovered new node: {NodeName} ({NodeId}) at {Address}:{Port}",
                    announcement.NodeName, announcement.NodeId, announcement.Address, announcement.Port);

                NodeDiscovered?.Invoke(this, new NodeDiscoveredEventArgs(node));
            }
        }

        return Task.CompletedTask;
    }

    private async Task PerformDiscoveryAsync()
    {
        try
        {
            _logger.LogDebug("Performing node discovery scan");

            // TODO: Implement actual network discovery
            // This could use:
            // - Multicast DNS (mDNS)
            // - UDP broadcast
            // - Consul/etcd service discovery
            // - Static configuration

            await Task.Delay(100); // Simulate discovery work
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during node discovery");
        }
    }

    private async Task CleanupStaleNodesAsync()
    {
        try
        {
            var staleThreshold = DateTime.UtcNow.AddMinutes(-5); // 5 minutes
            var staleNodes = new List<DiscoveredNode>();

            lock (_lock)
            {
                var nodesToRemove = _discoveredNodes.Values
                    .Where(n => n.LastSeen < staleThreshold)
                    .ToList();

                foreach (var node in nodesToRemove)
                {
                    _discoveredNodes.Remove(node.Id);
                    staleNodes.Add(node);
                }
            }

            foreach (var node in staleNodes)
            {
                _logger.LogWarning("Node {NodeName} ({NodeId}) is no longer responding - removing from discovery",
                    node.Name, node.Id);

                NodeLost?.Invoke(this, new NodeLostEventArgs(node));
            }

            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during stale node cleanup");
        }
    }
}

public class DiscoveredNode
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Address { get; set; } = string.Empty;
    public int Port { get; set; }
    public NodeRole Role { get; set; }
    public Dictionary<string, string> Labels { get; set; } = new();
    public DateTime LastSeen { get; set; }
    public string Version { get; set; } = string.Empty;
}

public class NodeAnnouncement
{
    public string NodeId { get; set; } = string.Empty;
    public string NodeName { get; set; } = string.Empty;
    public string Address { get; set; } = string.Empty;
    public int Port { get; set; }
    public NodeRole Role { get; set; }
    public Dictionary<string, string> Labels { get; set; } = new();
    public string Version { get; set; } = string.Empty;
}

public class NodeDiscoveredEventArgs : EventArgs
{
    public DiscoveredNode Node { get; }

    public NodeDiscoveredEventArgs(DiscoveredNode node)
    {
        Node = node;
    }
}

public class NodeLostEventArgs : EventArgs
{
    public DiscoveredNode Node { get; }

    public NodeLostEventArgs(DiscoveredNode node)
    {
        Node = node;
    }
}
