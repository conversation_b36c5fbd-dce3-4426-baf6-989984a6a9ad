{"format": 1, "restore": {"/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/HexaGrid.CLI.csproj": {}}, "projects": {"/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/HexaGrid.CLI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/HexaGrid.CLI.csproj", "projectName": "hexagrid", "projectPath": "/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/HexaGrid.CLI.csproj", "packagesPath": "/home/<USER>/.nuget/packages/", "outputPath": "/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/home/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.Core/HexaGrid.Core.csproj": {"projectPath": "/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.Core/HexaGrid.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Grpc.AspNetCore": {"target": "Package", "version": "[2.70.0, )"}, "Grpc.Net.Client": {"target": "Package", "version": "[2.70.0, )"}, "Grpc.Tools": {"suppressParent": "All", "target": "Package", "version": "[2.70.0, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[9.0.7, )", "autoReferenced": true}, "System.CommandLine": {"target": "Package", "version": "[2.0.0-beta4.22272.1, )"}, "System.Text.Json": {"target": "Package", "version": "[9.0.7, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/share/dotnet/sdk/9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.Core/HexaGrid.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.Core/HexaGrid.Core.csproj", "projectName": "HexaGrid.Core", "projectPath": "/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.Core/HexaGrid.Core.csproj", "packagesPath": "/home/<USER>/.nuget/packages/", "outputPath": "/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.Core/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/home/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/share/dotnet/sdk/9.0.302/PortableRuntimeIdentifierGraph.json"}}}}}