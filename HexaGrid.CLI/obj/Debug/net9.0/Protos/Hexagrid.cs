// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: Protos/hexagrid.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace HexaGrid.CLI.Grpc {

  /// <summary>Holder for reflection information generated from Protos/hexagrid.proto</summary>
  public static partial class HexagridReflection {

    #region Descriptor
    /// <summary>File descriptor for Protos/hexagrid.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static HexagridReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "ChVQcm90b3MvaGV4YWdyaWQucHJvdG8SCGhleGFncmlkImkKFFJlZ2lzdGVy",
            "QWdlbnRSZXF1ZXN0EhEKCW5vZGVfbmFtZRgBIAEoCRIVCg1jbHVzdGVyX3Rv",
            "a2VuGAIgASgJEicKCmFnZW50X2luZm8YAyABKAsyEy5oZXhhZ3JpZC5BZ2Vu",
            "dEluZm8iggEKFVJlZ2lzdGVyQWdlbnRSZXNwb25zZRIPCgdzdWNjZXNzGAEg",
            "ASgIEhUKDWVycm9yX21lc3NhZ2UYAiABKAkSEAoIYWdlbnRfaWQYAyABKAkS",
            "LwoOY2x1c3Rlcl9jb25maWcYBCABKAsyFy5oZXhhZ3JpZC5DbHVzdGVyQ29u",
            "ZmlnIs8BCglBZ2VudEluZm8SDwoHdmVyc2lvbhgBIAEoCRIQCghwbGF0Zm9y",
            "bRgCIAEoCRIUCgxhcmNoaXRlY3R1cmUYAyABKAkSLwoGbGFiZWxzGAQgAygL",
            "Mh8uaGV4YWdyaWQuQWdlbnRJbmZvLkxhYmVsc0VudHJ5EikKCXJlc291cmNl",
            "cxgFIAEoCzIWLmhleGFncmlkLlJlc291cmNlSW5mbxotCgtMYWJlbHNFbnRy",
            "eRILCgNrZXkYASABKAkSDQoFdmFsdWUYAiABKAk6AjgBIqABCgxSZXNvdXJj",
            "ZUluZm8SEQoJY3B1X2NvcmVzGAEgASgBEhQKDG1lbW9yeV9ieXRlcxgCIAEo",
            "AxIVCg1zdG9yYWdlX2J5dGVzGAMgASgDEhkKEWNwdV91c2FnZV9wZXJjZW50",
            "GAQgASgBEhkKEW1lbW9yeV91c2VkX2J5dGVzGAUgASgDEhoKEnN0b3JhZ2Vf",
            "dXNlZF9ieXRlcxgGIAEoAyKJAQoNQ2x1c3RlckNvbmZpZxISCgpjbHVzdGVy",
            "X2lkGAEgASgJEhwKFGNvbnRyb2xsZXJfZW5kcG9pbnRzGAIgAygJEiIKGmhl",
            "YXJ0YmVhdF9pbnRlcnZhbF9zZWNvbmRzGAMgASgFEiIKGndvcmtfcG9sbF9p",
            "bnRlcnZhbF9zZWNvbmRzGAQgASgFIo0BChBIZWFydGJlYXRSZXF1ZXN0EhAK",
            "CGFnZW50X2lkGAEgASgJEhUKDWNsdXN0ZXJfdG9rZW4YAiABKAkSJQoGc3Rh",
            "dHVzGAMgASgLMhUuaGV4YWdyaWQuQWdlbnRTdGF0dXMSKQoJcmVzb3VyY2Vz",
            "GAQgASgLMhYuaGV4YWdyaWQuUmVzb3VyY2VJbmZvIlUKEUhlYXJ0YmVhdFJl",
            "c3BvbnNlEg8KB3N1Y2Nlc3MYASABKAgSFQoNZXJyb3JfbWVzc2FnZRgCIAEo",
            "CRIYChBwZW5kaW5nX2NvbW1hbmRzGAMgAygJInAKC0FnZW50U3RhdHVzEiMK",
            "BXN0YXRlGAEgASgOMhQuaGV4YWdyaWQuQWdlbnRTdGF0ZRIUCgxhY3RpdmVf",
            "bm9kZXMYAiABKAUSFQoNbGFzdF9hY3Rpdml0eRgDIAEoAxIPCgd2ZXJzaW9u",
            "GAQgASgJIkEKFlVucmVnaXN0ZXJBZ2VudFJlcXVlc3QSEAoIYWdlbnRfaWQY",
            "ASABKAkSFQoNY2x1c3Rlcl90b2tlbhgCIAEoCSJBChdVbnJlZ2lzdGVyQWdl",
            "bnRSZXNwb25zZRIPCgdzdWNjZXNzGAEgASgIEhUKDWVycm9yX21lc3NhZ2UY",
            "AiABKAkiigEKFVN1Ym1pdFdvcmtmbG93UmVxdWVzdBIVCg1jbHVzdGVyX3Rv",
            "a2VuGAEgASgJEi4KCHdvcmtmbG93GAIgASgLMhwuaGV4YWdyaWQuV29ya2Zs",
            "b3dEZWZpbml0aW9uEioKB29wdGlvbnMYAyABKAsyGS5oZXhhZ3JpZC5Xb3Jr",
            "Zmxvd09wdGlvbnMiVQoWU3VibWl0V29ya2Zsb3dSZXNwb25zZRIPCgdzdWNj",
            "ZXNzGAEgASgIEhUKDWVycm9yX21lc3NhZ2UYAiABKAkSEwoLd29ya2Zsb3df",
            "aWQYAyABKAkihwIKEldvcmtmbG93RGVmaW5pdGlvbhIMCgRuYW1lGAEgASgJ",
            "EhMKC2Rlc2NyaXB0aW9uGAIgASgJEicKBW5vZGVzGAMgAygLMhguaGV4YWdy",
            "aWQuTm9kZURlZmluaXRpb24SMwoLY29ubmVjdGlvbnMYBCADKAsyHi5oZXhh",
            "Z3JpZC5Db25uZWN0aW9uRGVmaW5pdGlvbhI+Cgl2YXJpYWJsZXMYBSADKAsy",
            "Ky5oZXhhZ3JpZC5Xb3JrZmxvd0RlZmluaXRpb24uVmFyaWFibGVzRW50cnka",
            "MAoOVmFyaWFibGVzRW50cnkSCwoDa2V5GAEgASgJEg0KBXZhbHVlGAIgASgJ",
            "OgI4ASKnAgoOTm9kZURlZmluaXRpb24SCgoCaWQYASABKAkSDAoEdHlwZRgC",
            "IAEoCRI0CgZpbnB1dHMYAyADKAsyJC5oZXhhZ3JpZC5Ob2RlRGVmaW5pdGlv",
            "bi5JbnB1dHNFbnRyeRI2CgdvdXRwdXRzGAQgAygLMiUuaGV4YWdyaWQuTm9k",
            "ZURlZmluaXRpb24uT3V0cHV0c0VudHJ5Ei4KC2NvbnN0cmFpbnRzGAUgASgL",
            "MhkuaGV4YWdyaWQuTm9kZUNvbnN0cmFpbnRzGi0KC0lucHV0c0VudHJ5EgsK",
            "A2tleRgBIAEoCRINCgV2YWx1ZRgCIAEoCToCOAEaLgoMT3V0cHV0c0VudHJ5",
            "EgsKA2tleRgBIAEoCRINCgV2YWx1ZRgCIAEoCToCOAEidwoUQ29ubmVjdGlv",
            "bkRlZmluaXRpb24SFAoMZnJvbV9ub2RlX2lkGAEgASgJEhEKCWZyb21fcG9y",
            "dBgCIAEoCRISCgp0b19ub2RlX2lkGAMgASgJEg8KB3RvX3BvcnQYBCABKAkS",
            "EQoJY29uZGl0aW9uGAUgASgJIncKD05vZGVDb25zdHJhaW50cxIXCg9yZXF1",
            "aXJlZF9sYWJlbHMYASADKAkSGAoQcHJlZmVycmVkX2xhYmVscxgCIAMoCRIx",
            "CglyZXNvdXJjZXMYAyABKAsyHi5oZXhhZ3JpZC5SZXNvdXJjZVJlcXVpcmVt",
            "ZW50cyJWChRSZXNvdXJjZVJlcXVpcmVtZW50cxIRCgljcHVfY29yZXMYASAB",
            "KAESFAoMbWVtb3J5X2J5dGVzGAIgASgDEhUKDXN0b3JhZ2VfYnl0ZXMYAyAB",
            "KAMi1QEKD1dvcmtmbG93T3B0aW9ucxIRCgluYW1lc3BhY2UYASABKAkSGwoT",
            "c3RvcF9vbl9maXJzdF9lcnJvchgCIAEoCBIXCg90aW1lb3V0X3NlY29uZHMY",
            "AyABKAUSEwoLcmV0cnlfY291bnQYBCABKAUSNQoGbGFiZWxzGAUgAygLMiUu",
            "aGV4YWdyaWQuV29ya2Zsb3dPcHRpb25zLkxhYmVsc0VudHJ5Gi0KC0xhYmVs",
            "c0VudHJ5EgsKA2tleRgBIAEoCRINCgV2YWx1ZRgCIAEoCToCOAEiRgoYR2V0",
            "V29ya2Zsb3dTdGF0dXNSZXF1ZXN0EhUKDWNsdXN0ZXJfdG9rZW4YASABKAkS",
            "EwoLd29ya2Zsb3dfaWQYAiABKAkibQoZR2V0V29ya2Zsb3dTdGF0dXNSZXNw",
            "b25zZRIPCgdzdWNjZXNzGAEgASgIEhUKDWVycm9yX21lc3NhZ2UYAiABKAkS",
            "KAoGc3RhdHVzGAMgASgLMhguaGV4YWdyaWQuV29ya2Zsb3dTdGF0dXMiowIK",
            "DldvcmtmbG93U3RhdHVzEhMKC3dvcmtmbG93X2lkGAEgASgJEgwKBG5hbWUY",
            "AiABKAkSJgoFc3RhdGUYAyABKA4yFy5oZXhhZ3JpZC5Xb3JrZmxvd1N0YXRl",
            "EhQKDHN1Ym1pdHRlZF9hdBgEIAEoAxISCgpzdGFydGVkX2F0GAUgASgDEhQK",
            "DGNvbXBsZXRlZF9hdBgGIAEoAxITCgt0b3RhbF9ub2RlcxgHIAEoBRIXCg9j",
            "b21wbGV0ZWRfbm9kZXMYCCABKAUSFAoMZmFpbGVkX25vZGVzGAkgASgFEisK",
            "DW5vZGVfc3RhdHVzZXMYCiADKAsyFC5oZXhhZ3JpZC5Ob2RlU3RhdHVzEhUK",
            "DWVycm9yX21lc3NhZ2UYCyABKAki/gEKCk5vZGVTdGF0dXMSDwoHbm9kZV9p",
            "ZBgBIAEoCRIiCgVzdGF0ZRgCIAEoDjITLmhleGFncmlkLk5vZGVTdGF0ZRIW",
            "Cg5hc3NpZ25lZF9hZ2VudBgDIAEoCRISCgpzdGFydGVkX2F0GAQgASgDEhQK",
            "DGNvbXBsZXRlZF9hdBgFIAEoAxIVCg1lcnJvcl9tZXNzYWdlGAYgASgJEjIK",
            "B291dHB1dHMYByADKAsyIS5oZXhhZ3JpZC5Ob2RlU3RhdHVzLk91dHB1dHNF",
            "bnRyeRouCgxPdXRwdXRzRW50cnkSCwoDa2V5GAEgASgJEg0KBXZhbHVlGAIg",
            "ASgJOgI4ASJDChVDYW5jZWxXb3JrZmxvd1JlcXVlc3QSFQoNY2x1c3Rlcl90",
            "b2tlbhgBIAEoCRITCgt3b3JrZmxvd19pZBgCIAEoCSJAChZDYW5jZWxXb3Jr",
            "Zmxvd1Jlc3BvbnNlEg8KB3N1Y2Nlc3MYASABKAgSFQoNZXJyb3JfbWVzc2Fn",
            "ZRgCIAEoCSJTChVHZXRQZW5kaW5nV29ya1JlcXVlc3QSEAoIYWdlbnRfaWQY",
            "ASABKAkSFQoNY2x1c3Rlcl90b2tlbhgCIAEoCRIRCgltYXhfbm9kZXMYAyAB",
            "KAUiaAoWR2V0UGVuZGluZ1dvcmtSZXNwb25zZRIPCgdzdWNjZXNzGAEgASgI",
            "EhUKDWVycm9yX21lc3NhZ2UYAiABKAkSJgoKd29ya19pdGVtcxgDIAMoCzIS",
            "LmhleGFncmlkLldvcmtJdGVtItoBCghXb3JrSXRlbRITCgt3b3JrZmxvd19p",
            "ZBgBIAEoCRIPCgdub2RlX2lkGAIgASgJEjEKD25vZGVfZGVmaW5pdGlvbhgD",
            "IAEoCzIYLmhleGFncmlkLk5vZGVEZWZpbml0aW9uEjAKB2NvbnRleHQYBCAD",
            "KAsyHy5oZXhhZ3JpZC5Xb3JrSXRlbS5Db250ZXh0RW50cnkSEwoLYXNzaWdu",
            "ZWRfYXQYBSABKAMaLgoMQ29udGV4dEVudHJ5EgsKA2tleRgBIAEoCRINCgV2",
            "YWx1ZRgCIAEoCToCOAEilwEKF1JlcG9ydE5vZGVSZXN1bHRSZXF1ZXN0EhAK",
            "CGFnZW50X2lkGAEgASgJEhUKDWNsdXN0ZXJfdG9rZW4YAiABKAkSEwoLd29y",
            "a2Zsb3dfaWQYAyABKAkSDwoHbm9kZV9pZBgEIAEoCRItCgZyZXN1bHQYBSAB",
            "KAsyHS5oZXhhZ3JpZC5Ob2RlRXhlY3V0aW9uUmVzdWx0IkIKGFJlcG9ydE5v",
            "ZGVSZXN1bHRSZXNwb25zZRIPCgdzdWNjZXNzGAEgASgIEhUKDWVycm9yX21l",
            "c3NhZ2UYAiABKAki+QEKE05vZGVFeGVjdXRpb25SZXN1bHQSDwoHc3VjY2Vz",
            "cxgBIAEoCBIVCg1lcnJvcl9tZXNzYWdlGAIgASgJEjsKB291dHB1dHMYAyAD",
            "KAsyKi5oZXhhZ3JpZC5Ob2RlRXhlY3V0aW9uUmVzdWx0Lk91dHB1dHNFbnRy",
            "eRISCgpzdGFydGVkX2F0GAQgASgDEhQKDGNvbXBsZXRlZF9hdBgFIAEoAxIV",
            "Cg1hdHRlbXB0X2NvdW50GAYgASgFEgwKBGxvZ3MYByADKAkaLgoMT3V0cHV0",
            "c0VudHJ5EgsKA2tleRgBIAEoCRINCgV2YWx1ZRgCIAEoCToCOAEi2QEKEkV4",
            "ZWN1dGVOb2RlUmVxdWVzdBITCgt3b3JrZmxvd19pZBgBIAEoCRIPCgdub2Rl",
            "X2lkGAIgASgJEjEKD25vZGVfZGVmaW5pdGlvbhgDIAEoCzIYLmhleGFncmlk",
            "Lk5vZGVEZWZpbml0aW9uEjoKB2NvbnRleHQYBCADKAsyKS5oZXhhZ3JpZC5F",
            "eGVjdXRlTm9kZVJlcXVlc3QuQ29udGV4dEVudHJ5Gi4KDENvbnRleHRFbnRy",
            "eRILCgNrZXkYASABKAkSDQoFdmFsdWUYAiABKAk6AjgBImwKE0V4ZWN1dGVO",
            "b2RlUmVzcG9uc2USDwoHc3VjY2VzcxgBIAEoCBIVCg1lcnJvcl9tZXNzYWdl",
            "GAIgASgJEi0KBnJlc3VsdBgDIAEoCzIdLmhleGFncmlkLk5vZGVFeGVjdXRp",
            "b25SZXN1bHQiMQoVR2V0QWdlbnRTdGF0dXNSZXF1ZXN0EhgKEHJlcXVlc3Rp",
            "bmdfYWdlbnQYASABKAkikgEKFkdldEFnZW50U3RhdHVzUmVzcG9uc2USDwoH",
            "c3VjY2VzcxgBIAEoCBIVCg1lcnJvcl9tZXNzYWdlGAIgASgJEiUKBnN0YXR1",
            "cxgDIAEoCzIVLmhleGFncmlkLkFnZW50U3RhdHVzEikKCXJlc291cmNlcxgE",
            "IAEoCzIWLmhleGFncmlkLlJlc291cmNlSW5mbyIuChVHZXRDbHVzdGVySW5m",
            "b1JlcXVlc3QSFQoNY2x1c3Rlcl90b2tlbhgBIAEoCSJtChZHZXRDbHVzdGVy",
            "SW5mb1Jlc3BvbnNlEg8KB3N1Y2Nlc3MYASABKAgSFQoNZXJyb3JfbWVzc2Fn",
            "ZRgCIAEoCRIrCgxjbHVzdGVyX2luZm8YAyABKAsyFS5oZXhhZ3JpZC5DbHVz",
            "dGVySW5mbyKdAQoLQ2x1c3RlckluZm8SEgoKY2x1c3Rlcl9pZBgBIAEoCRIP",
            "Cgd2ZXJzaW9uGAIgASgJEhQKDHRvdGFsX2FnZW50cxgDIAEoBRIUCgxyZWFk",
            "eV9hZ2VudHMYBCABKAUSGAoQYWN0aXZlX3dvcmtmbG93cxgFIAEoBRIjCgZh",
            "Z2VudHMYBiADKAsyEy5oZXhhZ3JpZC5BZ2VudEluZm8qVwoKQWdlbnRTdGF0",
            "ZRILCgdVTktOT1dOEAASDAoIU1RBUlRJTkcQARIJCgVSRUFEWRACEggKBEJV",
            "U1kQAxIMCghEUkFJTklORxAEEgsKB09GRkxJTkUQBSqYAQoNV29ya2Zsb3dT",
            "dGF0ZRIUChBXT1JLRkxPV19VTktOT1dOEAASFgoSV09SS0ZMT1dfU1VCTUlU",
            "VEVEEAESFAoQV09SS0ZMT1dfUlVOTklORxACEhYKEldPUktGTE9XX0NPTVBM",
            "RVRFRBADEhMKD1dPUktGTE9XX0ZBSUxFRBAEEhYKEldPUktGTE9XX0NBTkNF",
            "TExFRBAFKnoKCU5vZGVTdGF0ZRIQCgxOT0RFX1VOS05PV04QABIQCgxOT0RF",
            "X1BFTkRJTkcQARIQCgxOT0RFX1JVTk5JTkcQAhISCg5OT0RFX0NPTVBMRVRF",
            "RBADEg8KC05PREVfRkFJTEVEEAQSEgoOTk9ERV9DQU5DRUxMRUQQBTKVBgoS",
            "SGV4YUdyaWRDb250cm9sbGVyElAKDVJlZ2lzdGVyQWdlbnQSHi5oZXhhZ3Jp",
            "ZC5SZWdpc3RlckFnZW50UmVxdWVzdBofLmhleGFncmlkLlJlZ2lzdGVyQWdl",
            "bnRSZXNwb25zZRJICg1TZW5kSGVhcnRiZWF0EhouaGV4YWdyaWQuSGVhcnRi",
            "ZWF0UmVxdWVzdBobLmhleGFncmlkLkhlYXJ0YmVhdFJlc3BvbnNlElYKD1Vu",
            "cmVnaXN0ZXJBZ2VudBIgLmhleGFncmlkLlVucmVnaXN0ZXJBZ2VudFJlcXVl",
            "c3QaIS5oZXhhZ3JpZC5VbnJlZ2lzdGVyQWdlbnRSZXNwb25zZRJTCg5TdWJt",
            "aXRXb3JrZmxvdxIfLmhleGFncmlkLlN1Ym1pdFdvcmtmbG93UmVxdWVzdBog",
            "LmhleGFncmlkLlN1Ym1pdFdvcmtmbG93UmVzcG9uc2USXAoRR2V0V29ya2Zs",
            "b3dTdGF0dXMSIi5oZXhhZ3JpZC5HZXRXb3JrZmxvd1N0YXR1c1JlcXVlc3Qa",
            "Iy5oZXhhZ3JpZC5HZXRXb3JrZmxvd1N0YXR1c1Jlc3BvbnNlElMKDkNhbmNl",
            "bFdvcmtmbG93Eh8uaGV4YWdyaWQuQ2FuY2VsV29ya2Zsb3dSZXF1ZXN0GiAu",
            "aGV4YWdyaWQuQ2FuY2VsV29ya2Zsb3dSZXNwb25zZRJTCg5HZXRQZW5kaW5n",
            "V29yaxIfLmhleGFncmlkLkdldFBlbmRpbmdXb3JrUmVxdWVzdBogLmhleGFn",
            "cmlkLkdldFBlbmRpbmdXb3JrUmVzcG9uc2USWQoQUmVwb3J0Tm9kZVJlc3Vs",
            "dBIhLmhleGFncmlkLlJlcG9ydE5vZGVSZXN1bHRSZXF1ZXN0GiIuaGV4YWdy",
            "aWQuUmVwb3J0Tm9kZVJlc3VsdFJlc3BvbnNlElMKDkdldENsdXN0ZXJJbmZv",
            "Eh8uaGV4YWdyaWQuR2V0Q2x1c3RlckluZm9SZXF1ZXN0GiAuaGV4YWdyaWQu",
            "R2V0Q2x1c3RlckluZm9SZXNwb25zZTKwAQoNSGV4YUdyaWRBZ2VudBJKCgtF",
            "eGVjdXRlTm9kZRIcLmhleGFncmlkLkV4ZWN1dGVOb2RlUmVxdWVzdBodLmhl",
            "eGFncmlkLkV4ZWN1dGVOb2RlUmVzcG9uc2USUwoOR2V0QWdlbnRTdGF0dXMS",
            "Hy5oZXhhZ3JpZC5HZXRBZ2VudFN0YXR1c1JlcXVlc3QaIC5oZXhhZ3JpZC5H",
            "ZXRBZ2VudFN0YXR1c1Jlc3BvbnNlQhSqAhFIZXhhR3JpZC5DTEkuR3JwY2IG",
            "cHJvdG8z"));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::HexaGrid.CLI.Grpc.AgentState), typeof(global::HexaGrid.CLI.Grpc.WorkflowState), typeof(global::HexaGrid.CLI.Grpc.NodeState), }, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::HexaGrid.CLI.Grpc.RegisterAgentRequest), global::HexaGrid.CLI.Grpc.RegisterAgentRequest.Parser, new[]{ "NodeName", "ClusterToken", "AgentInfo" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::HexaGrid.CLI.Grpc.RegisterAgentResponse), global::HexaGrid.CLI.Grpc.RegisterAgentResponse.Parser, new[]{ "Success", "ErrorMessage", "AgentId", "ClusterConfig" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::HexaGrid.CLI.Grpc.AgentInfo), global::HexaGrid.CLI.Grpc.AgentInfo.Parser, new[]{ "Version", "Platform", "Architecture", "Labels", "Resources" }, null, null, null, new pbr::GeneratedClrTypeInfo[] { null, }),
            new pbr::GeneratedClrTypeInfo(typeof(global::HexaGrid.CLI.Grpc.ResourceInfo), global::HexaGrid.CLI.Grpc.ResourceInfo.Parser, new[]{ "CpuCores", "MemoryBytes", "StorageBytes", "CpuUsagePercent", "MemoryUsedBytes", "StorageUsedBytes" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::HexaGrid.CLI.Grpc.ClusterConfig), global::HexaGrid.CLI.Grpc.ClusterConfig.Parser, new[]{ "ClusterId", "ControllerEndpoints", "HeartbeatIntervalSeconds", "WorkPollIntervalSeconds" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::HexaGrid.CLI.Grpc.HeartbeatRequest), global::HexaGrid.CLI.Grpc.HeartbeatRequest.Parser, new[]{ "AgentId", "ClusterToken", "Status", "Resources" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::HexaGrid.CLI.Grpc.HeartbeatResponse), global::HexaGrid.CLI.Grpc.HeartbeatResponse.Parser, new[]{ "Success", "ErrorMessage", "PendingCommands" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::HexaGrid.CLI.Grpc.AgentStatus), global::HexaGrid.CLI.Grpc.AgentStatus.Parser, new[]{ "State", "ActiveNodes", "LastActivity", "Version" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::HexaGrid.CLI.Grpc.UnregisterAgentRequest), global::HexaGrid.CLI.Grpc.UnregisterAgentRequest.Parser, new[]{ "AgentId", "ClusterToken" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::HexaGrid.CLI.Grpc.UnregisterAgentResponse), global::HexaGrid.CLI.Grpc.UnregisterAgentResponse.Parser, new[]{ "Success", "ErrorMessage" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::HexaGrid.CLI.Grpc.SubmitWorkflowRequest), global::HexaGrid.CLI.Grpc.SubmitWorkflowRequest.Parser, new[]{ "ClusterToken", "Workflow", "Options" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::HexaGrid.CLI.Grpc.SubmitWorkflowResponse), global::HexaGrid.CLI.Grpc.SubmitWorkflowResponse.Parser, new[]{ "Success", "ErrorMessage", "WorkflowId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::HexaGrid.CLI.Grpc.WorkflowDefinition), global::HexaGrid.CLI.Grpc.WorkflowDefinition.Parser, new[]{ "Name", "Description", "Nodes", "Connections", "Variables" }, null, null, null, new pbr::GeneratedClrTypeInfo[] { null, }),
            new pbr::GeneratedClrTypeInfo(typeof(global::HexaGrid.CLI.Grpc.NodeDefinition), global::HexaGrid.CLI.Grpc.NodeDefinition.Parser, new[]{ "Id", "Type", "Inputs", "Outputs", "Constraints" }, null, null, null, new pbr::GeneratedClrTypeInfo[] { null, null, }),
            new pbr::GeneratedClrTypeInfo(typeof(global::HexaGrid.CLI.Grpc.ConnectionDefinition), global::HexaGrid.CLI.Grpc.ConnectionDefinition.Parser, new[]{ "FromNodeId", "FromPort", "ToNodeId", "ToPort", "Condition" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::HexaGrid.CLI.Grpc.NodeConstraints), global::HexaGrid.CLI.Grpc.NodeConstraints.Parser, new[]{ "RequiredLabels", "PreferredLabels", "Resources" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::HexaGrid.CLI.Grpc.ResourceRequirements), global::HexaGrid.CLI.Grpc.ResourceRequirements.Parser, new[]{ "CpuCores", "MemoryBytes", "StorageBytes" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::HexaGrid.CLI.Grpc.WorkflowOptions), global::HexaGrid.CLI.Grpc.WorkflowOptions.Parser, new[]{ "Namespace", "StopOnFirstError", "TimeoutSeconds", "RetryCount", "Labels" }, null, null, null, new pbr::GeneratedClrTypeInfo[] { null, }),
            new pbr::GeneratedClrTypeInfo(typeof(global::HexaGrid.CLI.Grpc.GetWorkflowStatusRequest), global::HexaGrid.CLI.Grpc.GetWorkflowStatusRequest.Parser, new[]{ "ClusterToken", "WorkflowId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::HexaGrid.CLI.Grpc.GetWorkflowStatusResponse), global::HexaGrid.CLI.Grpc.GetWorkflowStatusResponse.Parser, new[]{ "Success", "ErrorMessage", "Status" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::HexaGrid.CLI.Grpc.WorkflowStatus), global::HexaGrid.CLI.Grpc.WorkflowStatus.Parser, new[]{ "WorkflowId", "Name", "State", "SubmittedAt", "StartedAt", "CompletedAt", "TotalNodes", "CompletedNodes", "FailedNodes", "NodeStatuses", "ErrorMessage" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::HexaGrid.CLI.Grpc.NodeStatus), global::HexaGrid.CLI.Grpc.NodeStatus.Parser, new[]{ "NodeId", "State", "AssignedAgent", "StartedAt", "CompletedAt", "ErrorMessage", "Outputs" }, null, null, null, new pbr::GeneratedClrTypeInfo[] { null, }),
            new pbr::GeneratedClrTypeInfo(typeof(global::HexaGrid.CLI.Grpc.CancelWorkflowRequest), global::HexaGrid.CLI.Grpc.CancelWorkflowRequest.Parser, new[]{ "ClusterToken", "WorkflowId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::HexaGrid.CLI.Grpc.CancelWorkflowResponse), global::HexaGrid.CLI.Grpc.CancelWorkflowResponse.Parser, new[]{ "Success", "ErrorMessage" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::HexaGrid.CLI.Grpc.GetPendingWorkRequest), global::HexaGrid.CLI.Grpc.GetPendingWorkRequest.Parser, new[]{ "AgentId", "ClusterToken", "MaxNodes" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::HexaGrid.CLI.Grpc.GetPendingWorkResponse), global::HexaGrid.CLI.Grpc.GetPendingWorkResponse.Parser, new[]{ "Success", "ErrorMessage", "WorkItems" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::HexaGrid.CLI.Grpc.WorkItem), global::HexaGrid.CLI.Grpc.WorkItem.Parser, new[]{ "WorkflowId", "NodeId", "NodeDefinition", "Context", "AssignedAt" }, null, null, null, new pbr::GeneratedClrTypeInfo[] { null, }),
            new pbr::GeneratedClrTypeInfo(typeof(global::HexaGrid.CLI.Grpc.ReportNodeResultRequest), global::HexaGrid.CLI.Grpc.ReportNodeResultRequest.Parser, new[]{ "AgentId", "ClusterToken", "WorkflowId", "NodeId", "Result" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::HexaGrid.CLI.Grpc.ReportNodeResultResponse), global::HexaGrid.CLI.Grpc.ReportNodeResultResponse.Parser, new[]{ "Success", "ErrorMessage" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::HexaGrid.CLI.Grpc.NodeExecutionResult), global::HexaGrid.CLI.Grpc.NodeExecutionResult.Parser, new[]{ "Success", "ErrorMessage", "Outputs", "StartedAt", "CompletedAt", "AttemptCount", "Logs" }, null, null, null, new pbr::GeneratedClrTypeInfo[] { null, }),
            new pbr::GeneratedClrTypeInfo(typeof(global::HexaGrid.CLI.Grpc.ExecuteNodeRequest), global::HexaGrid.CLI.Grpc.ExecuteNodeRequest.Parser, new[]{ "WorkflowId", "NodeId", "NodeDefinition", "Context" }, null, null, null, new pbr::GeneratedClrTypeInfo[] { null, }),
            new pbr::GeneratedClrTypeInfo(typeof(global::HexaGrid.CLI.Grpc.ExecuteNodeResponse), global::HexaGrid.CLI.Grpc.ExecuteNodeResponse.Parser, new[]{ "Success", "ErrorMessage", "Result" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::HexaGrid.CLI.Grpc.GetAgentStatusRequest), global::HexaGrid.CLI.Grpc.GetAgentStatusRequest.Parser, new[]{ "RequestingAgent" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::HexaGrid.CLI.Grpc.GetAgentStatusResponse), global::HexaGrid.CLI.Grpc.GetAgentStatusResponse.Parser, new[]{ "Success", "ErrorMessage", "Status", "Resources" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::HexaGrid.CLI.Grpc.GetClusterInfoRequest), global::HexaGrid.CLI.Grpc.GetClusterInfoRequest.Parser, new[]{ "ClusterToken" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::HexaGrid.CLI.Grpc.GetClusterInfoResponse), global::HexaGrid.CLI.Grpc.GetClusterInfoResponse.Parser, new[]{ "Success", "ErrorMessage", "ClusterInfo" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::HexaGrid.CLI.Grpc.ClusterInfo), global::HexaGrid.CLI.Grpc.ClusterInfo.Parser, new[]{ "ClusterId", "Version", "TotalAgents", "ReadyAgents", "ActiveWorkflows", "Agents" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Enums
  public enum AgentState {
    [pbr::OriginalName("UNKNOWN")] Unknown = 0,
    [pbr::OriginalName("STARTING")] Starting = 1,
    [pbr::OriginalName("READY")] Ready = 2,
    [pbr::OriginalName("BUSY")] Busy = 3,
    [pbr::OriginalName("DRAINING")] Draining = 4,
    [pbr::OriginalName("OFFLINE")] Offline = 5,
  }

  public enum WorkflowState {
    [pbr::OriginalName("WORKFLOW_UNKNOWN")] WorkflowUnknown = 0,
    [pbr::OriginalName("WORKFLOW_SUBMITTED")] WorkflowSubmitted = 1,
    [pbr::OriginalName("WORKFLOW_RUNNING")] WorkflowRunning = 2,
    [pbr::OriginalName("WORKFLOW_COMPLETED")] WorkflowCompleted = 3,
    [pbr::OriginalName("WORKFLOW_FAILED")] WorkflowFailed = 4,
    [pbr::OriginalName("WORKFLOW_CANCELLED")] WorkflowCancelled = 5,
  }

  public enum NodeState {
    [pbr::OriginalName("NODE_UNKNOWN")] NodeUnknown = 0,
    [pbr::OriginalName("NODE_PENDING")] NodePending = 1,
    [pbr::OriginalName("NODE_RUNNING")] NodeRunning = 2,
    [pbr::OriginalName("NODE_COMPLETED")] NodeCompleted = 3,
    [pbr::OriginalName("NODE_FAILED")] NodeFailed = 4,
    [pbr::OriginalName("NODE_CANCELLED")] NodeCancelled = 5,
  }

  #endregion

  #region Messages
  /// <summary>
  /// Agent Registration
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class RegisterAgentRequest : pb::IMessage<RegisterAgentRequest>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<RegisterAgentRequest> _parser = new pb::MessageParser<RegisterAgentRequest>(() => new RegisterAgentRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<RegisterAgentRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::HexaGrid.CLI.Grpc.HexagridReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RegisterAgentRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RegisterAgentRequest(RegisterAgentRequest other) : this() {
      nodeName_ = other.nodeName_;
      clusterToken_ = other.clusterToken_;
      agentInfo_ = other.agentInfo_ != null ? other.agentInfo_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RegisterAgentRequest Clone() {
      return new RegisterAgentRequest(this);
    }

    /// <summary>Field number for the "node_name" field.</summary>
    public const int NodeNameFieldNumber = 1;
    private string nodeName_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string NodeName {
      get { return nodeName_; }
      set {
        nodeName_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "cluster_token" field.</summary>
    public const int ClusterTokenFieldNumber = 2;
    private string clusterToken_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string ClusterToken {
      get { return clusterToken_; }
      set {
        clusterToken_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "agent_info" field.</summary>
    public const int AgentInfoFieldNumber = 3;
    private global::HexaGrid.CLI.Grpc.AgentInfo agentInfo_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::HexaGrid.CLI.Grpc.AgentInfo AgentInfo {
      get { return agentInfo_; }
      set {
        agentInfo_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as RegisterAgentRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(RegisterAgentRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (NodeName != other.NodeName) return false;
      if (ClusterToken != other.ClusterToken) return false;
      if (!object.Equals(AgentInfo, other.AgentInfo)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (NodeName.Length != 0) hash ^= NodeName.GetHashCode();
      if (ClusterToken.Length != 0) hash ^= ClusterToken.GetHashCode();
      if (agentInfo_ != null) hash ^= AgentInfo.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (NodeName.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(NodeName);
      }
      if (ClusterToken.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(ClusterToken);
      }
      if (agentInfo_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(AgentInfo);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (NodeName.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(NodeName);
      }
      if (ClusterToken.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(ClusterToken);
      }
      if (agentInfo_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(AgentInfo);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (NodeName.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(NodeName);
      }
      if (ClusterToken.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ClusterToken);
      }
      if (agentInfo_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(AgentInfo);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(RegisterAgentRequest other) {
      if (other == null) {
        return;
      }
      if (other.NodeName.Length != 0) {
        NodeName = other.NodeName;
      }
      if (other.ClusterToken.Length != 0) {
        ClusterToken = other.ClusterToken;
      }
      if (other.agentInfo_ != null) {
        if (agentInfo_ == null) {
          AgentInfo = new global::HexaGrid.CLI.Grpc.AgentInfo();
        }
        AgentInfo.MergeFrom(other.AgentInfo);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            NodeName = input.ReadString();
            break;
          }
          case 18: {
            ClusterToken = input.ReadString();
            break;
          }
          case 26: {
            if (agentInfo_ == null) {
              AgentInfo = new global::HexaGrid.CLI.Grpc.AgentInfo();
            }
            input.ReadMessage(AgentInfo);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            NodeName = input.ReadString();
            break;
          }
          case 18: {
            ClusterToken = input.ReadString();
            break;
          }
          case 26: {
            if (agentInfo_ == null) {
              AgentInfo = new global::HexaGrid.CLI.Grpc.AgentInfo();
            }
            input.ReadMessage(AgentInfo);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class RegisterAgentResponse : pb::IMessage<RegisterAgentResponse>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<RegisterAgentResponse> _parser = new pb::MessageParser<RegisterAgentResponse>(() => new RegisterAgentResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<RegisterAgentResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::HexaGrid.CLI.Grpc.HexagridReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RegisterAgentResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RegisterAgentResponse(RegisterAgentResponse other) : this() {
      success_ = other.success_;
      errorMessage_ = other.errorMessage_;
      agentId_ = other.agentId_;
      clusterConfig_ = other.clusterConfig_ != null ? other.clusterConfig_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RegisterAgentResponse Clone() {
      return new RegisterAgentResponse(this);
    }

    /// <summary>Field number for the "success" field.</summary>
    public const int SuccessFieldNumber = 1;
    private bool success_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Success {
      get { return success_; }
      set {
        success_ = value;
      }
    }

    /// <summary>Field number for the "error_message" field.</summary>
    public const int ErrorMessageFieldNumber = 2;
    private string errorMessage_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string ErrorMessage {
      get { return errorMessage_; }
      set {
        errorMessage_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "agent_id" field.</summary>
    public const int AgentIdFieldNumber = 3;
    private string agentId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string AgentId {
      get { return agentId_; }
      set {
        agentId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "cluster_config" field.</summary>
    public const int ClusterConfigFieldNumber = 4;
    private global::HexaGrid.CLI.Grpc.ClusterConfig clusterConfig_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::HexaGrid.CLI.Grpc.ClusterConfig ClusterConfig {
      get { return clusterConfig_; }
      set {
        clusterConfig_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as RegisterAgentResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(RegisterAgentResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Success != other.Success) return false;
      if (ErrorMessage != other.ErrorMessage) return false;
      if (AgentId != other.AgentId) return false;
      if (!object.Equals(ClusterConfig, other.ClusterConfig)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Success != false) hash ^= Success.GetHashCode();
      if (ErrorMessage.Length != 0) hash ^= ErrorMessage.GetHashCode();
      if (AgentId.Length != 0) hash ^= AgentId.GetHashCode();
      if (clusterConfig_ != null) hash ^= ClusterConfig.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Success != false) {
        output.WriteRawTag(8);
        output.WriteBool(Success);
      }
      if (ErrorMessage.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(ErrorMessage);
      }
      if (AgentId.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(AgentId);
      }
      if (clusterConfig_ != null) {
        output.WriteRawTag(34);
        output.WriteMessage(ClusterConfig);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Success != false) {
        output.WriteRawTag(8);
        output.WriteBool(Success);
      }
      if (ErrorMessage.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(ErrorMessage);
      }
      if (AgentId.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(AgentId);
      }
      if (clusterConfig_ != null) {
        output.WriteRawTag(34);
        output.WriteMessage(ClusterConfig);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Success != false) {
        size += 1 + 1;
      }
      if (ErrorMessage.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ErrorMessage);
      }
      if (AgentId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(AgentId);
      }
      if (clusterConfig_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(ClusterConfig);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(RegisterAgentResponse other) {
      if (other == null) {
        return;
      }
      if (other.Success != false) {
        Success = other.Success;
      }
      if (other.ErrorMessage.Length != 0) {
        ErrorMessage = other.ErrorMessage;
      }
      if (other.AgentId.Length != 0) {
        AgentId = other.AgentId;
      }
      if (other.clusterConfig_ != null) {
        if (clusterConfig_ == null) {
          ClusterConfig = new global::HexaGrid.CLI.Grpc.ClusterConfig();
        }
        ClusterConfig.MergeFrom(other.ClusterConfig);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Success = input.ReadBool();
            break;
          }
          case 18: {
            ErrorMessage = input.ReadString();
            break;
          }
          case 26: {
            AgentId = input.ReadString();
            break;
          }
          case 34: {
            if (clusterConfig_ == null) {
              ClusterConfig = new global::HexaGrid.CLI.Grpc.ClusterConfig();
            }
            input.ReadMessage(ClusterConfig);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Success = input.ReadBool();
            break;
          }
          case 18: {
            ErrorMessage = input.ReadString();
            break;
          }
          case 26: {
            AgentId = input.ReadString();
            break;
          }
          case 34: {
            if (clusterConfig_ == null) {
              ClusterConfig = new global::HexaGrid.CLI.Grpc.ClusterConfig();
            }
            input.ReadMessage(ClusterConfig);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class AgentInfo : pb::IMessage<AgentInfo>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<AgentInfo> _parser = new pb::MessageParser<AgentInfo>(() => new AgentInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<AgentInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::HexaGrid.CLI.Grpc.HexagridReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public AgentInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public AgentInfo(AgentInfo other) : this() {
      version_ = other.version_;
      platform_ = other.platform_;
      architecture_ = other.architecture_;
      labels_ = other.labels_.Clone();
      resources_ = other.resources_ != null ? other.resources_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public AgentInfo Clone() {
      return new AgentInfo(this);
    }

    /// <summary>Field number for the "version" field.</summary>
    public const int VersionFieldNumber = 1;
    private string version_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Version {
      get { return version_; }
      set {
        version_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "platform" field.</summary>
    public const int PlatformFieldNumber = 2;
    private string platform_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Platform {
      get { return platform_; }
      set {
        platform_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "architecture" field.</summary>
    public const int ArchitectureFieldNumber = 3;
    private string architecture_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Architecture {
      get { return architecture_; }
      set {
        architecture_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "labels" field.</summary>
    public const int LabelsFieldNumber = 4;
    private static readonly pbc::MapField<string, string>.Codec _map_labels_codec
        = new pbc::MapField<string, string>.Codec(pb::FieldCodec.ForString(10, ""), pb::FieldCodec.ForString(18, ""), 34);
    private readonly pbc::MapField<string, string> labels_ = new pbc::MapField<string, string>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<string, string> Labels {
      get { return labels_; }
    }

    /// <summary>Field number for the "resources" field.</summary>
    public const int ResourcesFieldNumber = 5;
    private global::HexaGrid.CLI.Grpc.ResourceInfo resources_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::HexaGrid.CLI.Grpc.ResourceInfo Resources {
      get { return resources_; }
      set {
        resources_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as AgentInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(AgentInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Version != other.Version) return false;
      if (Platform != other.Platform) return false;
      if (Architecture != other.Architecture) return false;
      if (!Labels.Equals(other.Labels)) return false;
      if (!object.Equals(Resources, other.Resources)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Version.Length != 0) hash ^= Version.GetHashCode();
      if (Platform.Length != 0) hash ^= Platform.GetHashCode();
      if (Architecture.Length != 0) hash ^= Architecture.GetHashCode();
      hash ^= Labels.GetHashCode();
      if (resources_ != null) hash ^= Resources.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Version.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(Version);
      }
      if (Platform.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Platform);
      }
      if (Architecture.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(Architecture);
      }
      labels_.WriteTo(output, _map_labels_codec);
      if (resources_ != null) {
        output.WriteRawTag(42);
        output.WriteMessage(Resources);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Version.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(Version);
      }
      if (Platform.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Platform);
      }
      if (Architecture.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(Architecture);
      }
      labels_.WriteTo(ref output, _map_labels_codec);
      if (resources_ != null) {
        output.WriteRawTag(42);
        output.WriteMessage(Resources);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Version.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Version);
      }
      if (Platform.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Platform);
      }
      if (Architecture.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Architecture);
      }
      size += labels_.CalculateSize(_map_labels_codec);
      if (resources_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Resources);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(AgentInfo other) {
      if (other == null) {
        return;
      }
      if (other.Version.Length != 0) {
        Version = other.Version;
      }
      if (other.Platform.Length != 0) {
        Platform = other.Platform;
      }
      if (other.Architecture.Length != 0) {
        Architecture = other.Architecture;
      }
      labels_.MergeFrom(other.labels_);
      if (other.resources_ != null) {
        if (resources_ == null) {
          Resources = new global::HexaGrid.CLI.Grpc.ResourceInfo();
        }
        Resources.MergeFrom(other.Resources);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            Version = input.ReadString();
            break;
          }
          case 18: {
            Platform = input.ReadString();
            break;
          }
          case 26: {
            Architecture = input.ReadString();
            break;
          }
          case 34: {
            labels_.AddEntriesFrom(input, _map_labels_codec);
            break;
          }
          case 42: {
            if (resources_ == null) {
              Resources = new global::HexaGrid.CLI.Grpc.ResourceInfo();
            }
            input.ReadMessage(Resources);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            Version = input.ReadString();
            break;
          }
          case 18: {
            Platform = input.ReadString();
            break;
          }
          case 26: {
            Architecture = input.ReadString();
            break;
          }
          case 34: {
            labels_.AddEntriesFrom(ref input, _map_labels_codec);
            break;
          }
          case 42: {
            if (resources_ == null) {
              Resources = new global::HexaGrid.CLI.Grpc.ResourceInfo();
            }
            input.ReadMessage(Resources);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class ResourceInfo : pb::IMessage<ResourceInfo>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<ResourceInfo> _parser = new pb::MessageParser<ResourceInfo>(() => new ResourceInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<ResourceInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::HexaGrid.CLI.Grpc.HexagridReflection.Descriptor.MessageTypes[3]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ResourceInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ResourceInfo(ResourceInfo other) : this() {
      cpuCores_ = other.cpuCores_;
      memoryBytes_ = other.memoryBytes_;
      storageBytes_ = other.storageBytes_;
      cpuUsagePercent_ = other.cpuUsagePercent_;
      memoryUsedBytes_ = other.memoryUsedBytes_;
      storageUsedBytes_ = other.storageUsedBytes_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ResourceInfo Clone() {
      return new ResourceInfo(this);
    }

    /// <summary>Field number for the "cpu_cores" field.</summary>
    public const int CpuCoresFieldNumber = 1;
    private double cpuCores_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public double CpuCores {
      get { return cpuCores_; }
      set {
        cpuCores_ = value;
      }
    }

    /// <summary>Field number for the "memory_bytes" field.</summary>
    public const int MemoryBytesFieldNumber = 2;
    private long memoryBytes_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long MemoryBytes {
      get { return memoryBytes_; }
      set {
        memoryBytes_ = value;
      }
    }

    /// <summary>Field number for the "storage_bytes" field.</summary>
    public const int StorageBytesFieldNumber = 3;
    private long storageBytes_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long StorageBytes {
      get { return storageBytes_; }
      set {
        storageBytes_ = value;
      }
    }

    /// <summary>Field number for the "cpu_usage_percent" field.</summary>
    public const int CpuUsagePercentFieldNumber = 4;
    private double cpuUsagePercent_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public double CpuUsagePercent {
      get { return cpuUsagePercent_; }
      set {
        cpuUsagePercent_ = value;
      }
    }

    /// <summary>Field number for the "memory_used_bytes" field.</summary>
    public const int MemoryUsedBytesFieldNumber = 5;
    private long memoryUsedBytes_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long MemoryUsedBytes {
      get { return memoryUsedBytes_; }
      set {
        memoryUsedBytes_ = value;
      }
    }

    /// <summary>Field number for the "storage_used_bytes" field.</summary>
    public const int StorageUsedBytesFieldNumber = 6;
    private long storageUsedBytes_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long StorageUsedBytes {
      get { return storageUsedBytes_; }
      set {
        storageUsedBytes_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as ResourceInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(ResourceInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!pbc::ProtobufEqualityComparers.BitwiseDoubleEqualityComparer.Equals(CpuCores, other.CpuCores)) return false;
      if (MemoryBytes != other.MemoryBytes) return false;
      if (StorageBytes != other.StorageBytes) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseDoubleEqualityComparer.Equals(CpuUsagePercent, other.CpuUsagePercent)) return false;
      if (MemoryUsedBytes != other.MemoryUsedBytes) return false;
      if (StorageUsedBytes != other.StorageUsedBytes) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (CpuCores != 0D) hash ^= pbc::ProtobufEqualityComparers.BitwiseDoubleEqualityComparer.GetHashCode(CpuCores);
      if (MemoryBytes != 0L) hash ^= MemoryBytes.GetHashCode();
      if (StorageBytes != 0L) hash ^= StorageBytes.GetHashCode();
      if (CpuUsagePercent != 0D) hash ^= pbc::ProtobufEqualityComparers.BitwiseDoubleEqualityComparer.GetHashCode(CpuUsagePercent);
      if (MemoryUsedBytes != 0L) hash ^= MemoryUsedBytes.GetHashCode();
      if (StorageUsedBytes != 0L) hash ^= StorageUsedBytes.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (CpuCores != 0D) {
        output.WriteRawTag(9);
        output.WriteDouble(CpuCores);
      }
      if (MemoryBytes != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(MemoryBytes);
      }
      if (StorageBytes != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(StorageBytes);
      }
      if (CpuUsagePercent != 0D) {
        output.WriteRawTag(33);
        output.WriteDouble(CpuUsagePercent);
      }
      if (MemoryUsedBytes != 0L) {
        output.WriteRawTag(40);
        output.WriteInt64(MemoryUsedBytes);
      }
      if (StorageUsedBytes != 0L) {
        output.WriteRawTag(48);
        output.WriteInt64(StorageUsedBytes);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (CpuCores != 0D) {
        output.WriteRawTag(9);
        output.WriteDouble(CpuCores);
      }
      if (MemoryBytes != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(MemoryBytes);
      }
      if (StorageBytes != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(StorageBytes);
      }
      if (CpuUsagePercent != 0D) {
        output.WriteRawTag(33);
        output.WriteDouble(CpuUsagePercent);
      }
      if (MemoryUsedBytes != 0L) {
        output.WriteRawTag(40);
        output.WriteInt64(MemoryUsedBytes);
      }
      if (StorageUsedBytes != 0L) {
        output.WriteRawTag(48);
        output.WriteInt64(StorageUsedBytes);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (CpuCores != 0D) {
        size += 1 + 8;
      }
      if (MemoryBytes != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(MemoryBytes);
      }
      if (StorageBytes != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(StorageBytes);
      }
      if (CpuUsagePercent != 0D) {
        size += 1 + 8;
      }
      if (MemoryUsedBytes != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(MemoryUsedBytes);
      }
      if (StorageUsedBytes != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(StorageUsedBytes);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(ResourceInfo other) {
      if (other == null) {
        return;
      }
      if (other.CpuCores != 0D) {
        CpuCores = other.CpuCores;
      }
      if (other.MemoryBytes != 0L) {
        MemoryBytes = other.MemoryBytes;
      }
      if (other.StorageBytes != 0L) {
        StorageBytes = other.StorageBytes;
      }
      if (other.CpuUsagePercent != 0D) {
        CpuUsagePercent = other.CpuUsagePercent;
      }
      if (other.MemoryUsedBytes != 0L) {
        MemoryUsedBytes = other.MemoryUsedBytes;
      }
      if (other.StorageUsedBytes != 0L) {
        StorageUsedBytes = other.StorageUsedBytes;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 9: {
            CpuCores = input.ReadDouble();
            break;
          }
          case 16: {
            MemoryBytes = input.ReadInt64();
            break;
          }
          case 24: {
            StorageBytes = input.ReadInt64();
            break;
          }
          case 33: {
            CpuUsagePercent = input.ReadDouble();
            break;
          }
          case 40: {
            MemoryUsedBytes = input.ReadInt64();
            break;
          }
          case 48: {
            StorageUsedBytes = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 9: {
            CpuCores = input.ReadDouble();
            break;
          }
          case 16: {
            MemoryBytes = input.ReadInt64();
            break;
          }
          case 24: {
            StorageBytes = input.ReadInt64();
            break;
          }
          case 33: {
            CpuUsagePercent = input.ReadDouble();
            break;
          }
          case 40: {
            MemoryUsedBytes = input.ReadInt64();
            break;
          }
          case 48: {
            StorageUsedBytes = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class ClusterConfig : pb::IMessage<ClusterConfig>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<ClusterConfig> _parser = new pb::MessageParser<ClusterConfig>(() => new ClusterConfig());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<ClusterConfig> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::HexaGrid.CLI.Grpc.HexagridReflection.Descriptor.MessageTypes[4]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ClusterConfig() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ClusterConfig(ClusterConfig other) : this() {
      clusterId_ = other.clusterId_;
      controllerEndpoints_ = other.controllerEndpoints_.Clone();
      heartbeatIntervalSeconds_ = other.heartbeatIntervalSeconds_;
      workPollIntervalSeconds_ = other.workPollIntervalSeconds_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ClusterConfig Clone() {
      return new ClusterConfig(this);
    }

    /// <summary>Field number for the "cluster_id" field.</summary>
    public const int ClusterIdFieldNumber = 1;
    private string clusterId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string ClusterId {
      get { return clusterId_; }
      set {
        clusterId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "controller_endpoints" field.</summary>
    public const int ControllerEndpointsFieldNumber = 2;
    private static readonly pb::FieldCodec<string> _repeated_controllerEndpoints_codec
        = pb::FieldCodec.ForString(18);
    private readonly pbc::RepeatedField<string> controllerEndpoints_ = new pbc::RepeatedField<string>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<string> ControllerEndpoints {
      get { return controllerEndpoints_; }
    }

    /// <summary>Field number for the "heartbeat_interval_seconds" field.</summary>
    public const int HeartbeatIntervalSecondsFieldNumber = 3;
    private int heartbeatIntervalSeconds_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int HeartbeatIntervalSeconds {
      get { return heartbeatIntervalSeconds_; }
      set {
        heartbeatIntervalSeconds_ = value;
      }
    }

    /// <summary>Field number for the "work_poll_interval_seconds" field.</summary>
    public const int WorkPollIntervalSecondsFieldNumber = 4;
    private int workPollIntervalSeconds_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int WorkPollIntervalSeconds {
      get { return workPollIntervalSeconds_; }
      set {
        workPollIntervalSeconds_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as ClusterConfig);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(ClusterConfig other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (ClusterId != other.ClusterId) return false;
      if(!controllerEndpoints_.Equals(other.controllerEndpoints_)) return false;
      if (HeartbeatIntervalSeconds != other.HeartbeatIntervalSeconds) return false;
      if (WorkPollIntervalSeconds != other.WorkPollIntervalSeconds) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (ClusterId.Length != 0) hash ^= ClusterId.GetHashCode();
      hash ^= controllerEndpoints_.GetHashCode();
      if (HeartbeatIntervalSeconds != 0) hash ^= HeartbeatIntervalSeconds.GetHashCode();
      if (WorkPollIntervalSeconds != 0) hash ^= WorkPollIntervalSeconds.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (ClusterId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(ClusterId);
      }
      controllerEndpoints_.WriteTo(output, _repeated_controllerEndpoints_codec);
      if (HeartbeatIntervalSeconds != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(HeartbeatIntervalSeconds);
      }
      if (WorkPollIntervalSeconds != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(WorkPollIntervalSeconds);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (ClusterId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(ClusterId);
      }
      controllerEndpoints_.WriteTo(ref output, _repeated_controllerEndpoints_codec);
      if (HeartbeatIntervalSeconds != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(HeartbeatIntervalSeconds);
      }
      if (WorkPollIntervalSeconds != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(WorkPollIntervalSeconds);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (ClusterId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ClusterId);
      }
      size += controllerEndpoints_.CalculateSize(_repeated_controllerEndpoints_codec);
      if (HeartbeatIntervalSeconds != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(HeartbeatIntervalSeconds);
      }
      if (WorkPollIntervalSeconds != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(WorkPollIntervalSeconds);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(ClusterConfig other) {
      if (other == null) {
        return;
      }
      if (other.ClusterId.Length != 0) {
        ClusterId = other.ClusterId;
      }
      controllerEndpoints_.Add(other.controllerEndpoints_);
      if (other.HeartbeatIntervalSeconds != 0) {
        HeartbeatIntervalSeconds = other.HeartbeatIntervalSeconds;
      }
      if (other.WorkPollIntervalSeconds != 0) {
        WorkPollIntervalSeconds = other.WorkPollIntervalSeconds;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            ClusterId = input.ReadString();
            break;
          }
          case 18: {
            controllerEndpoints_.AddEntriesFrom(input, _repeated_controllerEndpoints_codec);
            break;
          }
          case 24: {
            HeartbeatIntervalSeconds = input.ReadInt32();
            break;
          }
          case 32: {
            WorkPollIntervalSeconds = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            ClusterId = input.ReadString();
            break;
          }
          case 18: {
            controllerEndpoints_.AddEntriesFrom(ref input, _repeated_controllerEndpoints_codec);
            break;
          }
          case 24: {
            HeartbeatIntervalSeconds = input.ReadInt32();
            break;
          }
          case 32: {
            WorkPollIntervalSeconds = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// Heartbeat
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class HeartbeatRequest : pb::IMessage<HeartbeatRequest>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<HeartbeatRequest> _parser = new pb::MessageParser<HeartbeatRequest>(() => new HeartbeatRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<HeartbeatRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::HexaGrid.CLI.Grpc.HexagridReflection.Descriptor.MessageTypes[5]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public HeartbeatRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public HeartbeatRequest(HeartbeatRequest other) : this() {
      agentId_ = other.agentId_;
      clusterToken_ = other.clusterToken_;
      status_ = other.status_ != null ? other.status_.Clone() : null;
      resources_ = other.resources_ != null ? other.resources_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public HeartbeatRequest Clone() {
      return new HeartbeatRequest(this);
    }

    /// <summary>Field number for the "agent_id" field.</summary>
    public const int AgentIdFieldNumber = 1;
    private string agentId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string AgentId {
      get { return agentId_; }
      set {
        agentId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "cluster_token" field.</summary>
    public const int ClusterTokenFieldNumber = 2;
    private string clusterToken_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string ClusterToken {
      get { return clusterToken_; }
      set {
        clusterToken_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "status" field.</summary>
    public const int StatusFieldNumber = 3;
    private global::HexaGrid.CLI.Grpc.AgentStatus status_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::HexaGrid.CLI.Grpc.AgentStatus Status {
      get { return status_; }
      set {
        status_ = value;
      }
    }

    /// <summary>Field number for the "resources" field.</summary>
    public const int ResourcesFieldNumber = 4;
    private global::HexaGrid.CLI.Grpc.ResourceInfo resources_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::HexaGrid.CLI.Grpc.ResourceInfo Resources {
      get { return resources_; }
      set {
        resources_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as HeartbeatRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(HeartbeatRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (AgentId != other.AgentId) return false;
      if (ClusterToken != other.ClusterToken) return false;
      if (!object.Equals(Status, other.Status)) return false;
      if (!object.Equals(Resources, other.Resources)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (AgentId.Length != 0) hash ^= AgentId.GetHashCode();
      if (ClusterToken.Length != 0) hash ^= ClusterToken.GetHashCode();
      if (status_ != null) hash ^= Status.GetHashCode();
      if (resources_ != null) hash ^= Resources.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (AgentId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(AgentId);
      }
      if (ClusterToken.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(ClusterToken);
      }
      if (status_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(Status);
      }
      if (resources_ != null) {
        output.WriteRawTag(34);
        output.WriteMessage(Resources);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (AgentId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(AgentId);
      }
      if (ClusterToken.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(ClusterToken);
      }
      if (status_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(Status);
      }
      if (resources_ != null) {
        output.WriteRawTag(34);
        output.WriteMessage(Resources);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (AgentId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(AgentId);
      }
      if (ClusterToken.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ClusterToken);
      }
      if (status_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Status);
      }
      if (resources_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Resources);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(HeartbeatRequest other) {
      if (other == null) {
        return;
      }
      if (other.AgentId.Length != 0) {
        AgentId = other.AgentId;
      }
      if (other.ClusterToken.Length != 0) {
        ClusterToken = other.ClusterToken;
      }
      if (other.status_ != null) {
        if (status_ == null) {
          Status = new global::HexaGrid.CLI.Grpc.AgentStatus();
        }
        Status.MergeFrom(other.Status);
      }
      if (other.resources_ != null) {
        if (resources_ == null) {
          Resources = new global::HexaGrid.CLI.Grpc.ResourceInfo();
        }
        Resources.MergeFrom(other.Resources);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            AgentId = input.ReadString();
            break;
          }
          case 18: {
            ClusterToken = input.ReadString();
            break;
          }
          case 26: {
            if (status_ == null) {
              Status = new global::HexaGrid.CLI.Grpc.AgentStatus();
            }
            input.ReadMessage(Status);
            break;
          }
          case 34: {
            if (resources_ == null) {
              Resources = new global::HexaGrid.CLI.Grpc.ResourceInfo();
            }
            input.ReadMessage(Resources);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            AgentId = input.ReadString();
            break;
          }
          case 18: {
            ClusterToken = input.ReadString();
            break;
          }
          case 26: {
            if (status_ == null) {
              Status = new global::HexaGrid.CLI.Grpc.AgentStatus();
            }
            input.ReadMessage(Status);
            break;
          }
          case 34: {
            if (resources_ == null) {
              Resources = new global::HexaGrid.CLI.Grpc.ResourceInfo();
            }
            input.ReadMessage(Resources);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class HeartbeatResponse : pb::IMessage<HeartbeatResponse>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<HeartbeatResponse> _parser = new pb::MessageParser<HeartbeatResponse>(() => new HeartbeatResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<HeartbeatResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::HexaGrid.CLI.Grpc.HexagridReflection.Descriptor.MessageTypes[6]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public HeartbeatResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public HeartbeatResponse(HeartbeatResponse other) : this() {
      success_ = other.success_;
      errorMessage_ = other.errorMessage_;
      pendingCommands_ = other.pendingCommands_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public HeartbeatResponse Clone() {
      return new HeartbeatResponse(this);
    }

    /// <summary>Field number for the "success" field.</summary>
    public const int SuccessFieldNumber = 1;
    private bool success_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Success {
      get { return success_; }
      set {
        success_ = value;
      }
    }

    /// <summary>Field number for the "error_message" field.</summary>
    public const int ErrorMessageFieldNumber = 2;
    private string errorMessage_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string ErrorMessage {
      get { return errorMessage_; }
      set {
        errorMessage_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "pending_commands" field.</summary>
    public const int PendingCommandsFieldNumber = 3;
    private static readonly pb::FieldCodec<string> _repeated_pendingCommands_codec
        = pb::FieldCodec.ForString(26);
    private readonly pbc::RepeatedField<string> pendingCommands_ = new pbc::RepeatedField<string>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<string> PendingCommands {
      get { return pendingCommands_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as HeartbeatResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(HeartbeatResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Success != other.Success) return false;
      if (ErrorMessage != other.ErrorMessage) return false;
      if(!pendingCommands_.Equals(other.pendingCommands_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Success != false) hash ^= Success.GetHashCode();
      if (ErrorMessage.Length != 0) hash ^= ErrorMessage.GetHashCode();
      hash ^= pendingCommands_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Success != false) {
        output.WriteRawTag(8);
        output.WriteBool(Success);
      }
      if (ErrorMessage.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(ErrorMessage);
      }
      pendingCommands_.WriteTo(output, _repeated_pendingCommands_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Success != false) {
        output.WriteRawTag(8);
        output.WriteBool(Success);
      }
      if (ErrorMessage.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(ErrorMessage);
      }
      pendingCommands_.WriteTo(ref output, _repeated_pendingCommands_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Success != false) {
        size += 1 + 1;
      }
      if (ErrorMessage.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ErrorMessage);
      }
      size += pendingCommands_.CalculateSize(_repeated_pendingCommands_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(HeartbeatResponse other) {
      if (other == null) {
        return;
      }
      if (other.Success != false) {
        Success = other.Success;
      }
      if (other.ErrorMessage.Length != 0) {
        ErrorMessage = other.ErrorMessage;
      }
      pendingCommands_.Add(other.pendingCommands_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Success = input.ReadBool();
            break;
          }
          case 18: {
            ErrorMessage = input.ReadString();
            break;
          }
          case 26: {
            pendingCommands_.AddEntriesFrom(input, _repeated_pendingCommands_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Success = input.ReadBool();
            break;
          }
          case 18: {
            ErrorMessage = input.ReadString();
            break;
          }
          case 26: {
            pendingCommands_.AddEntriesFrom(ref input, _repeated_pendingCommands_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class AgentStatus : pb::IMessage<AgentStatus>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<AgentStatus> _parser = new pb::MessageParser<AgentStatus>(() => new AgentStatus());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<AgentStatus> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::HexaGrid.CLI.Grpc.HexagridReflection.Descriptor.MessageTypes[7]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public AgentStatus() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public AgentStatus(AgentStatus other) : this() {
      state_ = other.state_;
      activeNodes_ = other.activeNodes_;
      lastActivity_ = other.lastActivity_;
      version_ = other.version_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public AgentStatus Clone() {
      return new AgentStatus(this);
    }

    /// <summary>Field number for the "state" field.</summary>
    public const int StateFieldNumber = 1;
    private global::HexaGrid.CLI.Grpc.AgentState state_ = global::HexaGrid.CLI.Grpc.AgentState.Unknown;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::HexaGrid.CLI.Grpc.AgentState State {
      get { return state_; }
      set {
        state_ = value;
      }
    }

    /// <summary>Field number for the "active_nodes" field.</summary>
    public const int ActiveNodesFieldNumber = 2;
    private int activeNodes_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int ActiveNodes {
      get { return activeNodes_; }
      set {
        activeNodes_ = value;
      }
    }

    /// <summary>Field number for the "last_activity" field.</summary>
    public const int LastActivityFieldNumber = 3;
    private long lastActivity_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long LastActivity {
      get { return lastActivity_; }
      set {
        lastActivity_ = value;
      }
    }

    /// <summary>Field number for the "version" field.</summary>
    public const int VersionFieldNumber = 4;
    private string version_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Version {
      get { return version_; }
      set {
        version_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as AgentStatus);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(AgentStatus other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (State != other.State) return false;
      if (ActiveNodes != other.ActiveNodes) return false;
      if (LastActivity != other.LastActivity) return false;
      if (Version != other.Version) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (State != global::HexaGrid.CLI.Grpc.AgentState.Unknown) hash ^= State.GetHashCode();
      if (ActiveNodes != 0) hash ^= ActiveNodes.GetHashCode();
      if (LastActivity != 0L) hash ^= LastActivity.GetHashCode();
      if (Version.Length != 0) hash ^= Version.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (State != global::HexaGrid.CLI.Grpc.AgentState.Unknown) {
        output.WriteRawTag(8);
        output.WriteEnum((int) State);
      }
      if (ActiveNodes != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(ActiveNodes);
      }
      if (LastActivity != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(LastActivity);
      }
      if (Version.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(Version);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (State != global::HexaGrid.CLI.Grpc.AgentState.Unknown) {
        output.WriteRawTag(8);
        output.WriteEnum((int) State);
      }
      if (ActiveNodes != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(ActiveNodes);
      }
      if (LastActivity != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(LastActivity);
      }
      if (Version.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(Version);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (State != global::HexaGrid.CLI.Grpc.AgentState.Unknown) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) State);
      }
      if (ActiveNodes != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(ActiveNodes);
      }
      if (LastActivity != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(LastActivity);
      }
      if (Version.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Version);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(AgentStatus other) {
      if (other == null) {
        return;
      }
      if (other.State != global::HexaGrid.CLI.Grpc.AgentState.Unknown) {
        State = other.State;
      }
      if (other.ActiveNodes != 0) {
        ActiveNodes = other.ActiveNodes;
      }
      if (other.LastActivity != 0L) {
        LastActivity = other.LastActivity;
      }
      if (other.Version.Length != 0) {
        Version = other.Version;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            State = (global::HexaGrid.CLI.Grpc.AgentState) input.ReadEnum();
            break;
          }
          case 16: {
            ActiveNodes = input.ReadInt32();
            break;
          }
          case 24: {
            LastActivity = input.ReadInt64();
            break;
          }
          case 34: {
            Version = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            State = (global::HexaGrid.CLI.Grpc.AgentState) input.ReadEnum();
            break;
          }
          case 16: {
            ActiveNodes = input.ReadInt32();
            break;
          }
          case 24: {
            LastActivity = input.ReadInt64();
            break;
          }
          case 34: {
            Version = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// Agent Unregistration
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class UnregisterAgentRequest : pb::IMessage<UnregisterAgentRequest>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<UnregisterAgentRequest> _parser = new pb::MessageParser<UnregisterAgentRequest>(() => new UnregisterAgentRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<UnregisterAgentRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::HexaGrid.CLI.Grpc.HexagridReflection.Descriptor.MessageTypes[8]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public UnregisterAgentRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public UnregisterAgentRequest(UnregisterAgentRequest other) : this() {
      agentId_ = other.agentId_;
      clusterToken_ = other.clusterToken_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public UnregisterAgentRequest Clone() {
      return new UnregisterAgentRequest(this);
    }

    /// <summary>Field number for the "agent_id" field.</summary>
    public const int AgentIdFieldNumber = 1;
    private string agentId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string AgentId {
      get { return agentId_; }
      set {
        agentId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "cluster_token" field.</summary>
    public const int ClusterTokenFieldNumber = 2;
    private string clusterToken_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string ClusterToken {
      get { return clusterToken_; }
      set {
        clusterToken_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as UnregisterAgentRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(UnregisterAgentRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (AgentId != other.AgentId) return false;
      if (ClusterToken != other.ClusterToken) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (AgentId.Length != 0) hash ^= AgentId.GetHashCode();
      if (ClusterToken.Length != 0) hash ^= ClusterToken.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (AgentId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(AgentId);
      }
      if (ClusterToken.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(ClusterToken);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (AgentId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(AgentId);
      }
      if (ClusterToken.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(ClusterToken);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (AgentId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(AgentId);
      }
      if (ClusterToken.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ClusterToken);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(UnregisterAgentRequest other) {
      if (other == null) {
        return;
      }
      if (other.AgentId.Length != 0) {
        AgentId = other.AgentId;
      }
      if (other.ClusterToken.Length != 0) {
        ClusterToken = other.ClusterToken;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            AgentId = input.ReadString();
            break;
          }
          case 18: {
            ClusterToken = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            AgentId = input.ReadString();
            break;
          }
          case 18: {
            ClusterToken = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class UnregisterAgentResponse : pb::IMessage<UnregisterAgentResponse>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<UnregisterAgentResponse> _parser = new pb::MessageParser<UnregisterAgentResponse>(() => new UnregisterAgentResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<UnregisterAgentResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::HexaGrid.CLI.Grpc.HexagridReflection.Descriptor.MessageTypes[9]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public UnregisterAgentResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public UnregisterAgentResponse(UnregisterAgentResponse other) : this() {
      success_ = other.success_;
      errorMessage_ = other.errorMessage_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public UnregisterAgentResponse Clone() {
      return new UnregisterAgentResponse(this);
    }

    /// <summary>Field number for the "success" field.</summary>
    public const int SuccessFieldNumber = 1;
    private bool success_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Success {
      get { return success_; }
      set {
        success_ = value;
      }
    }

    /// <summary>Field number for the "error_message" field.</summary>
    public const int ErrorMessageFieldNumber = 2;
    private string errorMessage_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string ErrorMessage {
      get { return errorMessage_; }
      set {
        errorMessage_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as UnregisterAgentResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(UnregisterAgentResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Success != other.Success) return false;
      if (ErrorMessage != other.ErrorMessage) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Success != false) hash ^= Success.GetHashCode();
      if (ErrorMessage.Length != 0) hash ^= ErrorMessage.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Success != false) {
        output.WriteRawTag(8);
        output.WriteBool(Success);
      }
      if (ErrorMessage.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(ErrorMessage);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Success != false) {
        output.WriteRawTag(8);
        output.WriteBool(Success);
      }
      if (ErrorMessage.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(ErrorMessage);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Success != false) {
        size += 1 + 1;
      }
      if (ErrorMessage.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ErrorMessage);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(UnregisterAgentResponse other) {
      if (other == null) {
        return;
      }
      if (other.Success != false) {
        Success = other.Success;
      }
      if (other.ErrorMessage.Length != 0) {
        ErrorMessage = other.ErrorMessage;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Success = input.ReadBool();
            break;
          }
          case 18: {
            ErrorMessage = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Success = input.ReadBool();
            break;
          }
          case 18: {
            ErrorMessage = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// Workflow Submission
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SubmitWorkflowRequest : pb::IMessage<SubmitWorkflowRequest>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SubmitWorkflowRequest> _parser = new pb::MessageParser<SubmitWorkflowRequest>(() => new SubmitWorkflowRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SubmitWorkflowRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::HexaGrid.CLI.Grpc.HexagridReflection.Descriptor.MessageTypes[10]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SubmitWorkflowRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SubmitWorkflowRequest(SubmitWorkflowRequest other) : this() {
      clusterToken_ = other.clusterToken_;
      workflow_ = other.workflow_ != null ? other.workflow_.Clone() : null;
      options_ = other.options_ != null ? other.options_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SubmitWorkflowRequest Clone() {
      return new SubmitWorkflowRequest(this);
    }

    /// <summary>Field number for the "cluster_token" field.</summary>
    public const int ClusterTokenFieldNumber = 1;
    private string clusterToken_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string ClusterToken {
      get { return clusterToken_; }
      set {
        clusterToken_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "workflow" field.</summary>
    public const int WorkflowFieldNumber = 2;
    private global::HexaGrid.CLI.Grpc.WorkflowDefinition workflow_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::HexaGrid.CLI.Grpc.WorkflowDefinition Workflow {
      get { return workflow_; }
      set {
        workflow_ = value;
      }
    }

    /// <summary>Field number for the "options" field.</summary>
    public const int OptionsFieldNumber = 3;
    private global::HexaGrid.CLI.Grpc.WorkflowOptions options_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::HexaGrid.CLI.Grpc.WorkflowOptions Options {
      get { return options_; }
      set {
        options_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SubmitWorkflowRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SubmitWorkflowRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (ClusterToken != other.ClusterToken) return false;
      if (!object.Equals(Workflow, other.Workflow)) return false;
      if (!object.Equals(Options, other.Options)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (ClusterToken.Length != 0) hash ^= ClusterToken.GetHashCode();
      if (workflow_ != null) hash ^= Workflow.GetHashCode();
      if (options_ != null) hash ^= Options.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (ClusterToken.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(ClusterToken);
      }
      if (workflow_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(Workflow);
      }
      if (options_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(Options);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (ClusterToken.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(ClusterToken);
      }
      if (workflow_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(Workflow);
      }
      if (options_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(Options);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (ClusterToken.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ClusterToken);
      }
      if (workflow_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Workflow);
      }
      if (options_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Options);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SubmitWorkflowRequest other) {
      if (other == null) {
        return;
      }
      if (other.ClusterToken.Length != 0) {
        ClusterToken = other.ClusterToken;
      }
      if (other.workflow_ != null) {
        if (workflow_ == null) {
          Workflow = new global::HexaGrid.CLI.Grpc.WorkflowDefinition();
        }
        Workflow.MergeFrom(other.Workflow);
      }
      if (other.options_ != null) {
        if (options_ == null) {
          Options = new global::HexaGrid.CLI.Grpc.WorkflowOptions();
        }
        Options.MergeFrom(other.Options);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            ClusterToken = input.ReadString();
            break;
          }
          case 18: {
            if (workflow_ == null) {
              Workflow = new global::HexaGrid.CLI.Grpc.WorkflowDefinition();
            }
            input.ReadMessage(Workflow);
            break;
          }
          case 26: {
            if (options_ == null) {
              Options = new global::HexaGrid.CLI.Grpc.WorkflowOptions();
            }
            input.ReadMessage(Options);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            ClusterToken = input.ReadString();
            break;
          }
          case 18: {
            if (workflow_ == null) {
              Workflow = new global::HexaGrid.CLI.Grpc.WorkflowDefinition();
            }
            input.ReadMessage(Workflow);
            break;
          }
          case 26: {
            if (options_ == null) {
              Options = new global::HexaGrid.CLI.Grpc.WorkflowOptions();
            }
            input.ReadMessage(Options);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SubmitWorkflowResponse : pb::IMessage<SubmitWorkflowResponse>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SubmitWorkflowResponse> _parser = new pb::MessageParser<SubmitWorkflowResponse>(() => new SubmitWorkflowResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SubmitWorkflowResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::HexaGrid.CLI.Grpc.HexagridReflection.Descriptor.MessageTypes[11]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SubmitWorkflowResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SubmitWorkflowResponse(SubmitWorkflowResponse other) : this() {
      success_ = other.success_;
      errorMessage_ = other.errorMessage_;
      workflowId_ = other.workflowId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SubmitWorkflowResponse Clone() {
      return new SubmitWorkflowResponse(this);
    }

    /// <summary>Field number for the "success" field.</summary>
    public const int SuccessFieldNumber = 1;
    private bool success_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Success {
      get { return success_; }
      set {
        success_ = value;
      }
    }

    /// <summary>Field number for the "error_message" field.</summary>
    public const int ErrorMessageFieldNumber = 2;
    private string errorMessage_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string ErrorMessage {
      get { return errorMessage_; }
      set {
        errorMessage_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "workflow_id" field.</summary>
    public const int WorkflowIdFieldNumber = 3;
    private string workflowId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string WorkflowId {
      get { return workflowId_; }
      set {
        workflowId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SubmitWorkflowResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SubmitWorkflowResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Success != other.Success) return false;
      if (ErrorMessage != other.ErrorMessage) return false;
      if (WorkflowId != other.WorkflowId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Success != false) hash ^= Success.GetHashCode();
      if (ErrorMessage.Length != 0) hash ^= ErrorMessage.GetHashCode();
      if (WorkflowId.Length != 0) hash ^= WorkflowId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Success != false) {
        output.WriteRawTag(8);
        output.WriteBool(Success);
      }
      if (ErrorMessage.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(ErrorMessage);
      }
      if (WorkflowId.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(WorkflowId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Success != false) {
        output.WriteRawTag(8);
        output.WriteBool(Success);
      }
      if (ErrorMessage.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(ErrorMessage);
      }
      if (WorkflowId.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(WorkflowId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Success != false) {
        size += 1 + 1;
      }
      if (ErrorMessage.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ErrorMessage);
      }
      if (WorkflowId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(WorkflowId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SubmitWorkflowResponse other) {
      if (other == null) {
        return;
      }
      if (other.Success != false) {
        Success = other.Success;
      }
      if (other.ErrorMessage.Length != 0) {
        ErrorMessage = other.ErrorMessage;
      }
      if (other.WorkflowId.Length != 0) {
        WorkflowId = other.WorkflowId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Success = input.ReadBool();
            break;
          }
          case 18: {
            ErrorMessage = input.ReadString();
            break;
          }
          case 26: {
            WorkflowId = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Success = input.ReadBool();
            break;
          }
          case 18: {
            ErrorMessage = input.ReadString();
            break;
          }
          case 26: {
            WorkflowId = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class WorkflowDefinition : pb::IMessage<WorkflowDefinition>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<WorkflowDefinition> _parser = new pb::MessageParser<WorkflowDefinition>(() => new WorkflowDefinition());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<WorkflowDefinition> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::HexaGrid.CLI.Grpc.HexagridReflection.Descriptor.MessageTypes[12]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public WorkflowDefinition() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public WorkflowDefinition(WorkflowDefinition other) : this() {
      name_ = other.name_;
      description_ = other.description_;
      nodes_ = other.nodes_.Clone();
      connections_ = other.connections_.Clone();
      variables_ = other.variables_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public WorkflowDefinition Clone() {
      return new WorkflowDefinition(this);
    }

    /// <summary>Field number for the "name" field.</summary>
    public const int NameFieldNumber = 1;
    private string name_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Name {
      get { return name_; }
      set {
        name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "description" field.</summary>
    public const int DescriptionFieldNumber = 2;
    private string description_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Description {
      get { return description_; }
      set {
        description_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "nodes" field.</summary>
    public const int NodesFieldNumber = 3;
    private static readonly pb::FieldCodec<global::HexaGrid.CLI.Grpc.NodeDefinition> _repeated_nodes_codec
        = pb::FieldCodec.ForMessage(26, global::HexaGrid.CLI.Grpc.NodeDefinition.Parser);
    private readonly pbc::RepeatedField<global::HexaGrid.CLI.Grpc.NodeDefinition> nodes_ = new pbc::RepeatedField<global::HexaGrid.CLI.Grpc.NodeDefinition>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::HexaGrid.CLI.Grpc.NodeDefinition> Nodes {
      get { return nodes_; }
    }

    /// <summary>Field number for the "connections" field.</summary>
    public const int ConnectionsFieldNumber = 4;
    private static readonly pb::FieldCodec<global::HexaGrid.CLI.Grpc.ConnectionDefinition> _repeated_connections_codec
        = pb::FieldCodec.ForMessage(34, global::HexaGrid.CLI.Grpc.ConnectionDefinition.Parser);
    private readonly pbc::RepeatedField<global::HexaGrid.CLI.Grpc.ConnectionDefinition> connections_ = new pbc::RepeatedField<global::HexaGrid.CLI.Grpc.ConnectionDefinition>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::HexaGrid.CLI.Grpc.ConnectionDefinition> Connections {
      get { return connections_; }
    }

    /// <summary>Field number for the "variables" field.</summary>
    public const int VariablesFieldNumber = 5;
    private static readonly pbc::MapField<string, string>.Codec _map_variables_codec
        = new pbc::MapField<string, string>.Codec(pb::FieldCodec.ForString(10, ""), pb::FieldCodec.ForString(18, ""), 42);
    private readonly pbc::MapField<string, string> variables_ = new pbc::MapField<string, string>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<string, string> Variables {
      get { return variables_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as WorkflowDefinition);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(WorkflowDefinition other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Name != other.Name) return false;
      if (Description != other.Description) return false;
      if(!nodes_.Equals(other.nodes_)) return false;
      if(!connections_.Equals(other.connections_)) return false;
      if (!Variables.Equals(other.Variables)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Name.Length != 0) hash ^= Name.GetHashCode();
      if (Description.Length != 0) hash ^= Description.GetHashCode();
      hash ^= nodes_.GetHashCode();
      hash ^= connections_.GetHashCode();
      hash ^= Variables.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Name.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(Name);
      }
      if (Description.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Description);
      }
      nodes_.WriteTo(output, _repeated_nodes_codec);
      connections_.WriteTo(output, _repeated_connections_codec);
      variables_.WriteTo(output, _map_variables_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Name.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(Name);
      }
      if (Description.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Description);
      }
      nodes_.WriteTo(ref output, _repeated_nodes_codec);
      connections_.WriteTo(ref output, _repeated_connections_codec);
      variables_.WriteTo(ref output, _map_variables_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Name.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Name);
      }
      if (Description.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Description);
      }
      size += nodes_.CalculateSize(_repeated_nodes_codec);
      size += connections_.CalculateSize(_repeated_connections_codec);
      size += variables_.CalculateSize(_map_variables_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(WorkflowDefinition other) {
      if (other == null) {
        return;
      }
      if (other.Name.Length != 0) {
        Name = other.Name;
      }
      if (other.Description.Length != 0) {
        Description = other.Description;
      }
      nodes_.Add(other.nodes_);
      connections_.Add(other.connections_);
      variables_.MergeFrom(other.variables_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            Name = input.ReadString();
            break;
          }
          case 18: {
            Description = input.ReadString();
            break;
          }
          case 26: {
            nodes_.AddEntriesFrom(input, _repeated_nodes_codec);
            break;
          }
          case 34: {
            connections_.AddEntriesFrom(input, _repeated_connections_codec);
            break;
          }
          case 42: {
            variables_.AddEntriesFrom(input, _map_variables_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            Name = input.ReadString();
            break;
          }
          case 18: {
            Description = input.ReadString();
            break;
          }
          case 26: {
            nodes_.AddEntriesFrom(ref input, _repeated_nodes_codec);
            break;
          }
          case 34: {
            connections_.AddEntriesFrom(ref input, _repeated_connections_codec);
            break;
          }
          case 42: {
            variables_.AddEntriesFrom(ref input, _map_variables_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class NodeDefinition : pb::IMessage<NodeDefinition>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<NodeDefinition> _parser = new pb::MessageParser<NodeDefinition>(() => new NodeDefinition());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<NodeDefinition> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::HexaGrid.CLI.Grpc.HexagridReflection.Descriptor.MessageTypes[13]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public NodeDefinition() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public NodeDefinition(NodeDefinition other) : this() {
      id_ = other.id_;
      type_ = other.type_;
      inputs_ = other.inputs_.Clone();
      outputs_ = other.outputs_.Clone();
      constraints_ = other.constraints_ != null ? other.constraints_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public NodeDefinition Clone() {
      return new NodeDefinition(this);
    }

    /// <summary>Field number for the "id" field.</summary>
    public const int IdFieldNumber = 1;
    private string id_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Id {
      get { return id_; }
      set {
        id_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "type" field.</summary>
    public const int TypeFieldNumber = 2;
    private string type_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Type {
      get { return type_; }
      set {
        type_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "inputs" field.</summary>
    public const int InputsFieldNumber = 3;
    private static readonly pbc::MapField<string, string>.Codec _map_inputs_codec
        = new pbc::MapField<string, string>.Codec(pb::FieldCodec.ForString(10, ""), pb::FieldCodec.ForString(18, ""), 26);
    private readonly pbc::MapField<string, string> inputs_ = new pbc::MapField<string, string>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<string, string> Inputs {
      get { return inputs_; }
    }

    /// <summary>Field number for the "outputs" field.</summary>
    public const int OutputsFieldNumber = 4;
    private static readonly pbc::MapField<string, string>.Codec _map_outputs_codec
        = new pbc::MapField<string, string>.Codec(pb::FieldCodec.ForString(10, ""), pb::FieldCodec.ForString(18, ""), 34);
    private readonly pbc::MapField<string, string> outputs_ = new pbc::MapField<string, string>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<string, string> Outputs {
      get { return outputs_; }
    }

    /// <summary>Field number for the "constraints" field.</summary>
    public const int ConstraintsFieldNumber = 5;
    private global::HexaGrid.CLI.Grpc.NodeConstraints constraints_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::HexaGrid.CLI.Grpc.NodeConstraints Constraints {
      get { return constraints_; }
      set {
        constraints_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as NodeDefinition);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(NodeDefinition other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if (Type != other.Type) return false;
      if (!Inputs.Equals(other.Inputs)) return false;
      if (!Outputs.Equals(other.Outputs)) return false;
      if (!object.Equals(Constraints, other.Constraints)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Id.Length != 0) hash ^= Id.GetHashCode();
      if (Type.Length != 0) hash ^= Type.GetHashCode();
      hash ^= Inputs.GetHashCode();
      hash ^= Outputs.GetHashCode();
      if (constraints_ != null) hash ^= Constraints.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Id.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(Id);
      }
      if (Type.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Type);
      }
      inputs_.WriteTo(output, _map_inputs_codec);
      outputs_.WriteTo(output, _map_outputs_codec);
      if (constraints_ != null) {
        output.WriteRawTag(42);
        output.WriteMessage(Constraints);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Id.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(Id);
      }
      if (Type.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Type);
      }
      inputs_.WriteTo(ref output, _map_inputs_codec);
      outputs_.WriteTo(ref output, _map_outputs_codec);
      if (constraints_ != null) {
        output.WriteRawTag(42);
        output.WriteMessage(Constraints);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Id.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Id);
      }
      if (Type.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Type);
      }
      size += inputs_.CalculateSize(_map_inputs_codec);
      size += outputs_.CalculateSize(_map_outputs_codec);
      if (constraints_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Constraints);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(NodeDefinition other) {
      if (other == null) {
        return;
      }
      if (other.Id.Length != 0) {
        Id = other.Id;
      }
      if (other.Type.Length != 0) {
        Type = other.Type;
      }
      inputs_.MergeFrom(other.inputs_);
      outputs_.MergeFrom(other.outputs_);
      if (other.constraints_ != null) {
        if (constraints_ == null) {
          Constraints = new global::HexaGrid.CLI.Grpc.NodeConstraints();
        }
        Constraints.MergeFrom(other.Constraints);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            Id = input.ReadString();
            break;
          }
          case 18: {
            Type = input.ReadString();
            break;
          }
          case 26: {
            inputs_.AddEntriesFrom(input, _map_inputs_codec);
            break;
          }
          case 34: {
            outputs_.AddEntriesFrom(input, _map_outputs_codec);
            break;
          }
          case 42: {
            if (constraints_ == null) {
              Constraints = new global::HexaGrid.CLI.Grpc.NodeConstraints();
            }
            input.ReadMessage(Constraints);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            Id = input.ReadString();
            break;
          }
          case 18: {
            Type = input.ReadString();
            break;
          }
          case 26: {
            inputs_.AddEntriesFrom(ref input, _map_inputs_codec);
            break;
          }
          case 34: {
            outputs_.AddEntriesFrom(ref input, _map_outputs_codec);
            break;
          }
          case 42: {
            if (constraints_ == null) {
              Constraints = new global::HexaGrid.CLI.Grpc.NodeConstraints();
            }
            input.ReadMessage(Constraints);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class ConnectionDefinition : pb::IMessage<ConnectionDefinition>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<ConnectionDefinition> _parser = new pb::MessageParser<ConnectionDefinition>(() => new ConnectionDefinition());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<ConnectionDefinition> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::HexaGrid.CLI.Grpc.HexagridReflection.Descriptor.MessageTypes[14]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ConnectionDefinition() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ConnectionDefinition(ConnectionDefinition other) : this() {
      fromNodeId_ = other.fromNodeId_;
      fromPort_ = other.fromPort_;
      toNodeId_ = other.toNodeId_;
      toPort_ = other.toPort_;
      condition_ = other.condition_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ConnectionDefinition Clone() {
      return new ConnectionDefinition(this);
    }

    /// <summary>Field number for the "from_node_id" field.</summary>
    public const int FromNodeIdFieldNumber = 1;
    private string fromNodeId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string FromNodeId {
      get { return fromNodeId_; }
      set {
        fromNodeId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "from_port" field.</summary>
    public const int FromPortFieldNumber = 2;
    private string fromPort_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string FromPort {
      get { return fromPort_; }
      set {
        fromPort_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "to_node_id" field.</summary>
    public const int ToNodeIdFieldNumber = 3;
    private string toNodeId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string ToNodeId {
      get { return toNodeId_; }
      set {
        toNodeId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "to_port" field.</summary>
    public const int ToPortFieldNumber = 4;
    private string toPort_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string ToPort {
      get { return toPort_; }
      set {
        toPort_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "condition" field.</summary>
    public const int ConditionFieldNumber = 5;
    private string condition_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Condition {
      get { return condition_; }
      set {
        condition_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as ConnectionDefinition);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(ConnectionDefinition other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (FromNodeId != other.FromNodeId) return false;
      if (FromPort != other.FromPort) return false;
      if (ToNodeId != other.ToNodeId) return false;
      if (ToPort != other.ToPort) return false;
      if (Condition != other.Condition) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (FromNodeId.Length != 0) hash ^= FromNodeId.GetHashCode();
      if (FromPort.Length != 0) hash ^= FromPort.GetHashCode();
      if (ToNodeId.Length != 0) hash ^= ToNodeId.GetHashCode();
      if (ToPort.Length != 0) hash ^= ToPort.GetHashCode();
      if (Condition.Length != 0) hash ^= Condition.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (FromNodeId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(FromNodeId);
      }
      if (FromPort.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(FromPort);
      }
      if (ToNodeId.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(ToNodeId);
      }
      if (ToPort.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(ToPort);
      }
      if (Condition.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(Condition);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (FromNodeId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(FromNodeId);
      }
      if (FromPort.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(FromPort);
      }
      if (ToNodeId.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(ToNodeId);
      }
      if (ToPort.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(ToPort);
      }
      if (Condition.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(Condition);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (FromNodeId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(FromNodeId);
      }
      if (FromPort.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(FromPort);
      }
      if (ToNodeId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ToNodeId);
      }
      if (ToPort.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ToPort);
      }
      if (Condition.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Condition);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(ConnectionDefinition other) {
      if (other == null) {
        return;
      }
      if (other.FromNodeId.Length != 0) {
        FromNodeId = other.FromNodeId;
      }
      if (other.FromPort.Length != 0) {
        FromPort = other.FromPort;
      }
      if (other.ToNodeId.Length != 0) {
        ToNodeId = other.ToNodeId;
      }
      if (other.ToPort.Length != 0) {
        ToPort = other.ToPort;
      }
      if (other.Condition.Length != 0) {
        Condition = other.Condition;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            FromNodeId = input.ReadString();
            break;
          }
          case 18: {
            FromPort = input.ReadString();
            break;
          }
          case 26: {
            ToNodeId = input.ReadString();
            break;
          }
          case 34: {
            ToPort = input.ReadString();
            break;
          }
          case 42: {
            Condition = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            FromNodeId = input.ReadString();
            break;
          }
          case 18: {
            FromPort = input.ReadString();
            break;
          }
          case 26: {
            ToNodeId = input.ReadString();
            break;
          }
          case 34: {
            ToPort = input.ReadString();
            break;
          }
          case 42: {
            Condition = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class NodeConstraints : pb::IMessage<NodeConstraints>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<NodeConstraints> _parser = new pb::MessageParser<NodeConstraints>(() => new NodeConstraints());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<NodeConstraints> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::HexaGrid.CLI.Grpc.HexagridReflection.Descriptor.MessageTypes[15]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public NodeConstraints() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public NodeConstraints(NodeConstraints other) : this() {
      requiredLabels_ = other.requiredLabels_.Clone();
      preferredLabels_ = other.preferredLabels_.Clone();
      resources_ = other.resources_ != null ? other.resources_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public NodeConstraints Clone() {
      return new NodeConstraints(this);
    }

    /// <summary>Field number for the "required_labels" field.</summary>
    public const int RequiredLabelsFieldNumber = 1;
    private static readonly pb::FieldCodec<string> _repeated_requiredLabels_codec
        = pb::FieldCodec.ForString(10);
    private readonly pbc::RepeatedField<string> requiredLabels_ = new pbc::RepeatedField<string>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<string> RequiredLabels {
      get { return requiredLabels_; }
    }

    /// <summary>Field number for the "preferred_labels" field.</summary>
    public const int PreferredLabelsFieldNumber = 2;
    private static readonly pb::FieldCodec<string> _repeated_preferredLabels_codec
        = pb::FieldCodec.ForString(18);
    private readonly pbc::RepeatedField<string> preferredLabels_ = new pbc::RepeatedField<string>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<string> PreferredLabels {
      get { return preferredLabels_; }
    }

    /// <summary>Field number for the "resources" field.</summary>
    public const int ResourcesFieldNumber = 3;
    private global::HexaGrid.CLI.Grpc.ResourceRequirements resources_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::HexaGrid.CLI.Grpc.ResourceRequirements Resources {
      get { return resources_; }
      set {
        resources_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as NodeConstraints);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(NodeConstraints other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!requiredLabels_.Equals(other.requiredLabels_)) return false;
      if(!preferredLabels_.Equals(other.preferredLabels_)) return false;
      if (!object.Equals(Resources, other.Resources)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= requiredLabels_.GetHashCode();
      hash ^= preferredLabels_.GetHashCode();
      if (resources_ != null) hash ^= Resources.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      requiredLabels_.WriteTo(output, _repeated_requiredLabels_codec);
      preferredLabels_.WriteTo(output, _repeated_preferredLabels_codec);
      if (resources_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(Resources);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      requiredLabels_.WriteTo(ref output, _repeated_requiredLabels_codec);
      preferredLabels_.WriteTo(ref output, _repeated_preferredLabels_codec);
      if (resources_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(Resources);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += requiredLabels_.CalculateSize(_repeated_requiredLabels_codec);
      size += preferredLabels_.CalculateSize(_repeated_preferredLabels_codec);
      if (resources_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Resources);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(NodeConstraints other) {
      if (other == null) {
        return;
      }
      requiredLabels_.Add(other.requiredLabels_);
      preferredLabels_.Add(other.preferredLabels_);
      if (other.resources_ != null) {
        if (resources_ == null) {
          Resources = new global::HexaGrid.CLI.Grpc.ResourceRequirements();
        }
        Resources.MergeFrom(other.Resources);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            requiredLabels_.AddEntriesFrom(input, _repeated_requiredLabels_codec);
            break;
          }
          case 18: {
            preferredLabels_.AddEntriesFrom(input, _repeated_preferredLabels_codec);
            break;
          }
          case 26: {
            if (resources_ == null) {
              Resources = new global::HexaGrid.CLI.Grpc.ResourceRequirements();
            }
            input.ReadMessage(Resources);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            requiredLabels_.AddEntriesFrom(ref input, _repeated_requiredLabels_codec);
            break;
          }
          case 18: {
            preferredLabels_.AddEntriesFrom(ref input, _repeated_preferredLabels_codec);
            break;
          }
          case 26: {
            if (resources_ == null) {
              Resources = new global::HexaGrid.CLI.Grpc.ResourceRequirements();
            }
            input.ReadMessage(Resources);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class ResourceRequirements : pb::IMessage<ResourceRequirements>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<ResourceRequirements> _parser = new pb::MessageParser<ResourceRequirements>(() => new ResourceRequirements());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<ResourceRequirements> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::HexaGrid.CLI.Grpc.HexagridReflection.Descriptor.MessageTypes[16]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ResourceRequirements() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ResourceRequirements(ResourceRequirements other) : this() {
      cpuCores_ = other.cpuCores_;
      memoryBytes_ = other.memoryBytes_;
      storageBytes_ = other.storageBytes_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ResourceRequirements Clone() {
      return new ResourceRequirements(this);
    }

    /// <summary>Field number for the "cpu_cores" field.</summary>
    public const int CpuCoresFieldNumber = 1;
    private double cpuCores_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public double CpuCores {
      get { return cpuCores_; }
      set {
        cpuCores_ = value;
      }
    }

    /// <summary>Field number for the "memory_bytes" field.</summary>
    public const int MemoryBytesFieldNumber = 2;
    private long memoryBytes_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long MemoryBytes {
      get { return memoryBytes_; }
      set {
        memoryBytes_ = value;
      }
    }

    /// <summary>Field number for the "storage_bytes" field.</summary>
    public const int StorageBytesFieldNumber = 3;
    private long storageBytes_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long StorageBytes {
      get { return storageBytes_; }
      set {
        storageBytes_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as ResourceRequirements);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(ResourceRequirements other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!pbc::ProtobufEqualityComparers.BitwiseDoubleEqualityComparer.Equals(CpuCores, other.CpuCores)) return false;
      if (MemoryBytes != other.MemoryBytes) return false;
      if (StorageBytes != other.StorageBytes) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (CpuCores != 0D) hash ^= pbc::ProtobufEqualityComparers.BitwiseDoubleEqualityComparer.GetHashCode(CpuCores);
      if (MemoryBytes != 0L) hash ^= MemoryBytes.GetHashCode();
      if (StorageBytes != 0L) hash ^= StorageBytes.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (CpuCores != 0D) {
        output.WriteRawTag(9);
        output.WriteDouble(CpuCores);
      }
      if (MemoryBytes != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(MemoryBytes);
      }
      if (StorageBytes != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(StorageBytes);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (CpuCores != 0D) {
        output.WriteRawTag(9);
        output.WriteDouble(CpuCores);
      }
      if (MemoryBytes != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(MemoryBytes);
      }
      if (StorageBytes != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(StorageBytes);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (CpuCores != 0D) {
        size += 1 + 8;
      }
      if (MemoryBytes != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(MemoryBytes);
      }
      if (StorageBytes != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(StorageBytes);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(ResourceRequirements other) {
      if (other == null) {
        return;
      }
      if (other.CpuCores != 0D) {
        CpuCores = other.CpuCores;
      }
      if (other.MemoryBytes != 0L) {
        MemoryBytes = other.MemoryBytes;
      }
      if (other.StorageBytes != 0L) {
        StorageBytes = other.StorageBytes;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 9: {
            CpuCores = input.ReadDouble();
            break;
          }
          case 16: {
            MemoryBytes = input.ReadInt64();
            break;
          }
          case 24: {
            StorageBytes = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 9: {
            CpuCores = input.ReadDouble();
            break;
          }
          case 16: {
            MemoryBytes = input.ReadInt64();
            break;
          }
          case 24: {
            StorageBytes = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class WorkflowOptions : pb::IMessage<WorkflowOptions>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<WorkflowOptions> _parser = new pb::MessageParser<WorkflowOptions>(() => new WorkflowOptions());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<WorkflowOptions> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::HexaGrid.CLI.Grpc.HexagridReflection.Descriptor.MessageTypes[17]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public WorkflowOptions() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public WorkflowOptions(WorkflowOptions other) : this() {
      namespace_ = other.namespace_;
      stopOnFirstError_ = other.stopOnFirstError_;
      timeoutSeconds_ = other.timeoutSeconds_;
      retryCount_ = other.retryCount_;
      labels_ = other.labels_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public WorkflowOptions Clone() {
      return new WorkflowOptions(this);
    }

    /// <summary>Field number for the "namespace" field.</summary>
    public const int NamespaceFieldNumber = 1;
    private string namespace_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Namespace {
      get { return namespace_; }
      set {
        namespace_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "stop_on_first_error" field.</summary>
    public const int StopOnFirstErrorFieldNumber = 2;
    private bool stopOnFirstError_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool StopOnFirstError {
      get { return stopOnFirstError_; }
      set {
        stopOnFirstError_ = value;
      }
    }

    /// <summary>Field number for the "timeout_seconds" field.</summary>
    public const int TimeoutSecondsFieldNumber = 3;
    private int timeoutSeconds_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int TimeoutSeconds {
      get { return timeoutSeconds_; }
      set {
        timeoutSeconds_ = value;
      }
    }

    /// <summary>Field number for the "retry_count" field.</summary>
    public const int RetryCountFieldNumber = 4;
    private int retryCount_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int RetryCount {
      get { return retryCount_; }
      set {
        retryCount_ = value;
      }
    }

    /// <summary>Field number for the "labels" field.</summary>
    public const int LabelsFieldNumber = 5;
    private static readonly pbc::MapField<string, string>.Codec _map_labels_codec
        = new pbc::MapField<string, string>.Codec(pb::FieldCodec.ForString(10, ""), pb::FieldCodec.ForString(18, ""), 42);
    private readonly pbc::MapField<string, string> labels_ = new pbc::MapField<string, string>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<string, string> Labels {
      get { return labels_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as WorkflowOptions);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(WorkflowOptions other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Namespace != other.Namespace) return false;
      if (StopOnFirstError != other.StopOnFirstError) return false;
      if (TimeoutSeconds != other.TimeoutSeconds) return false;
      if (RetryCount != other.RetryCount) return false;
      if (!Labels.Equals(other.Labels)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Namespace.Length != 0) hash ^= Namespace.GetHashCode();
      if (StopOnFirstError != false) hash ^= StopOnFirstError.GetHashCode();
      if (TimeoutSeconds != 0) hash ^= TimeoutSeconds.GetHashCode();
      if (RetryCount != 0) hash ^= RetryCount.GetHashCode();
      hash ^= Labels.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Namespace.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(Namespace);
      }
      if (StopOnFirstError != false) {
        output.WriteRawTag(16);
        output.WriteBool(StopOnFirstError);
      }
      if (TimeoutSeconds != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(TimeoutSeconds);
      }
      if (RetryCount != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(RetryCount);
      }
      labels_.WriteTo(output, _map_labels_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Namespace.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(Namespace);
      }
      if (StopOnFirstError != false) {
        output.WriteRawTag(16);
        output.WriteBool(StopOnFirstError);
      }
      if (TimeoutSeconds != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(TimeoutSeconds);
      }
      if (RetryCount != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(RetryCount);
      }
      labels_.WriteTo(ref output, _map_labels_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Namespace.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Namespace);
      }
      if (StopOnFirstError != false) {
        size += 1 + 1;
      }
      if (TimeoutSeconds != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(TimeoutSeconds);
      }
      if (RetryCount != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(RetryCount);
      }
      size += labels_.CalculateSize(_map_labels_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(WorkflowOptions other) {
      if (other == null) {
        return;
      }
      if (other.Namespace.Length != 0) {
        Namespace = other.Namespace;
      }
      if (other.StopOnFirstError != false) {
        StopOnFirstError = other.StopOnFirstError;
      }
      if (other.TimeoutSeconds != 0) {
        TimeoutSeconds = other.TimeoutSeconds;
      }
      if (other.RetryCount != 0) {
        RetryCount = other.RetryCount;
      }
      labels_.MergeFrom(other.labels_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            Namespace = input.ReadString();
            break;
          }
          case 16: {
            StopOnFirstError = input.ReadBool();
            break;
          }
          case 24: {
            TimeoutSeconds = input.ReadInt32();
            break;
          }
          case 32: {
            RetryCount = input.ReadInt32();
            break;
          }
          case 42: {
            labels_.AddEntriesFrom(input, _map_labels_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            Namespace = input.ReadString();
            break;
          }
          case 16: {
            StopOnFirstError = input.ReadBool();
            break;
          }
          case 24: {
            TimeoutSeconds = input.ReadInt32();
            break;
          }
          case 32: {
            RetryCount = input.ReadInt32();
            break;
          }
          case 42: {
            labels_.AddEntriesFrom(ref input, _map_labels_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// Workflow Status
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class GetWorkflowStatusRequest : pb::IMessage<GetWorkflowStatusRequest>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<GetWorkflowStatusRequest> _parser = new pb::MessageParser<GetWorkflowStatusRequest>(() => new GetWorkflowStatusRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<GetWorkflowStatusRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::HexaGrid.CLI.Grpc.HexagridReflection.Descriptor.MessageTypes[18]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetWorkflowStatusRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetWorkflowStatusRequest(GetWorkflowStatusRequest other) : this() {
      clusterToken_ = other.clusterToken_;
      workflowId_ = other.workflowId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetWorkflowStatusRequest Clone() {
      return new GetWorkflowStatusRequest(this);
    }

    /// <summary>Field number for the "cluster_token" field.</summary>
    public const int ClusterTokenFieldNumber = 1;
    private string clusterToken_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string ClusterToken {
      get { return clusterToken_; }
      set {
        clusterToken_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "workflow_id" field.</summary>
    public const int WorkflowIdFieldNumber = 2;
    private string workflowId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string WorkflowId {
      get { return workflowId_; }
      set {
        workflowId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as GetWorkflowStatusRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(GetWorkflowStatusRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (ClusterToken != other.ClusterToken) return false;
      if (WorkflowId != other.WorkflowId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (ClusterToken.Length != 0) hash ^= ClusterToken.GetHashCode();
      if (WorkflowId.Length != 0) hash ^= WorkflowId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (ClusterToken.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(ClusterToken);
      }
      if (WorkflowId.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(WorkflowId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (ClusterToken.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(ClusterToken);
      }
      if (WorkflowId.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(WorkflowId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (ClusterToken.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ClusterToken);
      }
      if (WorkflowId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(WorkflowId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(GetWorkflowStatusRequest other) {
      if (other == null) {
        return;
      }
      if (other.ClusterToken.Length != 0) {
        ClusterToken = other.ClusterToken;
      }
      if (other.WorkflowId.Length != 0) {
        WorkflowId = other.WorkflowId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            ClusterToken = input.ReadString();
            break;
          }
          case 18: {
            WorkflowId = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            ClusterToken = input.ReadString();
            break;
          }
          case 18: {
            WorkflowId = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class GetWorkflowStatusResponse : pb::IMessage<GetWorkflowStatusResponse>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<GetWorkflowStatusResponse> _parser = new pb::MessageParser<GetWorkflowStatusResponse>(() => new GetWorkflowStatusResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<GetWorkflowStatusResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::HexaGrid.CLI.Grpc.HexagridReflection.Descriptor.MessageTypes[19]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetWorkflowStatusResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetWorkflowStatusResponse(GetWorkflowStatusResponse other) : this() {
      success_ = other.success_;
      errorMessage_ = other.errorMessage_;
      status_ = other.status_ != null ? other.status_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetWorkflowStatusResponse Clone() {
      return new GetWorkflowStatusResponse(this);
    }

    /// <summary>Field number for the "success" field.</summary>
    public const int SuccessFieldNumber = 1;
    private bool success_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Success {
      get { return success_; }
      set {
        success_ = value;
      }
    }

    /// <summary>Field number for the "error_message" field.</summary>
    public const int ErrorMessageFieldNumber = 2;
    private string errorMessage_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string ErrorMessage {
      get { return errorMessage_; }
      set {
        errorMessage_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "status" field.</summary>
    public const int StatusFieldNumber = 3;
    private global::HexaGrid.CLI.Grpc.WorkflowStatus status_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::HexaGrid.CLI.Grpc.WorkflowStatus Status {
      get { return status_; }
      set {
        status_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as GetWorkflowStatusResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(GetWorkflowStatusResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Success != other.Success) return false;
      if (ErrorMessage != other.ErrorMessage) return false;
      if (!object.Equals(Status, other.Status)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Success != false) hash ^= Success.GetHashCode();
      if (ErrorMessage.Length != 0) hash ^= ErrorMessage.GetHashCode();
      if (status_ != null) hash ^= Status.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Success != false) {
        output.WriteRawTag(8);
        output.WriteBool(Success);
      }
      if (ErrorMessage.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(ErrorMessage);
      }
      if (status_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(Status);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Success != false) {
        output.WriteRawTag(8);
        output.WriteBool(Success);
      }
      if (ErrorMessage.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(ErrorMessage);
      }
      if (status_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(Status);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Success != false) {
        size += 1 + 1;
      }
      if (ErrorMessage.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ErrorMessage);
      }
      if (status_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Status);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(GetWorkflowStatusResponse other) {
      if (other == null) {
        return;
      }
      if (other.Success != false) {
        Success = other.Success;
      }
      if (other.ErrorMessage.Length != 0) {
        ErrorMessage = other.ErrorMessage;
      }
      if (other.status_ != null) {
        if (status_ == null) {
          Status = new global::HexaGrid.CLI.Grpc.WorkflowStatus();
        }
        Status.MergeFrom(other.Status);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Success = input.ReadBool();
            break;
          }
          case 18: {
            ErrorMessage = input.ReadString();
            break;
          }
          case 26: {
            if (status_ == null) {
              Status = new global::HexaGrid.CLI.Grpc.WorkflowStatus();
            }
            input.ReadMessage(Status);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Success = input.ReadBool();
            break;
          }
          case 18: {
            ErrorMessage = input.ReadString();
            break;
          }
          case 26: {
            if (status_ == null) {
              Status = new global::HexaGrid.CLI.Grpc.WorkflowStatus();
            }
            input.ReadMessage(Status);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class WorkflowStatus : pb::IMessage<WorkflowStatus>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<WorkflowStatus> _parser = new pb::MessageParser<WorkflowStatus>(() => new WorkflowStatus());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<WorkflowStatus> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::HexaGrid.CLI.Grpc.HexagridReflection.Descriptor.MessageTypes[20]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public WorkflowStatus() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public WorkflowStatus(WorkflowStatus other) : this() {
      workflowId_ = other.workflowId_;
      name_ = other.name_;
      state_ = other.state_;
      submittedAt_ = other.submittedAt_;
      startedAt_ = other.startedAt_;
      completedAt_ = other.completedAt_;
      totalNodes_ = other.totalNodes_;
      completedNodes_ = other.completedNodes_;
      failedNodes_ = other.failedNodes_;
      nodeStatuses_ = other.nodeStatuses_.Clone();
      errorMessage_ = other.errorMessage_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public WorkflowStatus Clone() {
      return new WorkflowStatus(this);
    }

    /// <summary>Field number for the "workflow_id" field.</summary>
    public const int WorkflowIdFieldNumber = 1;
    private string workflowId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string WorkflowId {
      get { return workflowId_; }
      set {
        workflowId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "name" field.</summary>
    public const int NameFieldNumber = 2;
    private string name_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Name {
      get { return name_; }
      set {
        name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "state" field.</summary>
    public const int StateFieldNumber = 3;
    private global::HexaGrid.CLI.Grpc.WorkflowState state_ = global::HexaGrid.CLI.Grpc.WorkflowState.WorkflowUnknown;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::HexaGrid.CLI.Grpc.WorkflowState State {
      get { return state_; }
      set {
        state_ = value;
      }
    }

    /// <summary>Field number for the "submitted_at" field.</summary>
    public const int SubmittedAtFieldNumber = 4;
    private long submittedAt_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long SubmittedAt {
      get { return submittedAt_; }
      set {
        submittedAt_ = value;
      }
    }

    /// <summary>Field number for the "started_at" field.</summary>
    public const int StartedAtFieldNumber = 5;
    private long startedAt_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long StartedAt {
      get { return startedAt_; }
      set {
        startedAt_ = value;
      }
    }

    /// <summary>Field number for the "completed_at" field.</summary>
    public const int CompletedAtFieldNumber = 6;
    private long completedAt_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long CompletedAt {
      get { return completedAt_; }
      set {
        completedAt_ = value;
      }
    }

    /// <summary>Field number for the "total_nodes" field.</summary>
    public const int TotalNodesFieldNumber = 7;
    private int totalNodes_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int TotalNodes {
      get { return totalNodes_; }
      set {
        totalNodes_ = value;
      }
    }

    /// <summary>Field number for the "completed_nodes" field.</summary>
    public const int CompletedNodesFieldNumber = 8;
    private int completedNodes_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CompletedNodes {
      get { return completedNodes_; }
      set {
        completedNodes_ = value;
      }
    }

    /// <summary>Field number for the "failed_nodes" field.</summary>
    public const int FailedNodesFieldNumber = 9;
    private int failedNodes_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int FailedNodes {
      get { return failedNodes_; }
      set {
        failedNodes_ = value;
      }
    }

    /// <summary>Field number for the "node_statuses" field.</summary>
    public const int NodeStatusesFieldNumber = 10;
    private static readonly pb::FieldCodec<global::HexaGrid.CLI.Grpc.NodeStatus> _repeated_nodeStatuses_codec
        = pb::FieldCodec.ForMessage(82, global::HexaGrid.CLI.Grpc.NodeStatus.Parser);
    private readonly pbc::RepeatedField<global::HexaGrid.CLI.Grpc.NodeStatus> nodeStatuses_ = new pbc::RepeatedField<global::HexaGrid.CLI.Grpc.NodeStatus>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::HexaGrid.CLI.Grpc.NodeStatus> NodeStatuses {
      get { return nodeStatuses_; }
    }

    /// <summary>Field number for the "error_message" field.</summary>
    public const int ErrorMessageFieldNumber = 11;
    private string errorMessage_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string ErrorMessage {
      get { return errorMessage_; }
      set {
        errorMessage_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as WorkflowStatus);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(WorkflowStatus other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (WorkflowId != other.WorkflowId) return false;
      if (Name != other.Name) return false;
      if (State != other.State) return false;
      if (SubmittedAt != other.SubmittedAt) return false;
      if (StartedAt != other.StartedAt) return false;
      if (CompletedAt != other.CompletedAt) return false;
      if (TotalNodes != other.TotalNodes) return false;
      if (CompletedNodes != other.CompletedNodes) return false;
      if (FailedNodes != other.FailedNodes) return false;
      if(!nodeStatuses_.Equals(other.nodeStatuses_)) return false;
      if (ErrorMessage != other.ErrorMessage) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (WorkflowId.Length != 0) hash ^= WorkflowId.GetHashCode();
      if (Name.Length != 0) hash ^= Name.GetHashCode();
      if (State != global::HexaGrid.CLI.Grpc.WorkflowState.WorkflowUnknown) hash ^= State.GetHashCode();
      if (SubmittedAt != 0L) hash ^= SubmittedAt.GetHashCode();
      if (StartedAt != 0L) hash ^= StartedAt.GetHashCode();
      if (CompletedAt != 0L) hash ^= CompletedAt.GetHashCode();
      if (TotalNodes != 0) hash ^= TotalNodes.GetHashCode();
      if (CompletedNodes != 0) hash ^= CompletedNodes.GetHashCode();
      if (FailedNodes != 0) hash ^= FailedNodes.GetHashCode();
      hash ^= nodeStatuses_.GetHashCode();
      if (ErrorMessage.Length != 0) hash ^= ErrorMessage.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (WorkflowId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(WorkflowId);
      }
      if (Name.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Name);
      }
      if (State != global::HexaGrid.CLI.Grpc.WorkflowState.WorkflowUnknown) {
        output.WriteRawTag(24);
        output.WriteEnum((int) State);
      }
      if (SubmittedAt != 0L) {
        output.WriteRawTag(32);
        output.WriteInt64(SubmittedAt);
      }
      if (StartedAt != 0L) {
        output.WriteRawTag(40);
        output.WriteInt64(StartedAt);
      }
      if (CompletedAt != 0L) {
        output.WriteRawTag(48);
        output.WriteInt64(CompletedAt);
      }
      if (TotalNodes != 0) {
        output.WriteRawTag(56);
        output.WriteInt32(TotalNodes);
      }
      if (CompletedNodes != 0) {
        output.WriteRawTag(64);
        output.WriteInt32(CompletedNodes);
      }
      if (FailedNodes != 0) {
        output.WriteRawTag(72);
        output.WriteInt32(FailedNodes);
      }
      nodeStatuses_.WriteTo(output, _repeated_nodeStatuses_codec);
      if (ErrorMessage.Length != 0) {
        output.WriteRawTag(90);
        output.WriteString(ErrorMessage);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (WorkflowId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(WorkflowId);
      }
      if (Name.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Name);
      }
      if (State != global::HexaGrid.CLI.Grpc.WorkflowState.WorkflowUnknown) {
        output.WriteRawTag(24);
        output.WriteEnum((int) State);
      }
      if (SubmittedAt != 0L) {
        output.WriteRawTag(32);
        output.WriteInt64(SubmittedAt);
      }
      if (StartedAt != 0L) {
        output.WriteRawTag(40);
        output.WriteInt64(StartedAt);
      }
      if (CompletedAt != 0L) {
        output.WriteRawTag(48);
        output.WriteInt64(CompletedAt);
      }
      if (TotalNodes != 0) {
        output.WriteRawTag(56);
        output.WriteInt32(TotalNodes);
      }
      if (CompletedNodes != 0) {
        output.WriteRawTag(64);
        output.WriteInt32(CompletedNodes);
      }
      if (FailedNodes != 0) {
        output.WriteRawTag(72);
        output.WriteInt32(FailedNodes);
      }
      nodeStatuses_.WriteTo(ref output, _repeated_nodeStatuses_codec);
      if (ErrorMessage.Length != 0) {
        output.WriteRawTag(90);
        output.WriteString(ErrorMessage);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (WorkflowId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(WorkflowId);
      }
      if (Name.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Name);
      }
      if (State != global::HexaGrid.CLI.Grpc.WorkflowState.WorkflowUnknown) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) State);
      }
      if (SubmittedAt != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(SubmittedAt);
      }
      if (StartedAt != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(StartedAt);
      }
      if (CompletedAt != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(CompletedAt);
      }
      if (TotalNodes != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(TotalNodes);
      }
      if (CompletedNodes != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(CompletedNodes);
      }
      if (FailedNodes != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(FailedNodes);
      }
      size += nodeStatuses_.CalculateSize(_repeated_nodeStatuses_codec);
      if (ErrorMessage.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ErrorMessage);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(WorkflowStatus other) {
      if (other == null) {
        return;
      }
      if (other.WorkflowId.Length != 0) {
        WorkflowId = other.WorkflowId;
      }
      if (other.Name.Length != 0) {
        Name = other.Name;
      }
      if (other.State != global::HexaGrid.CLI.Grpc.WorkflowState.WorkflowUnknown) {
        State = other.State;
      }
      if (other.SubmittedAt != 0L) {
        SubmittedAt = other.SubmittedAt;
      }
      if (other.StartedAt != 0L) {
        StartedAt = other.StartedAt;
      }
      if (other.CompletedAt != 0L) {
        CompletedAt = other.CompletedAt;
      }
      if (other.TotalNodes != 0) {
        TotalNodes = other.TotalNodes;
      }
      if (other.CompletedNodes != 0) {
        CompletedNodes = other.CompletedNodes;
      }
      if (other.FailedNodes != 0) {
        FailedNodes = other.FailedNodes;
      }
      nodeStatuses_.Add(other.nodeStatuses_);
      if (other.ErrorMessage.Length != 0) {
        ErrorMessage = other.ErrorMessage;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            WorkflowId = input.ReadString();
            break;
          }
          case 18: {
            Name = input.ReadString();
            break;
          }
          case 24: {
            State = (global::HexaGrid.CLI.Grpc.WorkflowState) input.ReadEnum();
            break;
          }
          case 32: {
            SubmittedAt = input.ReadInt64();
            break;
          }
          case 40: {
            StartedAt = input.ReadInt64();
            break;
          }
          case 48: {
            CompletedAt = input.ReadInt64();
            break;
          }
          case 56: {
            TotalNodes = input.ReadInt32();
            break;
          }
          case 64: {
            CompletedNodes = input.ReadInt32();
            break;
          }
          case 72: {
            FailedNodes = input.ReadInt32();
            break;
          }
          case 82: {
            nodeStatuses_.AddEntriesFrom(input, _repeated_nodeStatuses_codec);
            break;
          }
          case 90: {
            ErrorMessage = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            WorkflowId = input.ReadString();
            break;
          }
          case 18: {
            Name = input.ReadString();
            break;
          }
          case 24: {
            State = (global::HexaGrid.CLI.Grpc.WorkflowState) input.ReadEnum();
            break;
          }
          case 32: {
            SubmittedAt = input.ReadInt64();
            break;
          }
          case 40: {
            StartedAt = input.ReadInt64();
            break;
          }
          case 48: {
            CompletedAt = input.ReadInt64();
            break;
          }
          case 56: {
            TotalNodes = input.ReadInt32();
            break;
          }
          case 64: {
            CompletedNodes = input.ReadInt32();
            break;
          }
          case 72: {
            FailedNodes = input.ReadInt32();
            break;
          }
          case 82: {
            nodeStatuses_.AddEntriesFrom(ref input, _repeated_nodeStatuses_codec);
            break;
          }
          case 90: {
            ErrorMessage = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class NodeStatus : pb::IMessage<NodeStatus>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<NodeStatus> _parser = new pb::MessageParser<NodeStatus>(() => new NodeStatus());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<NodeStatus> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::HexaGrid.CLI.Grpc.HexagridReflection.Descriptor.MessageTypes[21]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public NodeStatus() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public NodeStatus(NodeStatus other) : this() {
      nodeId_ = other.nodeId_;
      state_ = other.state_;
      assignedAgent_ = other.assignedAgent_;
      startedAt_ = other.startedAt_;
      completedAt_ = other.completedAt_;
      errorMessage_ = other.errorMessage_;
      outputs_ = other.outputs_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public NodeStatus Clone() {
      return new NodeStatus(this);
    }

    /// <summary>Field number for the "node_id" field.</summary>
    public const int NodeIdFieldNumber = 1;
    private string nodeId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string NodeId {
      get { return nodeId_; }
      set {
        nodeId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "state" field.</summary>
    public const int StateFieldNumber = 2;
    private global::HexaGrid.CLI.Grpc.NodeState state_ = global::HexaGrid.CLI.Grpc.NodeState.NodeUnknown;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::HexaGrid.CLI.Grpc.NodeState State {
      get { return state_; }
      set {
        state_ = value;
      }
    }

    /// <summary>Field number for the "assigned_agent" field.</summary>
    public const int AssignedAgentFieldNumber = 3;
    private string assignedAgent_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string AssignedAgent {
      get { return assignedAgent_; }
      set {
        assignedAgent_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "started_at" field.</summary>
    public const int StartedAtFieldNumber = 4;
    private long startedAt_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long StartedAt {
      get { return startedAt_; }
      set {
        startedAt_ = value;
      }
    }

    /// <summary>Field number for the "completed_at" field.</summary>
    public const int CompletedAtFieldNumber = 5;
    private long completedAt_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long CompletedAt {
      get { return completedAt_; }
      set {
        completedAt_ = value;
      }
    }

    /// <summary>Field number for the "error_message" field.</summary>
    public const int ErrorMessageFieldNumber = 6;
    private string errorMessage_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string ErrorMessage {
      get { return errorMessage_; }
      set {
        errorMessage_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "outputs" field.</summary>
    public const int OutputsFieldNumber = 7;
    private static readonly pbc::MapField<string, string>.Codec _map_outputs_codec
        = new pbc::MapField<string, string>.Codec(pb::FieldCodec.ForString(10, ""), pb::FieldCodec.ForString(18, ""), 58);
    private readonly pbc::MapField<string, string> outputs_ = new pbc::MapField<string, string>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<string, string> Outputs {
      get { return outputs_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as NodeStatus);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(NodeStatus other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (NodeId != other.NodeId) return false;
      if (State != other.State) return false;
      if (AssignedAgent != other.AssignedAgent) return false;
      if (StartedAt != other.StartedAt) return false;
      if (CompletedAt != other.CompletedAt) return false;
      if (ErrorMessage != other.ErrorMessage) return false;
      if (!Outputs.Equals(other.Outputs)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (NodeId.Length != 0) hash ^= NodeId.GetHashCode();
      if (State != global::HexaGrid.CLI.Grpc.NodeState.NodeUnknown) hash ^= State.GetHashCode();
      if (AssignedAgent.Length != 0) hash ^= AssignedAgent.GetHashCode();
      if (StartedAt != 0L) hash ^= StartedAt.GetHashCode();
      if (CompletedAt != 0L) hash ^= CompletedAt.GetHashCode();
      if (ErrorMessage.Length != 0) hash ^= ErrorMessage.GetHashCode();
      hash ^= Outputs.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (NodeId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(NodeId);
      }
      if (State != global::HexaGrid.CLI.Grpc.NodeState.NodeUnknown) {
        output.WriteRawTag(16);
        output.WriteEnum((int) State);
      }
      if (AssignedAgent.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(AssignedAgent);
      }
      if (StartedAt != 0L) {
        output.WriteRawTag(32);
        output.WriteInt64(StartedAt);
      }
      if (CompletedAt != 0L) {
        output.WriteRawTag(40);
        output.WriteInt64(CompletedAt);
      }
      if (ErrorMessage.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(ErrorMessage);
      }
      outputs_.WriteTo(output, _map_outputs_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (NodeId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(NodeId);
      }
      if (State != global::HexaGrid.CLI.Grpc.NodeState.NodeUnknown) {
        output.WriteRawTag(16);
        output.WriteEnum((int) State);
      }
      if (AssignedAgent.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(AssignedAgent);
      }
      if (StartedAt != 0L) {
        output.WriteRawTag(32);
        output.WriteInt64(StartedAt);
      }
      if (CompletedAt != 0L) {
        output.WriteRawTag(40);
        output.WriteInt64(CompletedAt);
      }
      if (ErrorMessage.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(ErrorMessage);
      }
      outputs_.WriteTo(ref output, _map_outputs_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (NodeId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(NodeId);
      }
      if (State != global::HexaGrid.CLI.Grpc.NodeState.NodeUnknown) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) State);
      }
      if (AssignedAgent.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(AssignedAgent);
      }
      if (StartedAt != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(StartedAt);
      }
      if (CompletedAt != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(CompletedAt);
      }
      if (ErrorMessage.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ErrorMessage);
      }
      size += outputs_.CalculateSize(_map_outputs_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(NodeStatus other) {
      if (other == null) {
        return;
      }
      if (other.NodeId.Length != 0) {
        NodeId = other.NodeId;
      }
      if (other.State != global::HexaGrid.CLI.Grpc.NodeState.NodeUnknown) {
        State = other.State;
      }
      if (other.AssignedAgent.Length != 0) {
        AssignedAgent = other.AssignedAgent;
      }
      if (other.StartedAt != 0L) {
        StartedAt = other.StartedAt;
      }
      if (other.CompletedAt != 0L) {
        CompletedAt = other.CompletedAt;
      }
      if (other.ErrorMessage.Length != 0) {
        ErrorMessage = other.ErrorMessage;
      }
      outputs_.MergeFrom(other.outputs_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            NodeId = input.ReadString();
            break;
          }
          case 16: {
            State = (global::HexaGrid.CLI.Grpc.NodeState) input.ReadEnum();
            break;
          }
          case 26: {
            AssignedAgent = input.ReadString();
            break;
          }
          case 32: {
            StartedAt = input.ReadInt64();
            break;
          }
          case 40: {
            CompletedAt = input.ReadInt64();
            break;
          }
          case 50: {
            ErrorMessage = input.ReadString();
            break;
          }
          case 58: {
            outputs_.AddEntriesFrom(input, _map_outputs_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            NodeId = input.ReadString();
            break;
          }
          case 16: {
            State = (global::HexaGrid.CLI.Grpc.NodeState) input.ReadEnum();
            break;
          }
          case 26: {
            AssignedAgent = input.ReadString();
            break;
          }
          case 32: {
            StartedAt = input.ReadInt64();
            break;
          }
          case 40: {
            CompletedAt = input.ReadInt64();
            break;
          }
          case 50: {
            ErrorMessage = input.ReadString();
            break;
          }
          case 58: {
            outputs_.AddEntriesFrom(ref input, _map_outputs_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// Workflow Cancellation
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CancelWorkflowRequest : pb::IMessage<CancelWorkflowRequest>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CancelWorkflowRequest> _parser = new pb::MessageParser<CancelWorkflowRequest>(() => new CancelWorkflowRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CancelWorkflowRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::HexaGrid.CLI.Grpc.HexagridReflection.Descriptor.MessageTypes[22]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CancelWorkflowRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CancelWorkflowRequest(CancelWorkflowRequest other) : this() {
      clusterToken_ = other.clusterToken_;
      workflowId_ = other.workflowId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CancelWorkflowRequest Clone() {
      return new CancelWorkflowRequest(this);
    }

    /// <summary>Field number for the "cluster_token" field.</summary>
    public const int ClusterTokenFieldNumber = 1;
    private string clusterToken_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string ClusterToken {
      get { return clusterToken_; }
      set {
        clusterToken_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "workflow_id" field.</summary>
    public const int WorkflowIdFieldNumber = 2;
    private string workflowId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string WorkflowId {
      get { return workflowId_; }
      set {
        workflowId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CancelWorkflowRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CancelWorkflowRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (ClusterToken != other.ClusterToken) return false;
      if (WorkflowId != other.WorkflowId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (ClusterToken.Length != 0) hash ^= ClusterToken.GetHashCode();
      if (WorkflowId.Length != 0) hash ^= WorkflowId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (ClusterToken.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(ClusterToken);
      }
      if (WorkflowId.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(WorkflowId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (ClusterToken.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(ClusterToken);
      }
      if (WorkflowId.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(WorkflowId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (ClusterToken.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ClusterToken);
      }
      if (WorkflowId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(WorkflowId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CancelWorkflowRequest other) {
      if (other == null) {
        return;
      }
      if (other.ClusterToken.Length != 0) {
        ClusterToken = other.ClusterToken;
      }
      if (other.WorkflowId.Length != 0) {
        WorkflowId = other.WorkflowId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            ClusterToken = input.ReadString();
            break;
          }
          case 18: {
            WorkflowId = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            ClusterToken = input.ReadString();
            break;
          }
          case 18: {
            WorkflowId = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CancelWorkflowResponse : pb::IMessage<CancelWorkflowResponse>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CancelWorkflowResponse> _parser = new pb::MessageParser<CancelWorkflowResponse>(() => new CancelWorkflowResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CancelWorkflowResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::HexaGrid.CLI.Grpc.HexagridReflection.Descriptor.MessageTypes[23]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CancelWorkflowResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CancelWorkflowResponse(CancelWorkflowResponse other) : this() {
      success_ = other.success_;
      errorMessage_ = other.errorMessage_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CancelWorkflowResponse Clone() {
      return new CancelWorkflowResponse(this);
    }

    /// <summary>Field number for the "success" field.</summary>
    public const int SuccessFieldNumber = 1;
    private bool success_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Success {
      get { return success_; }
      set {
        success_ = value;
      }
    }

    /// <summary>Field number for the "error_message" field.</summary>
    public const int ErrorMessageFieldNumber = 2;
    private string errorMessage_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string ErrorMessage {
      get { return errorMessage_; }
      set {
        errorMessage_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CancelWorkflowResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CancelWorkflowResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Success != other.Success) return false;
      if (ErrorMessage != other.ErrorMessage) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Success != false) hash ^= Success.GetHashCode();
      if (ErrorMessage.Length != 0) hash ^= ErrorMessage.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Success != false) {
        output.WriteRawTag(8);
        output.WriteBool(Success);
      }
      if (ErrorMessage.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(ErrorMessage);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Success != false) {
        output.WriteRawTag(8);
        output.WriteBool(Success);
      }
      if (ErrorMessage.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(ErrorMessage);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Success != false) {
        size += 1 + 1;
      }
      if (ErrorMessage.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ErrorMessage);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CancelWorkflowResponse other) {
      if (other == null) {
        return;
      }
      if (other.Success != false) {
        Success = other.Success;
      }
      if (other.ErrorMessage.Length != 0) {
        ErrorMessage = other.ErrorMessage;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Success = input.ReadBool();
            break;
          }
          case 18: {
            ErrorMessage = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Success = input.ReadBool();
            break;
          }
          case 18: {
            ErrorMessage = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// Work Distribution
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class GetPendingWorkRequest : pb::IMessage<GetPendingWorkRequest>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<GetPendingWorkRequest> _parser = new pb::MessageParser<GetPendingWorkRequest>(() => new GetPendingWorkRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<GetPendingWorkRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::HexaGrid.CLI.Grpc.HexagridReflection.Descriptor.MessageTypes[24]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetPendingWorkRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetPendingWorkRequest(GetPendingWorkRequest other) : this() {
      agentId_ = other.agentId_;
      clusterToken_ = other.clusterToken_;
      maxNodes_ = other.maxNodes_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetPendingWorkRequest Clone() {
      return new GetPendingWorkRequest(this);
    }

    /// <summary>Field number for the "agent_id" field.</summary>
    public const int AgentIdFieldNumber = 1;
    private string agentId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string AgentId {
      get { return agentId_; }
      set {
        agentId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "cluster_token" field.</summary>
    public const int ClusterTokenFieldNumber = 2;
    private string clusterToken_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string ClusterToken {
      get { return clusterToken_; }
      set {
        clusterToken_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "max_nodes" field.</summary>
    public const int MaxNodesFieldNumber = 3;
    private int maxNodes_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int MaxNodes {
      get { return maxNodes_; }
      set {
        maxNodes_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as GetPendingWorkRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(GetPendingWorkRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (AgentId != other.AgentId) return false;
      if (ClusterToken != other.ClusterToken) return false;
      if (MaxNodes != other.MaxNodes) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (AgentId.Length != 0) hash ^= AgentId.GetHashCode();
      if (ClusterToken.Length != 0) hash ^= ClusterToken.GetHashCode();
      if (MaxNodes != 0) hash ^= MaxNodes.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (AgentId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(AgentId);
      }
      if (ClusterToken.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(ClusterToken);
      }
      if (MaxNodes != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(MaxNodes);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (AgentId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(AgentId);
      }
      if (ClusterToken.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(ClusterToken);
      }
      if (MaxNodes != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(MaxNodes);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (AgentId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(AgentId);
      }
      if (ClusterToken.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ClusterToken);
      }
      if (MaxNodes != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(MaxNodes);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(GetPendingWorkRequest other) {
      if (other == null) {
        return;
      }
      if (other.AgentId.Length != 0) {
        AgentId = other.AgentId;
      }
      if (other.ClusterToken.Length != 0) {
        ClusterToken = other.ClusterToken;
      }
      if (other.MaxNodes != 0) {
        MaxNodes = other.MaxNodes;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            AgentId = input.ReadString();
            break;
          }
          case 18: {
            ClusterToken = input.ReadString();
            break;
          }
          case 24: {
            MaxNodes = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            AgentId = input.ReadString();
            break;
          }
          case 18: {
            ClusterToken = input.ReadString();
            break;
          }
          case 24: {
            MaxNodes = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class GetPendingWorkResponse : pb::IMessage<GetPendingWorkResponse>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<GetPendingWorkResponse> _parser = new pb::MessageParser<GetPendingWorkResponse>(() => new GetPendingWorkResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<GetPendingWorkResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::HexaGrid.CLI.Grpc.HexagridReflection.Descriptor.MessageTypes[25]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetPendingWorkResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetPendingWorkResponse(GetPendingWorkResponse other) : this() {
      success_ = other.success_;
      errorMessage_ = other.errorMessage_;
      workItems_ = other.workItems_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetPendingWorkResponse Clone() {
      return new GetPendingWorkResponse(this);
    }

    /// <summary>Field number for the "success" field.</summary>
    public const int SuccessFieldNumber = 1;
    private bool success_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Success {
      get { return success_; }
      set {
        success_ = value;
      }
    }

    /// <summary>Field number for the "error_message" field.</summary>
    public const int ErrorMessageFieldNumber = 2;
    private string errorMessage_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string ErrorMessage {
      get { return errorMessage_; }
      set {
        errorMessage_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "work_items" field.</summary>
    public const int WorkItemsFieldNumber = 3;
    private static readonly pb::FieldCodec<global::HexaGrid.CLI.Grpc.WorkItem> _repeated_workItems_codec
        = pb::FieldCodec.ForMessage(26, global::HexaGrid.CLI.Grpc.WorkItem.Parser);
    private readonly pbc::RepeatedField<global::HexaGrid.CLI.Grpc.WorkItem> workItems_ = new pbc::RepeatedField<global::HexaGrid.CLI.Grpc.WorkItem>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::HexaGrid.CLI.Grpc.WorkItem> WorkItems {
      get { return workItems_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as GetPendingWorkResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(GetPendingWorkResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Success != other.Success) return false;
      if (ErrorMessage != other.ErrorMessage) return false;
      if(!workItems_.Equals(other.workItems_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Success != false) hash ^= Success.GetHashCode();
      if (ErrorMessage.Length != 0) hash ^= ErrorMessage.GetHashCode();
      hash ^= workItems_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Success != false) {
        output.WriteRawTag(8);
        output.WriteBool(Success);
      }
      if (ErrorMessage.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(ErrorMessage);
      }
      workItems_.WriteTo(output, _repeated_workItems_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Success != false) {
        output.WriteRawTag(8);
        output.WriteBool(Success);
      }
      if (ErrorMessage.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(ErrorMessage);
      }
      workItems_.WriteTo(ref output, _repeated_workItems_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Success != false) {
        size += 1 + 1;
      }
      if (ErrorMessage.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ErrorMessage);
      }
      size += workItems_.CalculateSize(_repeated_workItems_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(GetPendingWorkResponse other) {
      if (other == null) {
        return;
      }
      if (other.Success != false) {
        Success = other.Success;
      }
      if (other.ErrorMessage.Length != 0) {
        ErrorMessage = other.ErrorMessage;
      }
      workItems_.Add(other.workItems_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Success = input.ReadBool();
            break;
          }
          case 18: {
            ErrorMessage = input.ReadString();
            break;
          }
          case 26: {
            workItems_.AddEntriesFrom(input, _repeated_workItems_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Success = input.ReadBool();
            break;
          }
          case 18: {
            ErrorMessage = input.ReadString();
            break;
          }
          case 26: {
            workItems_.AddEntriesFrom(ref input, _repeated_workItems_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class WorkItem : pb::IMessage<WorkItem>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<WorkItem> _parser = new pb::MessageParser<WorkItem>(() => new WorkItem());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<WorkItem> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::HexaGrid.CLI.Grpc.HexagridReflection.Descriptor.MessageTypes[26]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public WorkItem() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public WorkItem(WorkItem other) : this() {
      workflowId_ = other.workflowId_;
      nodeId_ = other.nodeId_;
      nodeDefinition_ = other.nodeDefinition_ != null ? other.nodeDefinition_.Clone() : null;
      context_ = other.context_.Clone();
      assignedAt_ = other.assignedAt_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public WorkItem Clone() {
      return new WorkItem(this);
    }

    /// <summary>Field number for the "workflow_id" field.</summary>
    public const int WorkflowIdFieldNumber = 1;
    private string workflowId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string WorkflowId {
      get { return workflowId_; }
      set {
        workflowId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "node_id" field.</summary>
    public const int NodeIdFieldNumber = 2;
    private string nodeId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string NodeId {
      get { return nodeId_; }
      set {
        nodeId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "node_definition" field.</summary>
    public const int NodeDefinitionFieldNumber = 3;
    private global::HexaGrid.CLI.Grpc.NodeDefinition nodeDefinition_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::HexaGrid.CLI.Grpc.NodeDefinition NodeDefinition {
      get { return nodeDefinition_; }
      set {
        nodeDefinition_ = value;
      }
    }

    /// <summary>Field number for the "context" field.</summary>
    public const int ContextFieldNumber = 4;
    private static readonly pbc::MapField<string, string>.Codec _map_context_codec
        = new pbc::MapField<string, string>.Codec(pb::FieldCodec.ForString(10, ""), pb::FieldCodec.ForString(18, ""), 34);
    private readonly pbc::MapField<string, string> context_ = new pbc::MapField<string, string>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<string, string> Context {
      get { return context_; }
    }

    /// <summary>Field number for the "assigned_at" field.</summary>
    public const int AssignedAtFieldNumber = 5;
    private long assignedAt_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long AssignedAt {
      get { return assignedAt_; }
      set {
        assignedAt_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as WorkItem);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(WorkItem other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (WorkflowId != other.WorkflowId) return false;
      if (NodeId != other.NodeId) return false;
      if (!object.Equals(NodeDefinition, other.NodeDefinition)) return false;
      if (!Context.Equals(other.Context)) return false;
      if (AssignedAt != other.AssignedAt) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (WorkflowId.Length != 0) hash ^= WorkflowId.GetHashCode();
      if (NodeId.Length != 0) hash ^= NodeId.GetHashCode();
      if (nodeDefinition_ != null) hash ^= NodeDefinition.GetHashCode();
      hash ^= Context.GetHashCode();
      if (AssignedAt != 0L) hash ^= AssignedAt.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (WorkflowId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(WorkflowId);
      }
      if (NodeId.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(NodeId);
      }
      if (nodeDefinition_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(NodeDefinition);
      }
      context_.WriteTo(output, _map_context_codec);
      if (AssignedAt != 0L) {
        output.WriteRawTag(40);
        output.WriteInt64(AssignedAt);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (WorkflowId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(WorkflowId);
      }
      if (NodeId.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(NodeId);
      }
      if (nodeDefinition_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(NodeDefinition);
      }
      context_.WriteTo(ref output, _map_context_codec);
      if (AssignedAt != 0L) {
        output.WriteRawTag(40);
        output.WriteInt64(AssignedAt);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (WorkflowId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(WorkflowId);
      }
      if (NodeId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(NodeId);
      }
      if (nodeDefinition_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(NodeDefinition);
      }
      size += context_.CalculateSize(_map_context_codec);
      if (AssignedAt != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(AssignedAt);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(WorkItem other) {
      if (other == null) {
        return;
      }
      if (other.WorkflowId.Length != 0) {
        WorkflowId = other.WorkflowId;
      }
      if (other.NodeId.Length != 0) {
        NodeId = other.NodeId;
      }
      if (other.nodeDefinition_ != null) {
        if (nodeDefinition_ == null) {
          NodeDefinition = new global::HexaGrid.CLI.Grpc.NodeDefinition();
        }
        NodeDefinition.MergeFrom(other.NodeDefinition);
      }
      context_.MergeFrom(other.context_);
      if (other.AssignedAt != 0L) {
        AssignedAt = other.AssignedAt;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            WorkflowId = input.ReadString();
            break;
          }
          case 18: {
            NodeId = input.ReadString();
            break;
          }
          case 26: {
            if (nodeDefinition_ == null) {
              NodeDefinition = new global::HexaGrid.CLI.Grpc.NodeDefinition();
            }
            input.ReadMessage(NodeDefinition);
            break;
          }
          case 34: {
            context_.AddEntriesFrom(input, _map_context_codec);
            break;
          }
          case 40: {
            AssignedAt = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            WorkflowId = input.ReadString();
            break;
          }
          case 18: {
            NodeId = input.ReadString();
            break;
          }
          case 26: {
            if (nodeDefinition_ == null) {
              NodeDefinition = new global::HexaGrid.CLI.Grpc.NodeDefinition();
            }
            input.ReadMessage(NodeDefinition);
            break;
          }
          case 34: {
            context_.AddEntriesFrom(ref input, _map_context_codec);
            break;
          }
          case 40: {
            AssignedAt = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// Node Result Reporting
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class ReportNodeResultRequest : pb::IMessage<ReportNodeResultRequest>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<ReportNodeResultRequest> _parser = new pb::MessageParser<ReportNodeResultRequest>(() => new ReportNodeResultRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<ReportNodeResultRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::HexaGrid.CLI.Grpc.HexagridReflection.Descriptor.MessageTypes[27]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ReportNodeResultRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ReportNodeResultRequest(ReportNodeResultRequest other) : this() {
      agentId_ = other.agentId_;
      clusterToken_ = other.clusterToken_;
      workflowId_ = other.workflowId_;
      nodeId_ = other.nodeId_;
      result_ = other.result_ != null ? other.result_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ReportNodeResultRequest Clone() {
      return new ReportNodeResultRequest(this);
    }

    /// <summary>Field number for the "agent_id" field.</summary>
    public const int AgentIdFieldNumber = 1;
    private string agentId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string AgentId {
      get { return agentId_; }
      set {
        agentId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "cluster_token" field.</summary>
    public const int ClusterTokenFieldNumber = 2;
    private string clusterToken_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string ClusterToken {
      get { return clusterToken_; }
      set {
        clusterToken_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "workflow_id" field.</summary>
    public const int WorkflowIdFieldNumber = 3;
    private string workflowId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string WorkflowId {
      get { return workflowId_; }
      set {
        workflowId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "node_id" field.</summary>
    public const int NodeIdFieldNumber = 4;
    private string nodeId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string NodeId {
      get { return nodeId_; }
      set {
        nodeId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "result" field.</summary>
    public const int ResultFieldNumber = 5;
    private global::HexaGrid.CLI.Grpc.NodeExecutionResult result_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::HexaGrid.CLI.Grpc.NodeExecutionResult Result {
      get { return result_; }
      set {
        result_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as ReportNodeResultRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(ReportNodeResultRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (AgentId != other.AgentId) return false;
      if (ClusterToken != other.ClusterToken) return false;
      if (WorkflowId != other.WorkflowId) return false;
      if (NodeId != other.NodeId) return false;
      if (!object.Equals(Result, other.Result)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (AgentId.Length != 0) hash ^= AgentId.GetHashCode();
      if (ClusterToken.Length != 0) hash ^= ClusterToken.GetHashCode();
      if (WorkflowId.Length != 0) hash ^= WorkflowId.GetHashCode();
      if (NodeId.Length != 0) hash ^= NodeId.GetHashCode();
      if (result_ != null) hash ^= Result.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (AgentId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(AgentId);
      }
      if (ClusterToken.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(ClusterToken);
      }
      if (WorkflowId.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(WorkflowId);
      }
      if (NodeId.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(NodeId);
      }
      if (result_ != null) {
        output.WriteRawTag(42);
        output.WriteMessage(Result);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (AgentId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(AgentId);
      }
      if (ClusterToken.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(ClusterToken);
      }
      if (WorkflowId.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(WorkflowId);
      }
      if (NodeId.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(NodeId);
      }
      if (result_ != null) {
        output.WriteRawTag(42);
        output.WriteMessage(Result);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (AgentId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(AgentId);
      }
      if (ClusterToken.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ClusterToken);
      }
      if (WorkflowId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(WorkflowId);
      }
      if (NodeId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(NodeId);
      }
      if (result_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Result);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(ReportNodeResultRequest other) {
      if (other == null) {
        return;
      }
      if (other.AgentId.Length != 0) {
        AgentId = other.AgentId;
      }
      if (other.ClusterToken.Length != 0) {
        ClusterToken = other.ClusterToken;
      }
      if (other.WorkflowId.Length != 0) {
        WorkflowId = other.WorkflowId;
      }
      if (other.NodeId.Length != 0) {
        NodeId = other.NodeId;
      }
      if (other.result_ != null) {
        if (result_ == null) {
          Result = new global::HexaGrid.CLI.Grpc.NodeExecutionResult();
        }
        Result.MergeFrom(other.Result);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            AgentId = input.ReadString();
            break;
          }
          case 18: {
            ClusterToken = input.ReadString();
            break;
          }
          case 26: {
            WorkflowId = input.ReadString();
            break;
          }
          case 34: {
            NodeId = input.ReadString();
            break;
          }
          case 42: {
            if (result_ == null) {
              Result = new global::HexaGrid.CLI.Grpc.NodeExecutionResult();
            }
            input.ReadMessage(Result);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            AgentId = input.ReadString();
            break;
          }
          case 18: {
            ClusterToken = input.ReadString();
            break;
          }
          case 26: {
            WorkflowId = input.ReadString();
            break;
          }
          case 34: {
            NodeId = input.ReadString();
            break;
          }
          case 42: {
            if (result_ == null) {
              Result = new global::HexaGrid.CLI.Grpc.NodeExecutionResult();
            }
            input.ReadMessage(Result);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class ReportNodeResultResponse : pb::IMessage<ReportNodeResultResponse>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<ReportNodeResultResponse> _parser = new pb::MessageParser<ReportNodeResultResponse>(() => new ReportNodeResultResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<ReportNodeResultResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::HexaGrid.CLI.Grpc.HexagridReflection.Descriptor.MessageTypes[28]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ReportNodeResultResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ReportNodeResultResponse(ReportNodeResultResponse other) : this() {
      success_ = other.success_;
      errorMessage_ = other.errorMessage_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ReportNodeResultResponse Clone() {
      return new ReportNodeResultResponse(this);
    }

    /// <summary>Field number for the "success" field.</summary>
    public const int SuccessFieldNumber = 1;
    private bool success_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Success {
      get { return success_; }
      set {
        success_ = value;
      }
    }

    /// <summary>Field number for the "error_message" field.</summary>
    public const int ErrorMessageFieldNumber = 2;
    private string errorMessage_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string ErrorMessage {
      get { return errorMessage_; }
      set {
        errorMessage_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as ReportNodeResultResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(ReportNodeResultResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Success != other.Success) return false;
      if (ErrorMessage != other.ErrorMessage) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Success != false) hash ^= Success.GetHashCode();
      if (ErrorMessage.Length != 0) hash ^= ErrorMessage.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Success != false) {
        output.WriteRawTag(8);
        output.WriteBool(Success);
      }
      if (ErrorMessage.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(ErrorMessage);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Success != false) {
        output.WriteRawTag(8);
        output.WriteBool(Success);
      }
      if (ErrorMessage.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(ErrorMessage);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Success != false) {
        size += 1 + 1;
      }
      if (ErrorMessage.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ErrorMessage);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(ReportNodeResultResponse other) {
      if (other == null) {
        return;
      }
      if (other.Success != false) {
        Success = other.Success;
      }
      if (other.ErrorMessage.Length != 0) {
        ErrorMessage = other.ErrorMessage;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Success = input.ReadBool();
            break;
          }
          case 18: {
            ErrorMessage = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Success = input.ReadBool();
            break;
          }
          case 18: {
            ErrorMessage = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class NodeExecutionResult : pb::IMessage<NodeExecutionResult>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<NodeExecutionResult> _parser = new pb::MessageParser<NodeExecutionResult>(() => new NodeExecutionResult());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<NodeExecutionResult> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::HexaGrid.CLI.Grpc.HexagridReflection.Descriptor.MessageTypes[29]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public NodeExecutionResult() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public NodeExecutionResult(NodeExecutionResult other) : this() {
      success_ = other.success_;
      errorMessage_ = other.errorMessage_;
      outputs_ = other.outputs_.Clone();
      startedAt_ = other.startedAt_;
      completedAt_ = other.completedAt_;
      attemptCount_ = other.attemptCount_;
      logs_ = other.logs_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public NodeExecutionResult Clone() {
      return new NodeExecutionResult(this);
    }

    /// <summary>Field number for the "success" field.</summary>
    public const int SuccessFieldNumber = 1;
    private bool success_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Success {
      get { return success_; }
      set {
        success_ = value;
      }
    }

    /// <summary>Field number for the "error_message" field.</summary>
    public const int ErrorMessageFieldNumber = 2;
    private string errorMessage_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string ErrorMessage {
      get { return errorMessage_; }
      set {
        errorMessage_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "outputs" field.</summary>
    public const int OutputsFieldNumber = 3;
    private static readonly pbc::MapField<string, string>.Codec _map_outputs_codec
        = new pbc::MapField<string, string>.Codec(pb::FieldCodec.ForString(10, ""), pb::FieldCodec.ForString(18, ""), 26);
    private readonly pbc::MapField<string, string> outputs_ = new pbc::MapField<string, string>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<string, string> Outputs {
      get { return outputs_; }
    }

    /// <summary>Field number for the "started_at" field.</summary>
    public const int StartedAtFieldNumber = 4;
    private long startedAt_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long StartedAt {
      get { return startedAt_; }
      set {
        startedAt_ = value;
      }
    }

    /// <summary>Field number for the "completed_at" field.</summary>
    public const int CompletedAtFieldNumber = 5;
    private long completedAt_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long CompletedAt {
      get { return completedAt_; }
      set {
        completedAt_ = value;
      }
    }

    /// <summary>Field number for the "attempt_count" field.</summary>
    public const int AttemptCountFieldNumber = 6;
    private int attemptCount_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int AttemptCount {
      get { return attemptCount_; }
      set {
        attemptCount_ = value;
      }
    }

    /// <summary>Field number for the "logs" field.</summary>
    public const int LogsFieldNumber = 7;
    private static readonly pb::FieldCodec<string> _repeated_logs_codec
        = pb::FieldCodec.ForString(58);
    private readonly pbc::RepeatedField<string> logs_ = new pbc::RepeatedField<string>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<string> Logs {
      get { return logs_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as NodeExecutionResult);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(NodeExecutionResult other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Success != other.Success) return false;
      if (ErrorMessage != other.ErrorMessage) return false;
      if (!Outputs.Equals(other.Outputs)) return false;
      if (StartedAt != other.StartedAt) return false;
      if (CompletedAt != other.CompletedAt) return false;
      if (AttemptCount != other.AttemptCount) return false;
      if(!logs_.Equals(other.logs_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Success != false) hash ^= Success.GetHashCode();
      if (ErrorMessage.Length != 0) hash ^= ErrorMessage.GetHashCode();
      hash ^= Outputs.GetHashCode();
      if (StartedAt != 0L) hash ^= StartedAt.GetHashCode();
      if (CompletedAt != 0L) hash ^= CompletedAt.GetHashCode();
      if (AttemptCount != 0) hash ^= AttemptCount.GetHashCode();
      hash ^= logs_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Success != false) {
        output.WriteRawTag(8);
        output.WriteBool(Success);
      }
      if (ErrorMessage.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(ErrorMessage);
      }
      outputs_.WriteTo(output, _map_outputs_codec);
      if (StartedAt != 0L) {
        output.WriteRawTag(32);
        output.WriteInt64(StartedAt);
      }
      if (CompletedAt != 0L) {
        output.WriteRawTag(40);
        output.WriteInt64(CompletedAt);
      }
      if (AttemptCount != 0) {
        output.WriteRawTag(48);
        output.WriteInt32(AttemptCount);
      }
      logs_.WriteTo(output, _repeated_logs_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Success != false) {
        output.WriteRawTag(8);
        output.WriteBool(Success);
      }
      if (ErrorMessage.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(ErrorMessage);
      }
      outputs_.WriteTo(ref output, _map_outputs_codec);
      if (StartedAt != 0L) {
        output.WriteRawTag(32);
        output.WriteInt64(StartedAt);
      }
      if (CompletedAt != 0L) {
        output.WriteRawTag(40);
        output.WriteInt64(CompletedAt);
      }
      if (AttemptCount != 0) {
        output.WriteRawTag(48);
        output.WriteInt32(AttemptCount);
      }
      logs_.WriteTo(ref output, _repeated_logs_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Success != false) {
        size += 1 + 1;
      }
      if (ErrorMessage.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ErrorMessage);
      }
      size += outputs_.CalculateSize(_map_outputs_codec);
      if (StartedAt != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(StartedAt);
      }
      if (CompletedAt != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(CompletedAt);
      }
      if (AttemptCount != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(AttemptCount);
      }
      size += logs_.CalculateSize(_repeated_logs_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(NodeExecutionResult other) {
      if (other == null) {
        return;
      }
      if (other.Success != false) {
        Success = other.Success;
      }
      if (other.ErrorMessage.Length != 0) {
        ErrorMessage = other.ErrorMessage;
      }
      outputs_.MergeFrom(other.outputs_);
      if (other.StartedAt != 0L) {
        StartedAt = other.StartedAt;
      }
      if (other.CompletedAt != 0L) {
        CompletedAt = other.CompletedAt;
      }
      if (other.AttemptCount != 0) {
        AttemptCount = other.AttemptCount;
      }
      logs_.Add(other.logs_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Success = input.ReadBool();
            break;
          }
          case 18: {
            ErrorMessage = input.ReadString();
            break;
          }
          case 26: {
            outputs_.AddEntriesFrom(input, _map_outputs_codec);
            break;
          }
          case 32: {
            StartedAt = input.ReadInt64();
            break;
          }
          case 40: {
            CompletedAt = input.ReadInt64();
            break;
          }
          case 48: {
            AttemptCount = input.ReadInt32();
            break;
          }
          case 58: {
            logs_.AddEntriesFrom(input, _repeated_logs_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Success = input.ReadBool();
            break;
          }
          case 18: {
            ErrorMessage = input.ReadString();
            break;
          }
          case 26: {
            outputs_.AddEntriesFrom(ref input, _map_outputs_codec);
            break;
          }
          case 32: {
            StartedAt = input.ReadInt64();
            break;
          }
          case 40: {
            CompletedAt = input.ReadInt64();
            break;
          }
          case 48: {
            AttemptCount = input.ReadInt32();
            break;
          }
          case 58: {
            logs_.AddEntriesFrom(ref input, _repeated_logs_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// Node Execution (Agent-to-Agent)
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class ExecuteNodeRequest : pb::IMessage<ExecuteNodeRequest>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<ExecuteNodeRequest> _parser = new pb::MessageParser<ExecuteNodeRequest>(() => new ExecuteNodeRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<ExecuteNodeRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::HexaGrid.CLI.Grpc.HexagridReflection.Descriptor.MessageTypes[30]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ExecuteNodeRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ExecuteNodeRequest(ExecuteNodeRequest other) : this() {
      workflowId_ = other.workflowId_;
      nodeId_ = other.nodeId_;
      nodeDefinition_ = other.nodeDefinition_ != null ? other.nodeDefinition_.Clone() : null;
      context_ = other.context_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ExecuteNodeRequest Clone() {
      return new ExecuteNodeRequest(this);
    }

    /// <summary>Field number for the "workflow_id" field.</summary>
    public const int WorkflowIdFieldNumber = 1;
    private string workflowId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string WorkflowId {
      get { return workflowId_; }
      set {
        workflowId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "node_id" field.</summary>
    public const int NodeIdFieldNumber = 2;
    private string nodeId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string NodeId {
      get { return nodeId_; }
      set {
        nodeId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "node_definition" field.</summary>
    public const int NodeDefinitionFieldNumber = 3;
    private global::HexaGrid.CLI.Grpc.NodeDefinition nodeDefinition_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::HexaGrid.CLI.Grpc.NodeDefinition NodeDefinition {
      get { return nodeDefinition_; }
      set {
        nodeDefinition_ = value;
      }
    }

    /// <summary>Field number for the "context" field.</summary>
    public const int ContextFieldNumber = 4;
    private static readonly pbc::MapField<string, string>.Codec _map_context_codec
        = new pbc::MapField<string, string>.Codec(pb::FieldCodec.ForString(10, ""), pb::FieldCodec.ForString(18, ""), 34);
    private readonly pbc::MapField<string, string> context_ = new pbc::MapField<string, string>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<string, string> Context {
      get { return context_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as ExecuteNodeRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(ExecuteNodeRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (WorkflowId != other.WorkflowId) return false;
      if (NodeId != other.NodeId) return false;
      if (!object.Equals(NodeDefinition, other.NodeDefinition)) return false;
      if (!Context.Equals(other.Context)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (WorkflowId.Length != 0) hash ^= WorkflowId.GetHashCode();
      if (NodeId.Length != 0) hash ^= NodeId.GetHashCode();
      if (nodeDefinition_ != null) hash ^= NodeDefinition.GetHashCode();
      hash ^= Context.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (WorkflowId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(WorkflowId);
      }
      if (NodeId.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(NodeId);
      }
      if (nodeDefinition_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(NodeDefinition);
      }
      context_.WriteTo(output, _map_context_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (WorkflowId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(WorkflowId);
      }
      if (NodeId.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(NodeId);
      }
      if (nodeDefinition_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(NodeDefinition);
      }
      context_.WriteTo(ref output, _map_context_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (WorkflowId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(WorkflowId);
      }
      if (NodeId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(NodeId);
      }
      if (nodeDefinition_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(NodeDefinition);
      }
      size += context_.CalculateSize(_map_context_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(ExecuteNodeRequest other) {
      if (other == null) {
        return;
      }
      if (other.WorkflowId.Length != 0) {
        WorkflowId = other.WorkflowId;
      }
      if (other.NodeId.Length != 0) {
        NodeId = other.NodeId;
      }
      if (other.nodeDefinition_ != null) {
        if (nodeDefinition_ == null) {
          NodeDefinition = new global::HexaGrid.CLI.Grpc.NodeDefinition();
        }
        NodeDefinition.MergeFrom(other.NodeDefinition);
      }
      context_.MergeFrom(other.context_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            WorkflowId = input.ReadString();
            break;
          }
          case 18: {
            NodeId = input.ReadString();
            break;
          }
          case 26: {
            if (nodeDefinition_ == null) {
              NodeDefinition = new global::HexaGrid.CLI.Grpc.NodeDefinition();
            }
            input.ReadMessage(NodeDefinition);
            break;
          }
          case 34: {
            context_.AddEntriesFrom(input, _map_context_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            WorkflowId = input.ReadString();
            break;
          }
          case 18: {
            NodeId = input.ReadString();
            break;
          }
          case 26: {
            if (nodeDefinition_ == null) {
              NodeDefinition = new global::HexaGrid.CLI.Grpc.NodeDefinition();
            }
            input.ReadMessage(NodeDefinition);
            break;
          }
          case 34: {
            context_.AddEntriesFrom(ref input, _map_context_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class ExecuteNodeResponse : pb::IMessage<ExecuteNodeResponse>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<ExecuteNodeResponse> _parser = new pb::MessageParser<ExecuteNodeResponse>(() => new ExecuteNodeResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<ExecuteNodeResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::HexaGrid.CLI.Grpc.HexagridReflection.Descriptor.MessageTypes[31]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ExecuteNodeResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ExecuteNodeResponse(ExecuteNodeResponse other) : this() {
      success_ = other.success_;
      errorMessage_ = other.errorMessage_;
      result_ = other.result_ != null ? other.result_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ExecuteNodeResponse Clone() {
      return new ExecuteNodeResponse(this);
    }

    /// <summary>Field number for the "success" field.</summary>
    public const int SuccessFieldNumber = 1;
    private bool success_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Success {
      get { return success_; }
      set {
        success_ = value;
      }
    }

    /// <summary>Field number for the "error_message" field.</summary>
    public const int ErrorMessageFieldNumber = 2;
    private string errorMessage_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string ErrorMessage {
      get { return errorMessage_; }
      set {
        errorMessage_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "result" field.</summary>
    public const int ResultFieldNumber = 3;
    private global::HexaGrid.CLI.Grpc.NodeExecutionResult result_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::HexaGrid.CLI.Grpc.NodeExecutionResult Result {
      get { return result_; }
      set {
        result_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as ExecuteNodeResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(ExecuteNodeResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Success != other.Success) return false;
      if (ErrorMessage != other.ErrorMessage) return false;
      if (!object.Equals(Result, other.Result)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Success != false) hash ^= Success.GetHashCode();
      if (ErrorMessage.Length != 0) hash ^= ErrorMessage.GetHashCode();
      if (result_ != null) hash ^= Result.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Success != false) {
        output.WriteRawTag(8);
        output.WriteBool(Success);
      }
      if (ErrorMessage.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(ErrorMessage);
      }
      if (result_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(Result);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Success != false) {
        output.WriteRawTag(8);
        output.WriteBool(Success);
      }
      if (ErrorMessage.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(ErrorMessage);
      }
      if (result_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(Result);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Success != false) {
        size += 1 + 1;
      }
      if (ErrorMessage.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ErrorMessage);
      }
      if (result_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Result);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(ExecuteNodeResponse other) {
      if (other == null) {
        return;
      }
      if (other.Success != false) {
        Success = other.Success;
      }
      if (other.ErrorMessage.Length != 0) {
        ErrorMessage = other.ErrorMessage;
      }
      if (other.result_ != null) {
        if (result_ == null) {
          Result = new global::HexaGrid.CLI.Grpc.NodeExecutionResult();
        }
        Result.MergeFrom(other.Result);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Success = input.ReadBool();
            break;
          }
          case 18: {
            ErrorMessage = input.ReadString();
            break;
          }
          case 26: {
            if (result_ == null) {
              Result = new global::HexaGrid.CLI.Grpc.NodeExecutionResult();
            }
            input.ReadMessage(Result);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Success = input.ReadBool();
            break;
          }
          case 18: {
            ErrorMessage = input.ReadString();
            break;
          }
          case 26: {
            if (result_ == null) {
              Result = new global::HexaGrid.CLI.Grpc.NodeExecutionResult();
            }
            input.ReadMessage(Result);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// Agent Status
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class GetAgentStatusRequest : pb::IMessage<GetAgentStatusRequest>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<GetAgentStatusRequest> _parser = new pb::MessageParser<GetAgentStatusRequest>(() => new GetAgentStatusRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<GetAgentStatusRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::HexaGrid.CLI.Grpc.HexagridReflection.Descriptor.MessageTypes[32]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetAgentStatusRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetAgentStatusRequest(GetAgentStatusRequest other) : this() {
      requestingAgent_ = other.requestingAgent_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetAgentStatusRequest Clone() {
      return new GetAgentStatusRequest(this);
    }

    /// <summary>Field number for the "requesting_agent" field.</summary>
    public const int RequestingAgentFieldNumber = 1;
    private string requestingAgent_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string RequestingAgent {
      get { return requestingAgent_; }
      set {
        requestingAgent_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as GetAgentStatusRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(GetAgentStatusRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (RequestingAgent != other.RequestingAgent) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (RequestingAgent.Length != 0) hash ^= RequestingAgent.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (RequestingAgent.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(RequestingAgent);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (RequestingAgent.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(RequestingAgent);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (RequestingAgent.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(RequestingAgent);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(GetAgentStatusRequest other) {
      if (other == null) {
        return;
      }
      if (other.RequestingAgent.Length != 0) {
        RequestingAgent = other.RequestingAgent;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            RequestingAgent = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            RequestingAgent = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class GetAgentStatusResponse : pb::IMessage<GetAgentStatusResponse>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<GetAgentStatusResponse> _parser = new pb::MessageParser<GetAgentStatusResponse>(() => new GetAgentStatusResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<GetAgentStatusResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::HexaGrid.CLI.Grpc.HexagridReflection.Descriptor.MessageTypes[33]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetAgentStatusResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetAgentStatusResponse(GetAgentStatusResponse other) : this() {
      success_ = other.success_;
      errorMessage_ = other.errorMessage_;
      status_ = other.status_ != null ? other.status_.Clone() : null;
      resources_ = other.resources_ != null ? other.resources_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetAgentStatusResponse Clone() {
      return new GetAgentStatusResponse(this);
    }

    /// <summary>Field number for the "success" field.</summary>
    public const int SuccessFieldNumber = 1;
    private bool success_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Success {
      get { return success_; }
      set {
        success_ = value;
      }
    }

    /// <summary>Field number for the "error_message" field.</summary>
    public const int ErrorMessageFieldNumber = 2;
    private string errorMessage_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string ErrorMessage {
      get { return errorMessage_; }
      set {
        errorMessage_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "status" field.</summary>
    public const int StatusFieldNumber = 3;
    private global::HexaGrid.CLI.Grpc.AgentStatus status_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::HexaGrid.CLI.Grpc.AgentStatus Status {
      get { return status_; }
      set {
        status_ = value;
      }
    }

    /// <summary>Field number for the "resources" field.</summary>
    public const int ResourcesFieldNumber = 4;
    private global::HexaGrid.CLI.Grpc.ResourceInfo resources_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::HexaGrid.CLI.Grpc.ResourceInfo Resources {
      get { return resources_; }
      set {
        resources_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as GetAgentStatusResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(GetAgentStatusResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Success != other.Success) return false;
      if (ErrorMessage != other.ErrorMessage) return false;
      if (!object.Equals(Status, other.Status)) return false;
      if (!object.Equals(Resources, other.Resources)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Success != false) hash ^= Success.GetHashCode();
      if (ErrorMessage.Length != 0) hash ^= ErrorMessage.GetHashCode();
      if (status_ != null) hash ^= Status.GetHashCode();
      if (resources_ != null) hash ^= Resources.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Success != false) {
        output.WriteRawTag(8);
        output.WriteBool(Success);
      }
      if (ErrorMessage.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(ErrorMessage);
      }
      if (status_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(Status);
      }
      if (resources_ != null) {
        output.WriteRawTag(34);
        output.WriteMessage(Resources);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Success != false) {
        output.WriteRawTag(8);
        output.WriteBool(Success);
      }
      if (ErrorMessage.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(ErrorMessage);
      }
      if (status_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(Status);
      }
      if (resources_ != null) {
        output.WriteRawTag(34);
        output.WriteMessage(Resources);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Success != false) {
        size += 1 + 1;
      }
      if (ErrorMessage.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ErrorMessage);
      }
      if (status_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Status);
      }
      if (resources_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Resources);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(GetAgentStatusResponse other) {
      if (other == null) {
        return;
      }
      if (other.Success != false) {
        Success = other.Success;
      }
      if (other.ErrorMessage.Length != 0) {
        ErrorMessage = other.ErrorMessage;
      }
      if (other.status_ != null) {
        if (status_ == null) {
          Status = new global::HexaGrid.CLI.Grpc.AgentStatus();
        }
        Status.MergeFrom(other.Status);
      }
      if (other.resources_ != null) {
        if (resources_ == null) {
          Resources = new global::HexaGrid.CLI.Grpc.ResourceInfo();
        }
        Resources.MergeFrom(other.Resources);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Success = input.ReadBool();
            break;
          }
          case 18: {
            ErrorMessage = input.ReadString();
            break;
          }
          case 26: {
            if (status_ == null) {
              Status = new global::HexaGrid.CLI.Grpc.AgentStatus();
            }
            input.ReadMessage(Status);
            break;
          }
          case 34: {
            if (resources_ == null) {
              Resources = new global::HexaGrid.CLI.Grpc.ResourceInfo();
            }
            input.ReadMessage(Resources);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Success = input.ReadBool();
            break;
          }
          case 18: {
            ErrorMessage = input.ReadString();
            break;
          }
          case 26: {
            if (status_ == null) {
              Status = new global::HexaGrid.CLI.Grpc.AgentStatus();
            }
            input.ReadMessage(Status);
            break;
          }
          case 34: {
            if (resources_ == null) {
              Resources = new global::HexaGrid.CLI.Grpc.ResourceInfo();
            }
            input.ReadMessage(Resources);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// Cluster Information
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class GetClusterInfoRequest : pb::IMessage<GetClusterInfoRequest>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<GetClusterInfoRequest> _parser = new pb::MessageParser<GetClusterInfoRequest>(() => new GetClusterInfoRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<GetClusterInfoRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::HexaGrid.CLI.Grpc.HexagridReflection.Descriptor.MessageTypes[34]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetClusterInfoRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetClusterInfoRequest(GetClusterInfoRequest other) : this() {
      clusterToken_ = other.clusterToken_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetClusterInfoRequest Clone() {
      return new GetClusterInfoRequest(this);
    }

    /// <summary>Field number for the "cluster_token" field.</summary>
    public const int ClusterTokenFieldNumber = 1;
    private string clusterToken_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string ClusterToken {
      get { return clusterToken_; }
      set {
        clusterToken_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as GetClusterInfoRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(GetClusterInfoRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (ClusterToken != other.ClusterToken) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (ClusterToken.Length != 0) hash ^= ClusterToken.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (ClusterToken.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(ClusterToken);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (ClusterToken.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(ClusterToken);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (ClusterToken.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ClusterToken);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(GetClusterInfoRequest other) {
      if (other == null) {
        return;
      }
      if (other.ClusterToken.Length != 0) {
        ClusterToken = other.ClusterToken;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            ClusterToken = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            ClusterToken = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class GetClusterInfoResponse : pb::IMessage<GetClusterInfoResponse>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<GetClusterInfoResponse> _parser = new pb::MessageParser<GetClusterInfoResponse>(() => new GetClusterInfoResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<GetClusterInfoResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::HexaGrid.CLI.Grpc.HexagridReflection.Descriptor.MessageTypes[35]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetClusterInfoResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetClusterInfoResponse(GetClusterInfoResponse other) : this() {
      success_ = other.success_;
      errorMessage_ = other.errorMessage_;
      clusterInfo_ = other.clusterInfo_ != null ? other.clusterInfo_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetClusterInfoResponse Clone() {
      return new GetClusterInfoResponse(this);
    }

    /// <summary>Field number for the "success" field.</summary>
    public const int SuccessFieldNumber = 1;
    private bool success_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Success {
      get { return success_; }
      set {
        success_ = value;
      }
    }

    /// <summary>Field number for the "error_message" field.</summary>
    public const int ErrorMessageFieldNumber = 2;
    private string errorMessage_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string ErrorMessage {
      get { return errorMessage_; }
      set {
        errorMessage_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "cluster_info" field.</summary>
    public const int ClusterInfoFieldNumber = 3;
    private global::HexaGrid.CLI.Grpc.ClusterInfo clusterInfo_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::HexaGrid.CLI.Grpc.ClusterInfo ClusterInfo {
      get { return clusterInfo_; }
      set {
        clusterInfo_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as GetClusterInfoResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(GetClusterInfoResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Success != other.Success) return false;
      if (ErrorMessage != other.ErrorMessage) return false;
      if (!object.Equals(ClusterInfo, other.ClusterInfo)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Success != false) hash ^= Success.GetHashCode();
      if (ErrorMessage.Length != 0) hash ^= ErrorMessage.GetHashCode();
      if (clusterInfo_ != null) hash ^= ClusterInfo.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Success != false) {
        output.WriteRawTag(8);
        output.WriteBool(Success);
      }
      if (ErrorMessage.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(ErrorMessage);
      }
      if (clusterInfo_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(ClusterInfo);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Success != false) {
        output.WriteRawTag(8);
        output.WriteBool(Success);
      }
      if (ErrorMessage.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(ErrorMessage);
      }
      if (clusterInfo_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(ClusterInfo);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Success != false) {
        size += 1 + 1;
      }
      if (ErrorMessage.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ErrorMessage);
      }
      if (clusterInfo_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(ClusterInfo);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(GetClusterInfoResponse other) {
      if (other == null) {
        return;
      }
      if (other.Success != false) {
        Success = other.Success;
      }
      if (other.ErrorMessage.Length != 0) {
        ErrorMessage = other.ErrorMessage;
      }
      if (other.clusterInfo_ != null) {
        if (clusterInfo_ == null) {
          ClusterInfo = new global::HexaGrid.CLI.Grpc.ClusterInfo();
        }
        ClusterInfo.MergeFrom(other.ClusterInfo);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Success = input.ReadBool();
            break;
          }
          case 18: {
            ErrorMessage = input.ReadString();
            break;
          }
          case 26: {
            if (clusterInfo_ == null) {
              ClusterInfo = new global::HexaGrid.CLI.Grpc.ClusterInfo();
            }
            input.ReadMessage(ClusterInfo);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Success = input.ReadBool();
            break;
          }
          case 18: {
            ErrorMessage = input.ReadString();
            break;
          }
          case 26: {
            if (clusterInfo_ == null) {
              ClusterInfo = new global::HexaGrid.CLI.Grpc.ClusterInfo();
            }
            input.ReadMessage(ClusterInfo);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class ClusterInfo : pb::IMessage<ClusterInfo>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<ClusterInfo> _parser = new pb::MessageParser<ClusterInfo>(() => new ClusterInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<ClusterInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::HexaGrid.CLI.Grpc.HexagridReflection.Descriptor.MessageTypes[36]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ClusterInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ClusterInfo(ClusterInfo other) : this() {
      clusterId_ = other.clusterId_;
      version_ = other.version_;
      totalAgents_ = other.totalAgents_;
      readyAgents_ = other.readyAgents_;
      activeWorkflows_ = other.activeWorkflows_;
      agents_ = other.agents_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ClusterInfo Clone() {
      return new ClusterInfo(this);
    }

    /// <summary>Field number for the "cluster_id" field.</summary>
    public const int ClusterIdFieldNumber = 1;
    private string clusterId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string ClusterId {
      get { return clusterId_; }
      set {
        clusterId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "version" field.</summary>
    public const int VersionFieldNumber = 2;
    private string version_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Version {
      get { return version_; }
      set {
        version_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "total_agents" field.</summary>
    public const int TotalAgentsFieldNumber = 3;
    private int totalAgents_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int TotalAgents {
      get { return totalAgents_; }
      set {
        totalAgents_ = value;
      }
    }

    /// <summary>Field number for the "ready_agents" field.</summary>
    public const int ReadyAgentsFieldNumber = 4;
    private int readyAgents_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int ReadyAgents {
      get { return readyAgents_; }
      set {
        readyAgents_ = value;
      }
    }

    /// <summary>Field number for the "active_workflows" field.</summary>
    public const int ActiveWorkflowsFieldNumber = 5;
    private int activeWorkflows_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int ActiveWorkflows {
      get { return activeWorkflows_; }
      set {
        activeWorkflows_ = value;
      }
    }

    /// <summary>Field number for the "agents" field.</summary>
    public const int AgentsFieldNumber = 6;
    private static readonly pb::FieldCodec<global::HexaGrid.CLI.Grpc.AgentInfo> _repeated_agents_codec
        = pb::FieldCodec.ForMessage(50, global::HexaGrid.CLI.Grpc.AgentInfo.Parser);
    private readonly pbc::RepeatedField<global::HexaGrid.CLI.Grpc.AgentInfo> agents_ = new pbc::RepeatedField<global::HexaGrid.CLI.Grpc.AgentInfo>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::HexaGrid.CLI.Grpc.AgentInfo> Agents {
      get { return agents_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as ClusterInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(ClusterInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (ClusterId != other.ClusterId) return false;
      if (Version != other.Version) return false;
      if (TotalAgents != other.TotalAgents) return false;
      if (ReadyAgents != other.ReadyAgents) return false;
      if (ActiveWorkflows != other.ActiveWorkflows) return false;
      if(!agents_.Equals(other.agents_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (ClusterId.Length != 0) hash ^= ClusterId.GetHashCode();
      if (Version.Length != 0) hash ^= Version.GetHashCode();
      if (TotalAgents != 0) hash ^= TotalAgents.GetHashCode();
      if (ReadyAgents != 0) hash ^= ReadyAgents.GetHashCode();
      if (ActiveWorkflows != 0) hash ^= ActiveWorkflows.GetHashCode();
      hash ^= agents_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (ClusterId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(ClusterId);
      }
      if (Version.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Version);
      }
      if (TotalAgents != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(TotalAgents);
      }
      if (ReadyAgents != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(ReadyAgents);
      }
      if (ActiveWorkflows != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(ActiveWorkflows);
      }
      agents_.WriteTo(output, _repeated_agents_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (ClusterId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(ClusterId);
      }
      if (Version.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Version);
      }
      if (TotalAgents != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(TotalAgents);
      }
      if (ReadyAgents != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(ReadyAgents);
      }
      if (ActiveWorkflows != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(ActiveWorkflows);
      }
      agents_.WriteTo(ref output, _repeated_agents_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (ClusterId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ClusterId);
      }
      if (Version.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Version);
      }
      if (TotalAgents != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(TotalAgents);
      }
      if (ReadyAgents != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(ReadyAgents);
      }
      if (ActiveWorkflows != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(ActiveWorkflows);
      }
      size += agents_.CalculateSize(_repeated_agents_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(ClusterInfo other) {
      if (other == null) {
        return;
      }
      if (other.ClusterId.Length != 0) {
        ClusterId = other.ClusterId;
      }
      if (other.Version.Length != 0) {
        Version = other.Version;
      }
      if (other.TotalAgents != 0) {
        TotalAgents = other.TotalAgents;
      }
      if (other.ReadyAgents != 0) {
        ReadyAgents = other.ReadyAgents;
      }
      if (other.ActiveWorkflows != 0) {
        ActiveWorkflows = other.ActiveWorkflows;
      }
      agents_.Add(other.agents_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            ClusterId = input.ReadString();
            break;
          }
          case 18: {
            Version = input.ReadString();
            break;
          }
          case 24: {
            TotalAgents = input.ReadInt32();
            break;
          }
          case 32: {
            ReadyAgents = input.ReadInt32();
            break;
          }
          case 40: {
            ActiveWorkflows = input.ReadInt32();
            break;
          }
          case 50: {
            agents_.AddEntriesFrom(input, _repeated_agents_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            ClusterId = input.ReadString();
            break;
          }
          case 18: {
            Version = input.ReadString();
            break;
          }
          case 24: {
            TotalAgents = input.ReadInt32();
            break;
          }
          case 32: {
            ReadyAgents = input.ReadInt32();
            break;
          }
          case 40: {
            ActiveWorkflows = input.ReadInt32();
            break;
          }
          case 50: {
            agents_.AddEntriesFrom(ref input, _repeated_agents_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
