// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: Protos/hexagrid.proto
// </auto-generated>
#pragma warning disable 0414, 1591, 8981, 0612
#region Designer generated code

using grpc = global::Grpc.Core;

namespace HexaGrid.CLI.Grpc {
  /// <summary>
  /// HexaGrid Controller Service
  /// </summary>
  public static partial class HexaGridController
  {
    static readonly string __ServiceName = "hexagrid.HexaGridController";

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static void __Helper_SerializeMessage(global::Google.Protobuf.IMessage message, grpc::SerializationContext context)
    {
      #if !GRPC_DISABLE_PROTOBUF_BUFFER_SERIALIZATION
      if (message is global::Google.Protobuf.IBufferMessage)
      {
        context.SetPayloadLength(message.CalculateSize());
        global::Google.Protobuf.MessageExtensions.WriteTo(message, context.GetBufferWriter());
        context.Complete();
        return;
      }
      #endif
      context.Complete(global::Google.Protobuf.MessageExtensions.ToByteArray(message));
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static class __Helper_MessageCache<T>
    {
      public static readonly bool IsBufferMessage = global::System.Reflection.IntrospectionExtensions.GetTypeInfo(typeof(global::Google.Protobuf.IBufferMessage)).IsAssignableFrom(typeof(T));
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static T __Helper_DeserializeMessage<T>(grpc::DeserializationContext context, global::Google.Protobuf.MessageParser<T> parser) where T : global::Google.Protobuf.IMessage<T>
    {
      #if !GRPC_DISABLE_PROTOBUF_BUFFER_SERIALIZATION
      if (__Helper_MessageCache<T>.IsBufferMessage)
      {
        return parser.ParseFrom(context.PayloadAsReadOnlySequence());
      }
      #endif
      return parser.ParseFrom(context.PayloadAsNewBuffer());
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::HexaGrid.CLI.Grpc.RegisterAgentRequest> __Marshaller_hexagrid_RegisterAgentRequest = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::HexaGrid.CLI.Grpc.RegisterAgentRequest.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::HexaGrid.CLI.Grpc.RegisterAgentResponse> __Marshaller_hexagrid_RegisterAgentResponse = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::HexaGrid.CLI.Grpc.RegisterAgentResponse.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::HexaGrid.CLI.Grpc.HeartbeatRequest> __Marshaller_hexagrid_HeartbeatRequest = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::HexaGrid.CLI.Grpc.HeartbeatRequest.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::HexaGrid.CLI.Grpc.HeartbeatResponse> __Marshaller_hexagrid_HeartbeatResponse = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::HexaGrid.CLI.Grpc.HeartbeatResponse.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::HexaGrid.CLI.Grpc.UnregisterAgentRequest> __Marshaller_hexagrid_UnregisterAgentRequest = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::HexaGrid.CLI.Grpc.UnregisterAgentRequest.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::HexaGrid.CLI.Grpc.UnregisterAgentResponse> __Marshaller_hexagrid_UnregisterAgentResponse = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::HexaGrid.CLI.Grpc.UnregisterAgentResponse.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::HexaGrid.CLI.Grpc.SubmitWorkflowRequest> __Marshaller_hexagrid_SubmitWorkflowRequest = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::HexaGrid.CLI.Grpc.SubmitWorkflowRequest.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::HexaGrid.CLI.Grpc.SubmitWorkflowResponse> __Marshaller_hexagrid_SubmitWorkflowResponse = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::HexaGrid.CLI.Grpc.SubmitWorkflowResponse.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::HexaGrid.CLI.Grpc.GetWorkflowStatusRequest> __Marshaller_hexagrid_GetWorkflowStatusRequest = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::HexaGrid.CLI.Grpc.GetWorkflowStatusRequest.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::HexaGrid.CLI.Grpc.GetWorkflowStatusResponse> __Marshaller_hexagrid_GetWorkflowStatusResponse = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::HexaGrid.CLI.Grpc.GetWorkflowStatusResponse.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::HexaGrid.CLI.Grpc.CancelWorkflowRequest> __Marshaller_hexagrid_CancelWorkflowRequest = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::HexaGrid.CLI.Grpc.CancelWorkflowRequest.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::HexaGrid.CLI.Grpc.CancelWorkflowResponse> __Marshaller_hexagrid_CancelWorkflowResponse = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::HexaGrid.CLI.Grpc.CancelWorkflowResponse.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::HexaGrid.CLI.Grpc.GetPendingWorkRequest> __Marshaller_hexagrid_GetPendingWorkRequest = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::HexaGrid.CLI.Grpc.GetPendingWorkRequest.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::HexaGrid.CLI.Grpc.GetPendingWorkResponse> __Marshaller_hexagrid_GetPendingWorkResponse = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::HexaGrid.CLI.Grpc.GetPendingWorkResponse.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::HexaGrid.CLI.Grpc.ReportNodeResultRequest> __Marshaller_hexagrid_ReportNodeResultRequest = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::HexaGrid.CLI.Grpc.ReportNodeResultRequest.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::HexaGrid.CLI.Grpc.ReportNodeResultResponse> __Marshaller_hexagrid_ReportNodeResultResponse = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::HexaGrid.CLI.Grpc.ReportNodeResultResponse.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::HexaGrid.CLI.Grpc.GetClusterInfoRequest> __Marshaller_hexagrid_GetClusterInfoRequest = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::HexaGrid.CLI.Grpc.GetClusterInfoRequest.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::HexaGrid.CLI.Grpc.GetClusterInfoResponse> __Marshaller_hexagrid_GetClusterInfoResponse = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::HexaGrid.CLI.Grpc.GetClusterInfoResponse.Parser));

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::HexaGrid.CLI.Grpc.RegisterAgentRequest, global::HexaGrid.CLI.Grpc.RegisterAgentResponse> __Method_RegisterAgent = new grpc::Method<global::HexaGrid.CLI.Grpc.RegisterAgentRequest, global::HexaGrid.CLI.Grpc.RegisterAgentResponse>(
        grpc::MethodType.Unary,
        __ServiceName,
        "RegisterAgent",
        __Marshaller_hexagrid_RegisterAgentRequest,
        __Marshaller_hexagrid_RegisterAgentResponse);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::HexaGrid.CLI.Grpc.HeartbeatRequest, global::HexaGrid.CLI.Grpc.HeartbeatResponse> __Method_SendHeartbeat = new grpc::Method<global::HexaGrid.CLI.Grpc.HeartbeatRequest, global::HexaGrid.CLI.Grpc.HeartbeatResponse>(
        grpc::MethodType.Unary,
        __ServiceName,
        "SendHeartbeat",
        __Marshaller_hexagrid_HeartbeatRequest,
        __Marshaller_hexagrid_HeartbeatResponse);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::HexaGrid.CLI.Grpc.UnregisterAgentRequest, global::HexaGrid.CLI.Grpc.UnregisterAgentResponse> __Method_UnregisterAgent = new grpc::Method<global::HexaGrid.CLI.Grpc.UnregisterAgentRequest, global::HexaGrid.CLI.Grpc.UnregisterAgentResponse>(
        grpc::MethodType.Unary,
        __ServiceName,
        "UnregisterAgent",
        __Marshaller_hexagrid_UnregisterAgentRequest,
        __Marshaller_hexagrid_UnregisterAgentResponse);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::HexaGrid.CLI.Grpc.SubmitWorkflowRequest, global::HexaGrid.CLI.Grpc.SubmitWorkflowResponse> __Method_SubmitWorkflow = new grpc::Method<global::HexaGrid.CLI.Grpc.SubmitWorkflowRequest, global::HexaGrid.CLI.Grpc.SubmitWorkflowResponse>(
        grpc::MethodType.Unary,
        __ServiceName,
        "SubmitWorkflow",
        __Marshaller_hexagrid_SubmitWorkflowRequest,
        __Marshaller_hexagrid_SubmitWorkflowResponse);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::HexaGrid.CLI.Grpc.GetWorkflowStatusRequest, global::HexaGrid.CLI.Grpc.GetWorkflowStatusResponse> __Method_GetWorkflowStatus = new grpc::Method<global::HexaGrid.CLI.Grpc.GetWorkflowStatusRequest, global::HexaGrid.CLI.Grpc.GetWorkflowStatusResponse>(
        grpc::MethodType.Unary,
        __ServiceName,
        "GetWorkflowStatus",
        __Marshaller_hexagrid_GetWorkflowStatusRequest,
        __Marshaller_hexagrid_GetWorkflowStatusResponse);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::HexaGrid.CLI.Grpc.CancelWorkflowRequest, global::HexaGrid.CLI.Grpc.CancelWorkflowResponse> __Method_CancelWorkflow = new grpc::Method<global::HexaGrid.CLI.Grpc.CancelWorkflowRequest, global::HexaGrid.CLI.Grpc.CancelWorkflowResponse>(
        grpc::MethodType.Unary,
        __ServiceName,
        "CancelWorkflow",
        __Marshaller_hexagrid_CancelWorkflowRequest,
        __Marshaller_hexagrid_CancelWorkflowResponse);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::HexaGrid.CLI.Grpc.GetPendingWorkRequest, global::HexaGrid.CLI.Grpc.GetPendingWorkResponse> __Method_GetPendingWork = new grpc::Method<global::HexaGrid.CLI.Grpc.GetPendingWorkRequest, global::HexaGrid.CLI.Grpc.GetPendingWorkResponse>(
        grpc::MethodType.Unary,
        __ServiceName,
        "GetPendingWork",
        __Marshaller_hexagrid_GetPendingWorkRequest,
        __Marshaller_hexagrid_GetPendingWorkResponse);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::HexaGrid.CLI.Grpc.ReportNodeResultRequest, global::HexaGrid.CLI.Grpc.ReportNodeResultResponse> __Method_ReportNodeResult = new grpc::Method<global::HexaGrid.CLI.Grpc.ReportNodeResultRequest, global::HexaGrid.CLI.Grpc.ReportNodeResultResponse>(
        grpc::MethodType.Unary,
        __ServiceName,
        "ReportNodeResult",
        __Marshaller_hexagrid_ReportNodeResultRequest,
        __Marshaller_hexagrid_ReportNodeResultResponse);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::HexaGrid.CLI.Grpc.GetClusterInfoRequest, global::HexaGrid.CLI.Grpc.GetClusterInfoResponse> __Method_GetClusterInfo = new grpc::Method<global::HexaGrid.CLI.Grpc.GetClusterInfoRequest, global::HexaGrid.CLI.Grpc.GetClusterInfoResponse>(
        grpc::MethodType.Unary,
        __ServiceName,
        "GetClusterInfo",
        __Marshaller_hexagrid_GetClusterInfoRequest,
        __Marshaller_hexagrid_GetClusterInfoResponse);

    /// <summary>Service descriptor</summary>
    public static global::Google.Protobuf.Reflection.ServiceDescriptor Descriptor
    {
      get { return global::HexaGrid.CLI.Grpc.HexagridReflection.Descriptor.Services[0]; }
    }

    /// <summary>Base class for server-side implementations of HexaGridController</summary>
    [grpc::BindServiceMethod(typeof(HexaGridController), "BindService")]
    public abstract partial class HexaGridControllerBase
    {
      /// <summary>
      /// Agent registration and heartbeat
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::HexaGrid.CLI.Grpc.RegisterAgentResponse> RegisterAgent(global::HexaGrid.CLI.Grpc.RegisterAgentRequest request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::HexaGrid.CLI.Grpc.HeartbeatResponse> SendHeartbeat(global::HexaGrid.CLI.Grpc.HeartbeatRequest request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::HexaGrid.CLI.Grpc.UnregisterAgentResponse> UnregisterAgent(global::HexaGrid.CLI.Grpc.UnregisterAgentRequest request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// Workflow management
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::HexaGrid.CLI.Grpc.SubmitWorkflowResponse> SubmitWorkflow(global::HexaGrid.CLI.Grpc.SubmitWorkflowRequest request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::HexaGrid.CLI.Grpc.GetWorkflowStatusResponse> GetWorkflowStatus(global::HexaGrid.CLI.Grpc.GetWorkflowStatusRequest request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::HexaGrid.CLI.Grpc.CancelWorkflowResponse> CancelWorkflow(global::HexaGrid.CLI.Grpc.CancelWorkflowRequest request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// Node execution
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::HexaGrid.CLI.Grpc.GetPendingWorkResponse> GetPendingWork(global::HexaGrid.CLI.Grpc.GetPendingWorkRequest request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::HexaGrid.CLI.Grpc.ReportNodeResultResponse> ReportNodeResult(global::HexaGrid.CLI.Grpc.ReportNodeResultRequest request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// Cluster information
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::HexaGrid.CLI.Grpc.GetClusterInfoResponse> GetClusterInfo(global::HexaGrid.CLI.Grpc.GetClusterInfoRequest request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

    }

    /// <summary>Client for HexaGridController</summary>
    public partial class HexaGridControllerClient : grpc::ClientBase<HexaGridControllerClient>
    {
      /// <summary>Creates a new client for HexaGridController</summary>
      /// <param name="channel">The channel to use to make remote calls.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public HexaGridControllerClient(grpc::ChannelBase channel) : base(channel)
      {
      }
      /// <summary>Creates a new client for HexaGridController that uses a custom <c>CallInvoker</c>.</summary>
      /// <param name="callInvoker">The callInvoker to use to make remote calls.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public HexaGridControllerClient(grpc::CallInvoker callInvoker) : base(callInvoker)
      {
      }
      /// <summary>Protected parameterless constructor to allow creation of test doubles.</summary>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected HexaGridControllerClient() : base()
      {
      }
      /// <summary>Protected constructor to allow creation of configured clients.</summary>
      /// <param name="configuration">The client configuration.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected HexaGridControllerClient(ClientBaseConfiguration configuration) : base(configuration)
      {
      }

      /// <summary>
      /// Agent registration and heartbeat
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::HexaGrid.CLI.Grpc.RegisterAgentResponse RegisterAgent(global::HexaGrid.CLI.Grpc.RegisterAgentRequest request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return RegisterAgent(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// Agent registration and heartbeat
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::HexaGrid.CLI.Grpc.RegisterAgentResponse RegisterAgent(global::HexaGrid.CLI.Grpc.RegisterAgentRequest request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_RegisterAgent, null, options, request);
      }
      /// <summary>
      /// Agent registration and heartbeat
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::HexaGrid.CLI.Grpc.RegisterAgentResponse> RegisterAgentAsync(global::HexaGrid.CLI.Grpc.RegisterAgentRequest request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return RegisterAgentAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// Agent registration and heartbeat
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::HexaGrid.CLI.Grpc.RegisterAgentResponse> RegisterAgentAsync(global::HexaGrid.CLI.Grpc.RegisterAgentRequest request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_RegisterAgent, null, options, request);
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::HexaGrid.CLI.Grpc.HeartbeatResponse SendHeartbeat(global::HexaGrid.CLI.Grpc.HeartbeatRequest request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return SendHeartbeat(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::HexaGrid.CLI.Grpc.HeartbeatResponse SendHeartbeat(global::HexaGrid.CLI.Grpc.HeartbeatRequest request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_SendHeartbeat, null, options, request);
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::HexaGrid.CLI.Grpc.HeartbeatResponse> SendHeartbeatAsync(global::HexaGrid.CLI.Grpc.HeartbeatRequest request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return SendHeartbeatAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::HexaGrid.CLI.Grpc.HeartbeatResponse> SendHeartbeatAsync(global::HexaGrid.CLI.Grpc.HeartbeatRequest request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_SendHeartbeat, null, options, request);
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::HexaGrid.CLI.Grpc.UnregisterAgentResponse UnregisterAgent(global::HexaGrid.CLI.Grpc.UnregisterAgentRequest request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return UnregisterAgent(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::HexaGrid.CLI.Grpc.UnregisterAgentResponse UnregisterAgent(global::HexaGrid.CLI.Grpc.UnregisterAgentRequest request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_UnregisterAgent, null, options, request);
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::HexaGrid.CLI.Grpc.UnregisterAgentResponse> UnregisterAgentAsync(global::HexaGrid.CLI.Grpc.UnregisterAgentRequest request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return UnregisterAgentAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::HexaGrid.CLI.Grpc.UnregisterAgentResponse> UnregisterAgentAsync(global::HexaGrid.CLI.Grpc.UnregisterAgentRequest request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_UnregisterAgent, null, options, request);
      }
      /// <summary>
      /// Workflow management
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::HexaGrid.CLI.Grpc.SubmitWorkflowResponse SubmitWorkflow(global::HexaGrid.CLI.Grpc.SubmitWorkflowRequest request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return SubmitWorkflow(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// Workflow management
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::HexaGrid.CLI.Grpc.SubmitWorkflowResponse SubmitWorkflow(global::HexaGrid.CLI.Grpc.SubmitWorkflowRequest request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_SubmitWorkflow, null, options, request);
      }
      /// <summary>
      /// Workflow management
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::HexaGrid.CLI.Grpc.SubmitWorkflowResponse> SubmitWorkflowAsync(global::HexaGrid.CLI.Grpc.SubmitWorkflowRequest request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return SubmitWorkflowAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// Workflow management
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::HexaGrid.CLI.Grpc.SubmitWorkflowResponse> SubmitWorkflowAsync(global::HexaGrid.CLI.Grpc.SubmitWorkflowRequest request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_SubmitWorkflow, null, options, request);
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::HexaGrid.CLI.Grpc.GetWorkflowStatusResponse GetWorkflowStatus(global::HexaGrid.CLI.Grpc.GetWorkflowStatusRequest request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetWorkflowStatus(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::HexaGrid.CLI.Grpc.GetWorkflowStatusResponse GetWorkflowStatus(global::HexaGrid.CLI.Grpc.GetWorkflowStatusRequest request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_GetWorkflowStatus, null, options, request);
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::HexaGrid.CLI.Grpc.GetWorkflowStatusResponse> GetWorkflowStatusAsync(global::HexaGrid.CLI.Grpc.GetWorkflowStatusRequest request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetWorkflowStatusAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::HexaGrid.CLI.Grpc.GetWorkflowStatusResponse> GetWorkflowStatusAsync(global::HexaGrid.CLI.Grpc.GetWorkflowStatusRequest request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_GetWorkflowStatus, null, options, request);
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::HexaGrid.CLI.Grpc.CancelWorkflowResponse CancelWorkflow(global::HexaGrid.CLI.Grpc.CancelWorkflowRequest request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return CancelWorkflow(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::HexaGrid.CLI.Grpc.CancelWorkflowResponse CancelWorkflow(global::HexaGrid.CLI.Grpc.CancelWorkflowRequest request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_CancelWorkflow, null, options, request);
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::HexaGrid.CLI.Grpc.CancelWorkflowResponse> CancelWorkflowAsync(global::HexaGrid.CLI.Grpc.CancelWorkflowRequest request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return CancelWorkflowAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::HexaGrid.CLI.Grpc.CancelWorkflowResponse> CancelWorkflowAsync(global::HexaGrid.CLI.Grpc.CancelWorkflowRequest request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_CancelWorkflow, null, options, request);
      }
      /// <summary>
      /// Node execution
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::HexaGrid.CLI.Grpc.GetPendingWorkResponse GetPendingWork(global::HexaGrid.CLI.Grpc.GetPendingWorkRequest request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetPendingWork(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// Node execution
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::HexaGrid.CLI.Grpc.GetPendingWorkResponse GetPendingWork(global::HexaGrid.CLI.Grpc.GetPendingWorkRequest request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_GetPendingWork, null, options, request);
      }
      /// <summary>
      /// Node execution
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::HexaGrid.CLI.Grpc.GetPendingWorkResponse> GetPendingWorkAsync(global::HexaGrid.CLI.Grpc.GetPendingWorkRequest request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetPendingWorkAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// Node execution
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::HexaGrid.CLI.Grpc.GetPendingWorkResponse> GetPendingWorkAsync(global::HexaGrid.CLI.Grpc.GetPendingWorkRequest request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_GetPendingWork, null, options, request);
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::HexaGrid.CLI.Grpc.ReportNodeResultResponse ReportNodeResult(global::HexaGrid.CLI.Grpc.ReportNodeResultRequest request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return ReportNodeResult(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::HexaGrid.CLI.Grpc.ReportNodeResultResponse ReportNodeResult(global::HexaGrid.CLI.Grpc.ReportNodeResultRequest request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_ReportNodeResult, null, options, request);
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::HexaGrid.CLI.Grpc.ReportNodeResultResponse> ReportNodeResultAsync(global::HexaGrid.CLI.Grpc.ReportNodeResultRequest request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return ReportNodeResultAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::HexaGrid.CLI.Grpc.ReportNodeResultResponse> ReportNodeResultAsync(global::HexaGrid.CLI.Grpc.ReportNodeResultRequest request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_ReportNodeResult, null, options, request);
      }
      /// <summary>
      /// Cluster information
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::HexaGrid.CLI.Grpc.GetClusterInfoResponse GetClusterInfo(global::HexaGrid.CLI.Grpc.GetClusterInfoRequest request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetClusterInfo(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// Cluster information
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The response received from the server.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::HexaGrid.CLI.Grpc.GetClusterInfoResponse GetClusterInfo(global::HexaGrid.CLI.Grpc.GetClusterInfoRequest request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_GetClusterInfo, null, options, request);
      }
      /// <summary>
      /// Cluster information
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="headers">The initial metadata to send with the call. This parameter is optional.</param>
      /// <param name="deadline">An optional deadline for the call. The call will be cancelled if deadline is hit.</param>
      /// <param name="cancellationToken">An optional token for canceling the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::HexaGrid.CLI.Grpc.GetClusterInfoResponse> GetClusterInfoAsync(global::HexaGrid.CLI.Grpc.GetClusterInfoRequest request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetClusterInfoAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      /// <summary>
      /// Cluster information
      /// </summary>
      /// <param name="request">The request to send to the server.</param>
      /// <param name="options">The options for the call.</param>
      /// <returns>The call object.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::HexaGrid.CLI.Grpc.GetClusterInfoResponse> GetClusterInfoAsync(global::HexaGrid.CLI.Grpc.GetClusterInfoRequest request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_GetClusterInfo, null, options, request);
      }
      /// <summary>Creates a new instance of client from given <c>ClientBaseConfiguration</c>.</summary>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected override HexaGridControllerClient NewInstance(ClientBaseConfiguration configuration)
      {
        return new HexaGridControllerClient(configuration);
      }
    }

    /// <summary>Creates service definition that can be registered with a server</summary>
    /// <param name="serviceImpl">An object implementing the server-side handling logic.</param>
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    public static grpc::ServerServiceDefinition BindService(HexaGridControllerBase serviceImpl)
    {
      return grpc::ServerServiceDefinition.CreateBuilder()
          .AddMethod(__Method_RegisterAgent, serviceImpl.RegisterAgent)
          .AddMethod(__Method_SendHeartbeat, serviceImpl.SendHeartbeat)
          .AddMethod(__Method_UnregisterAgent, serviceImpl.UnregisterAgent)
          .AddMethod(__Method_SubmitWorkflow, serviceImpl.SubmitWorkflow)
          .AddMethod(__Method_GetWorkflowStatus, serviceImpl.GetWorkflowStatus)
          .AddMethod(__Method_CancelWorkflow, serviceImpl.CancelWorkflow)
          .AddMethod(__Method_GetPendingWork, serviceImpl.GetPendingWork)
          .AddMethod(__Method_ReportNodeResult, serviceImpl.ReportNodeResult)
          .AddMethod(__Method_GetClusterInfo, serviceImpl.GetClusterInfo).Build();
    }

    /// <summary>Register service method with a service binder with or without implementation. Useful when customizing the service binding logic.
    /// Note: this method is part of an experimental API that can change or be removed without any prior notice.</summary>
    /// <param name="serviceBinder">Service methods will be bound by calling <c>AddMethod</c> on this object.</param>
    /// <param name="serviceImpl">An object implementing the server-side handling logic.</param>
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    public static void BindService(grpc::ServiceBinderBase serviceBinder, HexaGridControllerBase serviceImpl)
    {
      serviceBinder.AddMethod(__Method_RegisterAgent, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::HexaGrid.CLI.Grpc.RegisterAgentRequest, global::HexaGrid.CLI.Grpc.RegisterAgentResponse>(serviceImpl.RegisterAgent));
      serviceBinder.AddMethod(__Method_SendHeartbeat, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::HexaGrid.CLI.Grpc.HeartbeatRequest, global::HexaGrid.CLI.Grpc.HeartbeatResponse>(serviceImpl.SendHeartbeat));
      serviceBinder.AddMethod(__Method_UnregisterAgent, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::HexaGrid.CLI.Grpc.UnregisterAgentRequest, global::HexaGrid.CLI.Grpc.UnregisterAgentResponse>(serviceImpl.UnregisterAgent));
      serviceBinder.AddMethod(__Method_SubmitWorkflow, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::HexaGrid.CLI.Grpc.SubmitWorkflowRequest, global::HexaGrid.CLI.Grpc.SubmitWorkflowResponse>(serviceImpl.SubmitWorkflow));
      serviceBinder.AddMethod(__Method_GetWorkflowStatus, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::HexaGrid.CLI.Grpc.GetWorkflowStatusRequest, global::HexaGrid.CLI.Grpc.GetWorkflowStatusResponse>(serviceImpl.GetWorkflowStatus));
      serviceBinder.AddMethod(__Method_CancelWorkflow, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::HexaGrid.CLI.Grpc.CancelWorkflowRequest, global::HexaGrid.CLI.Grpc.CancelWorkflowResponse>(serviceImpl.CancelWorkflow));
      serviceBinder.AddMethod(__Method_GetPendingWork, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::HexaGrid.CLI.Grpc.GetPendingWorkRequest, global::HexaGrid.CLI.Grpc.GetPendingWorkResponse>(serviceImpl.GetPendingWork));
      serviceBinder.AddMethod(__Method_ReportNodeResult, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::HexaGrid.CLI.Grpc.ReportNodeResultRequest, global::HexaGrid.CLI.Grpc.ReportNodeResultResponse>(serviceImpl.ReportNodeResult));
      serviceBinder.AddMethod(__Method_GetClusterInfo, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::HexaGrid.CLI.Grpc.GetClusterInfoRequest, global::HexaGrid.CLI.Grpc.GetClusterInfoResponse>(serviceImpl.GetClusterInfo));
    }

  }
  /// <summary>
  /// HexaGrid Agent Service (for peer-to-peer communication)
  /// </summary>
  public static partial class HexaGridAgent
  {
    static readonly string __ServiceName = "hexagrid.HexaGridAgent";

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static void __Helper_SerializeMessage(global::Google.Protobuf.IMessage message, grpc::SerializationContext context)
    {
      #if !GRPC_DISABLE_PROTOBUF_BUFFER_SERIALIZATION
      if (message is global::Google.Protobuf.IBufferMessage)
      {
        context.SetPayloadLength(message.CalculateSize());
        global::Google.Protobuf.MessageExtensions.WriteTo(message, context.GetBufferWriter());
        context.Complete();
        return;
      }
      #endif
      context.Complete(global::Google.Protobuf.MessageExtensions.ToByteArray(message));
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static class __Helper_MessageCache<T>
    {
      public static readonly bool IsBufferMessage = global::System.Reflection.IntrospectionExtensions.GetTypeInfo(typeof(global::Google.Protobuf.IBufferMessage)).IsAssignableFrom(typeof(T));
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static T __Helper_DeserializeMessage<T>(grpc::DeserializationContext context, global::Google.Protobuf.MessageParser<T> parser) where T : global::Google.Protobuf.IMessage<T>
    {
      #if !GRPC_DISABLE_PROTOBUF_BUFFER_SERIALIZATION
      if (__Helper_MessageCache<T>.IsBufferMessage)
      {
        return parser.ParseFrom(context.PayloadAsReadOnlySequence());
      }
      #endif
      return parser.ParseFrom(context.PayloadAsNewBuffer());
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::HexaGrid.CLI.Grpc.ExecuteNodeRequest> __Marshaller_hexagrid_ExecuteNodeRequest = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::HexaGrid.CLI.Grpc.ExecuteNodeRequest.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::HexaGrid.CLI.Grpc.ExecuteNodeResponse> __Marshaller_hexagrid_ExecuteNodeResponse = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::HexaGrid.CLI.Grpc.ExecuteNodeResponse.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::HexaGrid.CLI.Grpc.GetAgentStatusRequest> __Marshaller_hexagrid_GetAgentStatusRequest = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::HexaGrid.CLI.Grpc.GetAgentStatusRequest.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::HexaGrid.CLI.Grpc.GetAgentStatusResponse> __Marshaller_hexagrid_GetAgentStatusResponse = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::HexaGrid.CLI.Grpc.GetAgentStatusResponse.Parser));

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::HexaGrid.CLI.Grpc.ExecuteNodeRequest, global::HexaGrid.CLI.Grpc.ExecuteNodeResponse> __Method_ExecuteNode = new grpc::Method<global::HexaGrid.CLI.Grpc.ExecuteNodeRequest, global::HexaGrid.CLI.Grpc.ExecuteNodeResponse>(
        grpc::MethodType.Unary,
        __ServiceName,
        "ExecuteNode",
        __Marshaller_hexagrid_ExecuteNodeRequest,
        __Marshaller_hexagrid_ExecuteNodeResponse);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::HexaGrid.CLI.Grpc.GetAgentStatusRequest, global::HexaGrid.CLI.Grpc.GetAgentStatusResponse> __Method_GetAgentStatus = new grpc::Method<global::HexaGrid.CLI.Grpc.GetAgentStatusRequest, global::HexaGrid.CLI.Grpc.GetAgentStatusResponse>(
        grpc::MethodType.Unary,
        __ServiceName,
        "GetAgentStatus",
        __Marshaller_hexagrid_GetAgentStatusRequest,
        __Marshaller_hexagrid_GetAgentStatusResponse);

    /// <summary>Service descriptor</summary>
    public static global::Google.Protobuf.Reflection.ServiceDescriptor Descriptor
    {
      get { return global::HexaGrid.CLI.Grpc.HexagridReflection.Descriptor.Services[1]; }
    }

    /// <summary>Base class for server-side implementations of HexaGridAgent</summary>
    [grpc::BindServiceMethod(typeof(HexaGridAgent), "BindService")]
    public abstract partial class HexaGridAgentBase
    {
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::HexaGrid.CLI.Grpc.ExecuteNodeResponse> ExecuteNode(global::HexaGrid.CLI.Grpc.ExecuteNodeRequest request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::HexaGrid.CLI.Grpc.GetAgentStatusResponse> GetAgentStatus(global::HexaGrid.CLI.Grpc.GetAgentStatusRequest request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

    }

    /// <summary>Client for HexaGridAgent</summary>
    public partial class HexaGridAgentClient : grpc::ClientBase<HexaGridAgentClient>
    {
      /// <summary>Creates a new client for HexaGridAgent</summary>
      /// <param name="channel">The channel to use to make remote calls.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public HexaGridAgentClient(grpc::ChannelBase channel) : base(channel)
      {
      }
      /// <summary>Creates a new client for HexaGridAgent that uses a custom <c>CallInvoker</c>.</summary>
      /// <param name="callInvoker">The callInvoker to use to make remote calls.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public HexaGridAgentClient(grpc::CallInvoker callInvoker) : base(callInvoker)
      {
      }
      /// <summary>Protected parameterless constructor to allow creation of test doubles.</summary>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected HexaGridAgentClient() : base()
      {
      }
      /// <summary>Protected constructor to allow creation of configured clients.</summary>
      /// <param name="configuration">The client configuration.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected HexaGridAgentClient(ClientBaseConfiguration configuration) : base(configuration)
      {
      }

      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::HexaGrid.CLI.Grpc.ExecuteNodeResponse ExecuteNode(global::HexaGrid.CLI.Grpc.ExecuteNodeRequest request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return ExecuteNode(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::HexaGrid.CLI.Grpc.ExecuteNodeResponse ExecuteNode(global::HexaGrid.CLI.Grpc.ExecuteNodeRequest request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_ExecuteNode, null, options, request);
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::HexaGrid.CLI.Grpc.ExecuteNodeResponse> ExecuteNodeAsync(global::HexaGrid.CLI.Grpc.ExecuteNodeRequest request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return ExecuteNodeAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::HexaGrid.CLI.Grpc.ExecuteNodeResponse> ExecuteNodeAsync(global::HexaGrid.CLI.Grpc.ExecuteNodeRequest request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_ExecuteNode, null, options, request);
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::HexaGrid.CLI.Grpc.GetAgentStatusResponse GetAgentStatus(global::HexaGrid.CLI.Grpc.GetAgentStatusRequest request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetAgentStatus(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::HexaGrid.CLI.Grpc.GetAgentStatusResponse GetAgentStatus(global::HexaGrid.CLI.Grpc.GetAgentStatusRequest request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_GetAgentStatus, null, options, request);
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::HexaGrid.CLI.Grpc.GetAgentStatusResponse> GetAgentStatusAsync(global::HexaGrid.CLI.Grpc.GetAgentStatusRequest request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetAgentStatusAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::HexaGrid.CLI.Grpc.GetAgentStatusResponse> GetAgentStatusAsync(global::HexaGrid.CLI.Grpc.GetAgentStatusRequest request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_GetAgentStatus, null, options, request);
      }
      /// <summary>Creates a new instance of client from given <c>ClientBaseConfiguration</c>.</summary>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected override HexaGridAgentClient NewInstance(ClientBaseConfiguration configuration)
      {
        return new HexaGridAgentClient(configuration);
      }
    }

    /// <summary>Creates service definition that can be registered with a server</summary>
    /// <param name="serviceImpl">An object implementing the server-side handling logic.</param>
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    public static grpc::ServerServiceDefinition BindService(HexaGridAgentBase serviceImpl)
    {
      return grpc::ServerServiceDefinition.CreateBuilder()
          .AddMethod(__Method_ExecuteNode, serviceImpl.ExecuteNode)
          .AddMethod(__Method_GetAgentStatus, serviceImpl.GetAgentStatus).Build();
    }

    /// <summary>Register service method with a service binder with or without implementation. Useful when customizing the service binding logic.
    /// Note: this method is part of an experimental API that can change or be removed without any prior notice.</summary>
    /// <param name="serviceBinder">Service methods will be bound by calling <c>AddMethod</c> on this object.</param>
    /// <param name="serviceImpl">An object implementing the server-side handling logic.</param>
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    public static void BindService(grpc::ServiceBinderBase serviceBinder, HexaGridAgentBase serviceImpl)
    {
      serviceBinder.AddMethod(__Method_ExecuteNode, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::HexaGrid.CLI.Grpc.ExecuteNodeRequest, global::HexaGrid.CLI.Grpc.ExecuteNodeResponse>(serviceImpl.ExecuteNode));
      serviceBinder.AddMethod(__Method_GetAgentStatus, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::HexaGrid.CLI.Grpc.GetAgentStatusRequest, global::HexaGrid.CLI.Grpc.GetAgentStatusResponse>(serviceImpl.GetAgentStatus));
    }

  }
}
#endregion
