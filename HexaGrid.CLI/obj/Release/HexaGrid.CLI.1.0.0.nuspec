﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2010/07/nuspec.xsd">
  <metadata>
    <id>HexaGrid.CLI</id>
    <version>1.0.0</version>
    <authors>HexaGrid Team</authors>
    <description>HexaGrid CLI - Simple, Powerful Workflow Automation</description>
    <packageTypes>
      <packageType name="DotnetTool" />
    </packageTypes>
    <repository type="git" commit="77fdfad5536adc014edf092b004ed106b2cdeace" />
  </metadata>
  <files>
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/obj/Release/net9.0/DotnetToolSettings.xml" target="tools/net9.0/any/DotnetToolSettings.xml" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/hexagrid.dll" target="tools/net9.0/any/hexagrid.dll" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/hexagrid.runtimeconfig.json" target="tools/net9.0/any/hexagrid.runtimeconfig.json" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/hexagrid.pdb" target="tools/net9.0/any/hexagrid.pdb" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/Google.Protobuf.dll" target="tools/net9.0/any/Google.Protobuf.dll" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/Grpc.AspNetCore.Server.dll" target="tools/net9.0/any/Grpc.AspNetCore.Server.dll" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/Grpc.AspNetCore.Server.ClientFactory.dll" target="tools/net9.0/any/Grpc.AspNetCore.Server.ClientFactory.dll" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/Grpc.Core.Api.dll" target="tools/net9.0/any/Grpc.Core.Api.dll" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/Grpc.Net.Client.dll" target="tools/net9.0/any/Grpc.Net.Client.dll" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/Grpc.Net.ClientFactory.dll" target="tools/net9.0/any/Grpc.Net.ClientFactory.dll" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/Grpc.Net.Common.dll" target="tools/net9.0/any/Grpc.Net.Common.dll" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/Microsoft.Data.Sqlite.dll" target="tools/net9.0/any/Microsoft.Data.Sqlite.dll" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/Microsoft.EntityFrameworkCore.dll" target="tools/net9.0/any/Microsoft.EntityFrameworkCore.dll" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/Microsoft.EntityFrameworkCore.Abstractions.dll" target="tools/net9.0/any/Microsoft.EntityFrameworkCore.Abstractions.dll" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/Microsoft.EntityFrameworkCore.Relational.dll" target="tools/net9.0/any/Microsoft.EntityFrameworkCore.Relational.dll" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/Microsoft.EntityFrameworkCore.Sqlite.dll" target="tools/net9.0/any/Microsoft.EntityFrameworkCore.Sqlite.dll" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/Microsoft.Extensions.DependencyModel.dll" target="tools/net9.0/any/Microsoft.Extensions.DependencyModel.dll" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/SQLitePCLRaw.batteries_v2.dll" target="tools/net9.0/any/SQLitePCLRaw.batteries_v2.dll" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/SQLitePCLRaw.core.dll" target="tools/net9.0/any/SQLitePCLRaw.core.dll" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/SQLitePCLRaw.provider.e_sqlite3.dll" target="tools/net9.0/any/SQLitePCLRaw.provider.e_sqlite3.dll" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/System.CommandLine.dll" target="tools/net9.0/any/System.CommandLine.dll" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/HexaGrid.Core.dll" target="tools/net9.0/any/HexaGrid.Core.dll" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/HexaGrid.Core.pdb" target="tools/net9.0/any/HexaGrid.Core.pdb" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/hexagrid.deps.json" target="tools/net9.0/any/hexagrid.deps.json" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/cs/System.CommandLine.resources.dll" target="tools/net9.0/any/cs/System.CommandLine.resources.dll" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/de/System.CommandLine.resources.dll" target="tools/net9.0/any/de/System.CommandLine.resources.dll" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/es/System.CommandLine.resources.dll" target="tools/net9.0/any/es/System.CommandLine.resources.dll" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/fr/System.CommandLine.resources.dll" target="tools/net9.0/any/fr/System.CommandLine.resources.dll" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/it/System.CommandLine.resources.dll" target="tools/net9.0/any/it/System.CommandLine.resources.dll" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/ja/System.CommandLine.resources.dll" target="tools/net9.0/any/ja/System.CommandLine.resources.dll" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/ko/System.CommandLine.resources.dll" target="tools/net9.0/any/ko/System.CommandLine.resources.dll" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/pl/System.CommandLine.resources.dll" target="tools/net9.0/any/pl/System.CommandLine.resources.dll" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/pt-BR/System.CommandLine.resources.dll" target="tools/net9.0/any/pt-BR/System.CommandLine.resources.dll" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/ru/System.CommandLine.resources.dll" target="tools/net9.0/any/ru/System.CommandLine.resources.dll" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/tr/System.CommandLine.resources.dll" target="tools/net9.0/any/tr/System.CommandLine.resources.dll" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/zh-Hans/System.CommandLine.resources.dll" target="tools/net9.0/any/zh-Hans/System.CommandLine.resources.dll" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/zh-Hant/System.CommandLine.resources.dll" target="tools/net9.0/any/zh-Hant/System.CommandLine.resources.dll" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/runtimes/browser-wasm/nativeassets/net9.0/e_sqlite3.a" target="tools/net9.0/any/runtimes/browser-wasm/nativeassets/net9.0/e_sqlite3.a" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/runtimes/linux-arm/native/libe_sqlite3.so" target="tools/net9.0/any/runtimes/linux-arm/native/libe_sqlite3.so" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/runtimes/linux-arm64/native/libe_sqlite3.so" target="tools/net9.0/any/runtimes/linux-arm64/native/libe_sqlite3.so" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/runtimes/linux-armel/native/libe_sqlite3.so" target="tools/net9.0/any/runtimes/linux-armel/native/libe_sqlite3.so" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/runtimes/linux-mips64/native/libe_sqlite3.so" target="tools/net9.0/any/runtimes/linux-mips64/native/libe_sqlite3.so" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/runtimes/linux-musl-arm/native/libe_sqlite3.so" target="tools/net9.0/any/runtimes/linux-musl-arm/native/libe_sqlite3.so" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/runtimes/linux-musl-arm64/native/libe_sqlite3.so" target="tools/net9.0/any/runtimes/linux-musl-arm64/native/libe_sqlite3.so" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/runtimes/linux-musl-s390x/native/libe_sqlite3.so" target="tools/net9.0/any/runtimes/linux-musl-s390x/native/libe_sqlite3.so" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/runtimes/linux-musl-x64/native/libe_sqlite3.so" target="tools/net9.0/any/runtimes/linux-musl-x64/native/libe_sqlite3.so" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/runtimes/linux-ppc64le/native/libe_sqlite3.so" target="tools/net9.0/any/runtimes/linux-ppc64le/native/libe_sqlite3.so" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/runtimes/linux-s390x/native/libe_sqlite3.so" target="tools/net9.0/any/runtimes/linux-s390x/native/libe_sqlite3.so" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/runtimes/linux-x64/native/libe_sqlite3.so" target="tools/net9.0/any/runtimes/linux-x64/native/libe_sqlite3.so" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/runtimes/linux-x86/native/libe_sqlite3.so" target="tools/net9.0/any/runtimes/linux-x86/native/libe_sqlite3.so" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/runtimes/maccatalyst-arm64/native/libe_sqlite3.dylib" target="tools/net9.0/any/runtimes/maccatalyst-arm64/native/libe_sqlite3.dylib" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/runtimes/maccatalyst-x64/native/libe_sqlite3.dylib" target="tools/net9.0/any/runtimes/maccatalyst-x64/native/libe_sqlite3.dylib" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/runtimes/osx-arm64/native/libe_sqlite3.dylib" target="tools/net9.0/any/runtimes/osx-arm64/native/libe_sqlite3.dylib" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/runtimes/osx-x64/native/libe_sqlite3.dylib" target="tools/net9.0/any/runtimes/osx-x64/native/libe_sqlite3.dylib" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/runtimes/win-arm/native/e_sqlite3.dll" target="tools/net9.0/any/runtimes/win-arm/native/e_sqlite3.dll" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/runtimes/win-arm64/native/e_sqlite3.dll" target="tools/net9.0/any/runtimes/win-arm64/native/e_sqlite3.dll" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/runtimes/win-x64/native/e_sqlite3.dll" target="tools/net9.0/any/runtimes/win-x64/native/e_sqlite3.dll" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/runtimes/win-x86/native/e_sqlite3.dll" target="tools/net9.0/any/runtimes/win-x86/native/e_sqlite3.dll" />
    <file src="/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.CLI/bin/Release/net9.0/publish/runtimes/win/lib/net9.0/System.Diagnostics.EventLog.Messages.dll" target="tools/net9.0/any/runtimes/win/lib/net9.0/System.Diagnostics.EventLog.Messages.dll" />
  </files>
</package>