using System.CommandLine;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using HexaGrid.CLI.Commands;
using HexaGrid.CLI.Services;
using HexaGrid.Core.Nodes;

namespace HexaGrid.CLI;

class Program
{
    static async Task<int> Main(string[] args)
    {
        // Create the root command with beginner-friendly description
        var rootCommand = new RootCommand("🔷 HexaGrid - Simple, Powerful Workflow Automation\n\n" +
            "HexaGrid makes it easy to create and run automated workflows across multiple machines.\n" +
            "Think of it like connecting building blocks to automate your tasks.\n\n" +
            "Quick Start:\n" +
            "  hexagrid start          # Start HexaGrid (single-node mode)\n" +
            "  hexagrid deploy my.yaml # Run a workflow\n" +
            "  hexagrid status         # Check what's running\n" +
            "  hexagrid logs           # View execution logs\n\n" +
            "Need help? Run 'hexagrid --help' or visit: https://hexagrid.dev/docs")
        {
            Name = "hexagrid"
        };

        // Remove the automatic --version option to avoid conflicts
        rootCommand.TreatUnmatchedTokensAsErrors = true;

        // Add global options
        var verboseOption = new Option<bool>(
            aliases: ["--verbose", "-v"],
            description: "Enable verbose logging");
        
        var configOption = new Option<string?>(
            aliases: ["--config", "-c"],
            description: "Path to configuration file");

        rootCommand.AddGlobalOption(verboseOption);
        rootCommand.AddGlobalOption(configOption);

        // Beginner-friendly commands
        var startCommand = new Command("start", "🚀 Start HexaGrid (easiest way to begin)")
        {
            StartCommand.CreateCommand()
        };

        var deployCommand = new Command("deploy", "📤 Run a workflow from a YAML file")
        {
            DeployCommand.CreateCommand()
        };

        var statusCommand = new Command("status", "📊 Show what's currently running")
        {
            StatusCommand.CreateCommand()
        };

        var logsCommand = new Command("logs", "📋 View workflow execution logs")
        {
            LogsCommand.CreateCommand()
        };

        // Advanced commands (for power users)
        var serverCommand = new Command("server", "🎛️ Start HexaGrid controller (advanced)")
        {
            ServerCommand.CreateCommand()
        };

        var agentCommand = new Command("agent", "🤖 Start HexaGrid agent (advanced)")
        {
            AgentCommand.CreateCommand()
        };

        var getCommand = new Command("get", "🔍 Get detailed cluster information")
        {
            GetCommand.CreateCommand()
        };

        // Utility commands
        var initCommand = new Command("init", "🏗️ Create a sample workflow to get started")
        {
            InitCommand.CreateCommand()
        };

        var validateCommand = new Command("validate", "✅ Check if a workflow file is valid")
        {
            ValidateCommand.CreateCommand()
        };

        var versionCommand = new Command("version", "ℹ️ Show version information")
        {
            VersionCommand.CreateCommand()
        };

        // Add commands to root (beginner-friendly first)
        rootCommand.AddCommand(startCommand);
        rootCommand.AddCommand(deployCommand);
        rootCommand.AddCommand(statusCommand);
        rootCommand.AddCommand(logsCommand);
        rootCommand.AddCommand(initCommand);
        rootCommand.AddCommand(validateCommand);

        // Advanced commands
        rootCommand.AddCommand(serverCommand);
        rootCommand.AddCommand(agentCommand);
        rootCommand.AddCommand(getCommand);
        rootCommand.AddCommand(versionCommand);

        // Configure services
        var services = new ServiceCollection();
        ConfigureServices(services);

        using var serviceProvider = services.BuildServiceProvider();
        
        // Set up logging
        var loggerFactory = serviceProvider.GetRequiredService<ILoggerFactory>();
        var logger = loggerFactory.CreateLogger<Program>();

        try
        {
            return await rootCommand.InvokeAsync(args);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Unhandled exception occurred");
            Console.WriteLine($"❌ Error: {ex.Message}");
            return 1;
        }
    }

    private static void ConfigureServices(IServiceCollection services)
    {
        services.AddLogging(builder =>
        {
            builder.AddConsole();
            builder.SetMinimumLevel(LogLevel.Information);
        });

        // Add HexaGrid services
        services.AddSingleton<IClusterStateService, ClusterStateService>();
        services.AddSingleton<INodeDiscoveryService, NodeDiscoveryService>();
        services.AddSingleton<IWorkflowExecutionService, WorkflowExecutionService>();
        services.AddSingleton<IConfigurationService, ConfigurationService>();
        services.AddSingleton<INodeFactory, DefaultNodeFactory>();
    }
}
