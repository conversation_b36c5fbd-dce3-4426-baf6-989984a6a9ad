{"name": "hello-world", "description": "A simple workflow to get you started with HexaGrid", "nodes": [{"id": "start", "type": "PrintNode", "inputs": {"message": "🚀 Starting your first HexaGrid workflow!"}}, {"id": "wait", "type": "WaitNode", "inputs": {"duration": 2, "message": "⏳ Waiting 2 seconds..."}}, {"id": "process", "type": "PrintNode", "inputs": {"message": "⚙️ Processing data (this is where the magic happens!)"}}, {"id": "finish", "type": "PrintNode", "inputs": {"message": "✅ Congratulations! Your first workflow completed successfully!"}}], "connections": [{"fromNodeId": "start", "fromPort": "done", "toNodeId": "wait", "toPort": "trigger"}, {"fromNodeId": "wait", "fromPort": "done", "toNodeId": "process", "toPort": "trigger"}, {"fromNodeId": "process", "fromPort": "done", "toNodeId": "finish", "toPort": "trigger"}], "variables": {"environment": "development", "debug": true}}