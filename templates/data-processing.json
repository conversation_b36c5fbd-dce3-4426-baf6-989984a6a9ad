{"name": "data-processing", "description": "A more complex workflow showing data processing patterns", "nodes": [{"id": "load-data", "type": "PrintNode", "inputs": {"message": "📂 Loading data from source..."}}, {"id": "validate-data", "type": "ConditionalNode", "inputs": {"condition": "data.length > 0", "message": "✅ Data validation passed"}}, {"id": "transform-data", "type": "MathNode", "inputs": {"operation": "aggregate", "function": "sum", "message": "🔄 Transforming data..."}}, {"id": "save-results", "type": "PrintNode", "inputs": {"message": "💾 Saving processed results..."}}, {"id": "send-notification", "type": "PrintNode", "inputs": {"message": "📧 Sending completion notification..."}}, {"id": "cleanup", "type": "PrintNode", "inputs": {"message": "🧹 Cleaning up temporary files..."}}], "connections": [{"fromNodeId": "load-data", "fromPort": "done", "toNodeId": "validate-data", "toPort": "trigger"}, {"fromNodeId": "validate-data", "fromPort": "success", "toNodeId": "transform-data", "toPort": "trigger"}, {"fromNodeId": "transform-data", "fromPort": "done", "toNodeId": "save-results", "toPort": "trigger"}, {"fromNodeId": "save-results", "fromPort": "done", "toNodeId": "send-notification", "toPort": "trigger"}, {"fromNodeId": "send-notification", "fromPort": "done", "toNodeId": "cleanup", "toPort": "trigger"}], "variables": {"batch_size": 1000, "retry_count": 3, "environment": "development"}}