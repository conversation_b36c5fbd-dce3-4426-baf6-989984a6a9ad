{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"HexaGrid.Web/1.0.0": {"dependencies": {"HexaGrid.Core": "1.0.0", "HexaGrid.Runtime": "1.0.0", "Swashbuckle.AspNetCore": "9.0.3"}, "runtime": {"HexaGrid.Web.dll": {}}}, "Microsoft.Extensions.ApiDescription.Server/9.0.0": {}, "Microsoft.OpenApi/1.6.23": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Swashbuckle.AspNetCore/9.0.3": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "9.0.0", "Swashbuckle.AspNetCore.Swagger": "9.0.3", "Swashbuckle.AspNetCore.SwaggerGen": "9.0.3", "Swashbuckle.AspNetCore.SwaggerUI": "9.0.3"}}, "Swashbuckle.AspNetCore.Swagger/9.0.3": {"dependencies": {"Microsoft.OpenApi": "1.6.23"}, "runtime": {"lib/net9.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.3.1613"}}}, "Swashbuckle.AspNetCore.SwaggerGen/9.0.3": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "9.0.3"}, "runtime": {"lib/net9.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.3.1613"}}}, "Swashbuckle.AspNetCore.SwaggerUI/9.0.3": {"runtime": {"lib/net9.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.3.1613"}}}, "System.Text.Json/9.0.7": {}, "HexaGrid.Core/1.0.0": {"runtime": {"HexaGrid.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "HexaGrid.Runtime/1.0.0": {"dependencies": {"HexaGrid.Core": "1.0.0", "System.Text.Json": "9.0.7"}, "runtime": {"HexaGrid.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"HexaGrid.Web/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.ApiDescription.Server/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-1Kzzf7pRey40VaUkHN9/uWxrKVkLu2AQjt+GVeeKLLpiEHAJ1xZRsLSh4ZZYEnyS7Kt2OBOPmsXNdU+wbcOl5w==", "path": "microsoft.extensions.apidescription.server/9.0.0", "hashPath": "microsoft.extensions.apidescription.server.9.0.0.nupkg.sha512"}, "Microsoft.OpenApi/1.6.23": {"type": "package", "serviceable": true, "sha512": "sha512-tZ1I0KXnn98CWuV8cpI247A17jaY+ILS9vvF7yhI0uPPEqF4P1d7BWL5Uwtel10w9NucllHB3nTkfYTAcHAh8g==", "path": "microsoft.openapi/1.6.23", "hashPath": "microsoft.openapi.1.6.23.nupkg.sha512"}, "Swashbuckle.AspNetCore/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-Akk4oFgy0ST8Q8pZTfPbrt045tWNyMMiKhlbYjG3qnjQZLz645IL5vhQm7NLicc2sAAQ+vftArIlsYWFevmb2g==", "path": "swashbuckle.aspnetcore/9.0.3", "hashPath": "swashbuckle.aspnetcore.9.0.3.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-CGpkZDWj1g/yH/0wYkxUtBhiFo5TY/Esq2fS0vlBvLOs1UL2Jzef9tdtYmTdd3zBPtnMyXQcsXjMt9yCxz4VaA==", "path": "swashbuckle.aspnetcore.swagger/9.0.3", "hashPath": "swashbuckle.aspnetcore.swagger.9.0.3.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-STqjhw1TZiEGmIRgE6jcJUOcgU/Fjquc6dP4GqbuwBzqWZAWr/9T7FZOGWYEwKnmkMplzlUNepGHwnUrfTP0fw==", "path": "swashbuckle.aspnetcore.swaggergen/9.0.3", "hashPath": "swashbuckle.aspnetcore.swaggergen.9.0.3.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-DgJKJASz5OAygeKv2+N0FCZVhQylESqLXrtrRAqIT0vKpX7t5ImJ1FL6+6OqxKiamGkL0jchRXR8OgpMSsMh8w==", "path": "swashbuckle.aspnetcore.swaggerui/9.0.3", "hashPath": "swashbuckle.aspnetcore.swaggerui.9.0.3.nupkg.sha512"}, "System.Text.Json/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-u/lN2FEEXs3ghj2ta8tWA4r2MS9Yni07K7jDmnz8h1UPDf0lIIIEMkWx383Zz4fJjJio7gDl+00RYuQ/7R8ZQw==", "path": "system.text.json/9.0.7", "hashPath": "system.text.json.9.0.7.nupkg.sha512"}, "HexaGrid.Core/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "HexaGrid.Runtime/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}