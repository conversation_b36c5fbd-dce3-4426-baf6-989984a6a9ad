using HexaGrid.Core.Nodes;
using HexaGrid.Web.Hubs;
using HexaGrid.Web.Services;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Add SignalR
builder.Services.AddSignalR();

// Register HexaGrid services
builder.Services.AddSingleton<INodeFactory, DefaultNodeFactory>();
builder.Services.AddScoped<IGraphMonitoringService, GraphMonitoringService>();

// Add CORS for development
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
    app.UseCors("AllowAll");
}

app.UseHttpsRedirection();
app.UseAuthorization();

app.MapControllers();
app.MapHub<MonitoringHub>("/monitoring");

app.MapGet("/", () => new {
    message = "🔷 HexaGrid Web API",
    version = "1.0.0",
    endpoints = new[] {
        "/api/graph/run - POST: Execute a graph",
        "/api/graph/validate - POST: Validate a graph",
        "/monitoring - WebSocket: Real-time monitoring",
        "/swagger - API Documentation"
    }
});

app.Run();
