using Microsoft.AspNetCore.SignalR;
using HexaGrid.Web.Hubs;
using HexaGrid.Core.Graph;
using HexaGrid.Core.Models;
using System.Diagnostics;

namespace HexaGrid.Web.Services
{
    public interface IGraphMonitoringService
    {
        Task NotifyGraphStarted(string graphId, NodeGraph graph);
        Task NotifyGraphCompleted(string graphId, GraphExecutionResult result);
        Task NotifyNodeStarted(string graphId, Node node);
        Task NotifyNodeCompleted(string graphId, Node node, NodeResult result);
        Task NotifyNodeRetrying(string graphId, Node node, int attemptNumber, int maxAttempts, string errorMessage, TimeSpan nextRetryDelay);
        Task NotifyProgress(string graphId, GraphProgressInfo progress);
        Task LogMessage(string graphId, string level, string message, string? nodeId = null, string? nodeType = null, Dictionary<string, object>? properties = null);
    }

    public class GraphMonitoringService : IGraphMonitoringService
    {
        private readonly IHubContext<MonitoringHub, IMonitoringClient> _hubContext;
        private readonly ILogger<GraphMonitoringService> _logger;

        public GraphMonitoringService(IHubContext<MonitoringHub, IMonitoringClient> hubContext, ILogger<GraphMonitoringService> logger)
        {
            _hubContext = hubContext;
            _logger = logger;
        }

        public async Task NotifyGraphStarted(string graphId, NodeGraph graph)
        {
            var eventData = new GraphExecutionStartedEvent
            {
                GraphId = graphId,
                GraphName = graph.Name,
                TotalNodes = graph.Nodes.Count,
                Variables = graph.Variables
            };

            await _hubContext.Clients.Group($"graph-{graphId}").GraphStarted(graphId, eventData);
            _logger.LogInformation("Graph {GraphId} execution started with {NodeCount} nodes", graphId, graph.Nodes.Count);
        }

        public async Task NotifyGraphCompleted(string graphId, GraphExecutionResult result)
        {
            var eventData = new GraphExecutionCompletedEvent
            {
                GraphId = graphId,
                Success = result.Success,
                ErrorMessage = result.ErrorMessage,
                Duration = result.ExecutionDuration,
                SuccessfulNodes = result.SuccessfulNodes,
                FailedNodes = result.FailedNodes,
                SuccessRate = result.SuccessRate
            };

            await _hubContext.Clients.Group($"graph-{graphId}").GraphCompleted(graphId, eventData);
            
            var logLevel = result.Success ? LogLevel.Information : LogLevel.Warning;
            _logger.Log(logLevel, "Graph {GraphId} execution completed. Success: {Success}, Duration: {Duration}", 
                graphId, result.Success, result.ExecutionDuration);
        }

        public async Task NotifyNodeStarted(string graphId, Node node)
        {
            var eventData = new NodeExecutionStartedEvent
            {
                NodeId = node.Id,
                NodeType = node.Type,
                NodeName = node.Name,
                Inputs = node.Inputs
            };

            await _hubContext.Clients.Group($"graph-{graphId}").NodeStarted(graphId, eventData);
            _logger.LogDebug("Node {NodeId} ({NodeType}) started in graph {GraphId}", node.Id, node.Type, graphId);
        }

        public async Task NotifyNodeCompleted(string graphId, Node node, NodeResult result)
        {
            var eventData = new NodeExecutionCompletedEvent
            {
                NodeId = node.Id,
                NodeType = node.Type,
                Success = result.Success,
                ErrorMessage = result.ErrorMessage,
                Duration = result.ExecutionDuration,
                AttemptCount = result.AttemptCount,
                Outputs = result.Outputs
            };

            await _hubContext.Clients.Group($"graph-{graphId}").NodeCompleted(graphId, eventData);
            
            var logLevel = result.Success ? LogLevel.Debug : LogLevel.Warning;
            _logger.Log(logLevel, "Node {NodeId} ({NodeType}) completed in graph {GraphId}. Success: {Success}, Attempts: {AttemptCount}", 
                node.Id, node.Type, graphId, result.Success, result.AttemptCount);
        }

        public async Task NotifyNodeRetrying(string graphId, Node node, int attemptNumber, int maxAttempts, string errorMessage, TimeSpan nextRetryDelay)
        {
            var eventData = new NodeRetryEvent
            {
                NodeId = node.Id,
                NodeType = node.Type,
                AttemptNumber = attemptNumber,
                MaxAttempts = maxAttempts,
                ErrorMessage = errorMessage,
                NextRetryDelay = nextRetryDelay
            };

            await _hubContext.Clients.Group($"graph-{graphId}").NodeRetrying(graphId, eventData);
            _logger.LogWarning("Node {NodeId} ({NodeType}) retry {AttemptNumber}/{MaxAttempts} in graph {GraphId}. Error: {ErrorMessage}", 
                node.Id, node.Type, attemptNumber, maxAttempts, graphId, errorMessage);
        }

        public async Task NotifyProgress(string graphId, GraphProgressInfo progress)
        {
            var eventData = new GraphProgressEvent
            {
                GraphId = graphId,
                CompletedNodes = progress.CompletedNodes,
                TotalNodes = progress.TotalNodes,
                SuccessfulNodes = progress.SuccessfulNodes,
                FailedNodes = progress.FailedNodes,
                ProgressPercentage = progress.ProgressPercentage,
                CurrentNodeId = progress.CurrentNodeId,
                CurrentNodeType = progress.CurrentNodeType,
                ElapsedTime = progress.ElapsedTime,
                EstimatedTimeRemaining = progress.EstimatedTimeRemaining
            };

            await _hubContext.Clients.Group($"graph-{graphId}").GraphProgress(graphId, eventData);
        }

        public async Task LogMessage(string graphId, string level, string message, string? nodeId = null, string? nodeType = null, Dictionary<string, object>? properties = null)
        {
            var eventData = new LogEvent
            {
                Level = level,
                Message = message,
                NodeId = nodeId,
                NodeType = nodeType,
                Properties = properties ?? new Dictionary<string, object>()
            };

            await _hubContext.Clients.Group($"graph-{graphId}").LogMessage(graphId, eventData);
            
            var logLevel = level.ToLower() switch
            {
                "debug" => LogLevel.Debug,
                "info" or "information" => LogLevel.Information,
                "warn" or "warning" => LogLevel.Warning,
                "error" => LogLevel.Error,
                "critical" => LogLevel.Critical,
                _ => LogLevel.Information
            };
            
            _logger.Log(logLevel, "Graph {GraphId} - {Message}", graphId, message);
        }
    }

    public class GraphProgressInfo
    {
        public int CompletedNodes { get; set; }
        public int TotalNodes { get; set; }
        public int SuccessfulNodes { get; set; }
        public int FailedNodes { get; set; }
        public double ProgressPercentage => TotalNodes > 0 ? (double)CompletedNodes / TotalNodes * 100 : 0;
        public string CurrentNodeId { get; set; } = "";
        public string CurrentNodeType { get; set; } = "";
        public TimeSpan ElapsedTime { get; set; }
        public TimeSpan? EstimatedTimeRemaining { get; set; }
    }
}
