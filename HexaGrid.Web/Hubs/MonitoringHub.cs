using Microsoft.AspNetCore.SignalR;
using HexaGrid.Core.Graph;
using HexaGrid.Core.Models;

namespace HexaGrid.Web.Hubs
{
    public class MonitoringHub : Hub<IMonitoringClient>
    {
        public async Task JoinGraphExecution(string graphId)
        {
            await Groups.AddToGroupAsync(Context.ConnectionId, $"graph-{graphId}");
        }

        public async Task LeaveGraphExecution(string graphId)
        {
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"graph-{graphId}");
        }

        public override async Task OnDisconnectedAsync(Exception? exception)
        {
            // Clean up any group memberships if needed
            await base.OnDisconnectedAsync(exception);
        }
    }

    public interface IMonitoringClient
    {
        Task GraphStarted(string graphId, GraphExecutionStartedEvent eventData);
        Task GraphCompleted(string graphId, GraphExecutionCompletedEvent eventData);
        Task NodeStarted(string graphId, NodeExecutionStartedEvent eventData);
        Task NodeCompleted(string graphId, NodeExecutionCompletedEvent eventData);
        Task NodeRetrying(string graphId, NodeRetryEvent eventData);
        Task GraphProgress(string graphId, GraphProgressEvent eventData);
        Task LogMessage(string graphId, LogEvent eventData);
    }

    public class GraphExecutionStartedEvent
    {
        public string GraphId { get; set; } = "";
        public string GraphName { get; set; } = "";
        public int TotalNodes { get; set; }
        public DateTime StartTime { get; set; } = DateTime.UtcNow;
        public Dictionary<string, object> Variables { get; set; } = new();
    }

    public class GraphExecutionCompletedEvent
    {
        public string GraphId { get; set; } = "";
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
        public TimeSpan Duration { get; set; }
        public int SuccessfulNodes { get; set; }
        public int FailedNodes { get; set; }
        public double SuccessRate { get; set; }
        public DateTime EndTime { get; set; } = DateTime.UtcNow;
    }

    public class NodeExecutionStartedEvent
    {
        public string NodeId { get; set; } = "";
        public string NodeType { get; set; } = "";
        public string NodeName { get; set; } = "";
        public DateTime StartTime { get; set; } = DateTime.UtcNow;
        public Dictionary<string, object> Inputs { get; set; } = new();
    }

    public class NodeExecutionCompletedEvent
    {
        public string NodeId { get; set; } = "";
        public string NodeType { get; set; } = "";
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
        public TimeSpan Duration { get; set; }
        public int AttemptCount { get; set; }
        public Dictionary<string, object> Outputs { get; set; } = new();
        public DateTime EndTime { get; set; } = DateTime.UtcNow;
    }

    public class NodeRetryEvent
    {
        public string NodeId { get; set; } = "";
        public string NodeType { get; set; } = "";
        public int AttemptNumber { get; set; }
        public int MaxAttempts { get; set; }
        public string ErrorMessage { get; set; } = "";
        public TimeSpan NextRetryDelay { get; set; }
        public DateTime RetryTime { get; set; } = DateTime.UtcNow;
    }

    public class GraphProgressEvent
    {
        public string GraphId { get; set; } = "";
        public int CompletedNodes { get; set; }
        public int TotalNodes { get; set; }
        public int SuccessfulNodes { get; set; }
        public int FailedNodes { get; set; }
        public double ProgressPercentage { get; set; }
        public string CurrentNodeId { get; set; } = "";
        public string CurrentNodeType { get; set; } = "";
        public TimeSpan ElapsedTime { get; set; }
        public TimeSpan? EstimatedTimeRemaining { get; set; }
    }

    public class LogEvent
    {
        public string Level { get; set; } = "Info";
        public string Message { get; set; } = "";
        public string? NodeId { get; set; }
        public string? NodeType { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public Dictionary<string, object> Properties { get; set; } = new();
    }
}
