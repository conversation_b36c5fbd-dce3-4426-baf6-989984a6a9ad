{"format": 1, "restore": {"/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.Web/HexaGrid.Web.csproj": {}}, "projects": {"/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.Core/HexaGrid.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.Core/HexaGrid.Core.csproj", "projectName": "HexaGrid.Core", "projectPath": "/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.Core/HexaGrid.Core.csproj", "packagesPath": "/home/<USER>/.nuget/packages/", "outputPath": "/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.Core/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/home/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/share/dotnet/sdk/9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.Runtime/HexaGrid.Runtime.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.Runtime/HexaGrid.Runtime.csproj", "projectName": "HexaGrid.Runtime", "projectPath": "/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.Runtime/HexaGrid.Runtime.csproj", "packagesPath": "/home/<USER>/.nuget/packages/", "outputPath": "/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.Runtime/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/home/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.Core/HexaGrid.Core.csproj": {"projectPath": "/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.Core/HexaGrid.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"System.Text.Json": {"target": "Package", "version": "[9.0.7, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/share/dotnet/sdk/9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.Web/HexaGrid.Web.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.Web/HexaGrid.Web.csproj", "projectName": "HexaGrid.Web", "projectPath": "/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.Web/HexaGrid.Web.csproj", "packagesPath": "/home/<USER>/.nuget/packages/", "outputPath": "/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.Web/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/home/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.Core/HexaGrid.Core.csproj": {"projectPath": "/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.Core/HexaGrid.Core.csproj"}, "/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.Runtime/HexaGrid.Runtime.csproj": {"projectPath": "/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.Runtime/HexaGrid.Runtime.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Swashbuckle.AspNetCore": {"target": "Package", "version": "[9.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/share/dotnet/sdk/9.0.302/PortableRuntimeIdentifierGraph.json"}}}}}