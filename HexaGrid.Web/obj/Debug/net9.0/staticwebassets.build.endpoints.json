{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "monitor.html", "AssetFile": "monitor.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000317460317"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3149"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"XsB+PYIJS1duDl7jyvF02XBT1nVVsvJS07wS8wr8EAo=\""}, {"Name": "ETag", "Value": "W/\"TgwX4dhJTP/gszngKahJmBkt6oDIgfJD8LeYgcOnJCE=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jul 2025 16:08:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-TgwX4dhJTP/gszngKahJmBkt6oDIgfJD8LeYgcOnJCE="}]}, {"Route": "monitor.html", "AssetFile": "monitor.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "11499"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"TgwX4dhJTP/gszngKahJmBkt6oDIgfJD8LeYgcOnJCE=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jul 2025 10:11:32 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-TgwX4dhJTP/gszngKahJmBkt6oDIgfJD8LeYgcOnJCE="}]}, {"Route": "monitor.html.gz", "AssetFile": "monitor.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3149"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"XsB+PYIJS1duDl7jyvF02XBT1nVVsvJS07wS8wr8EAo=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jul 2025 16:08:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XsB+PYIJS1duDl7jyvF02XBT1nVVsvJS07wS8wr8EAo="}]}, {"Route": "monitor.mjblqvtjyw.html", "AssetFile": "monitor.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000317460317"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3149"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"XsB+PYIJS1duDl7jyvF02XBT1nVVsvJS07wS8wr8EAo=\""}, {"Name": "ETag", "Value": "W/\"TgwX4dhJTP/gszngKahJmBkt6oDIgfJD8LeYgcOnJCE=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jul 2025 16:08:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mjblqvtjyw"}, {"Name": "integrity", "Value": "sha256-TgwX4dhJTP/gszngKahJmBkt6oDIgfJD8LeYgcOnJCE="}, {"Name": "label", "Value": "monitor.html"}]}, {"Route": "monitor.mjblqvtjyw.html", "AssetFile": "monitor.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "11499"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"TgwX4dhJTP/gszngKahJmBkt6oDIgfJD8LeYgcOnJCE=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jul 2025 10:11:32 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mjblqvtjyw"}, {"Name": "integrity", "Value": "sha256-TgwX4dhJTP/gszngKahJmBkt6oDIgfJD8LeYgcOnJCE="}, {"Name": "label", "Value": "monitor.html"}]}, {"Route": "monitor.mjblqvtjyw.html.gz", "AssetFile": "monitor.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3149"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"XsB+PYIJS1duDl7jyvF02XBT1nVVsvJS07wS8wr8EAo=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jul 2025 16:08:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mjblqvtjyw"}, {"Name": "integrity", "Value": "sha256-XsB+PYIJS1duDl7jyvF02XBT1nVVsvJS07wS8wr8EAo="}, {"Name": "label", "Value": "monitor.html.gz"}]}]}