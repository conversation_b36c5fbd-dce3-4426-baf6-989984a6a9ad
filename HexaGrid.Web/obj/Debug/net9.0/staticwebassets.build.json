{"Version": 1, "Hash": "C6/i1aQ4Ha9aH8pSYdOYGVXmlofsajwcZiXtmSoHWGQ=", "Source": "HexaGrid.Web", "BasePath": "_content/HexaGrid.Web", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "HexaGrid.Web/wwwroot", "Source": "HexaGrid.Web", "ContentRoot": "/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.Web/wwwroot/", "BasePath": "_content/HexaGrid.Web", "Pattern": "**"}], "Assets": [{"Identity": "/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.Web/obj/Debug/net9.0/compressed/t26f15851i-mjblqvtjyw.gz", "SourceId": "HexaGrid.Web", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.Web/obj/Debug/net9.0/compressed/", "BasePath": "_content/HexaGrid.Web", "RelativePath": "monitor#[.{fingerprint=mjblqvtjyw}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.Web/wwwroot/monitor.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q9cxg1tuaa", "Integrity": "XsB+PYIJS1duDl7jyvF02XBT1nVVsvJS07wS8wr8EAo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.Web/wwwroot/monitor.html", "FileLength": 3149, "LastWriteTime": "2025-07-24T16:08:13+00:00"}, {"Identity": "/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.Web/wwwroot/monitor.html", "SourceId": "HexaGrid.Web", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.Web/wwwroot/", "BasePath": "_content/HexaGrid.Web", "RelativePath": "monitor#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "mjblqvtjyw", "Integrity": "TgwX4dhJTP/gszngKahJmBkt6oDIgfJD8LeYgcOnJCE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/monitor.html", "FileLength": 11499, "LastWriteTime": "2025-07-24T10:11:32+00:00"}], "Endpoints": [{"Route": "monitor.html", "AssetFile": "/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.Web/obj/Debug/net9.0/compressed/t26f15851i-mjblqvtjyw.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000317460317"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3149"}, {"Name": "ETag", "Value": "\"XsB+PYIJS1duDl7jyvF02XBT1nVVsvJS07wS8wr8EAo=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jul 2025 16:08:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"TgwX4dhJTP/gszngKahJmBkt6oDIgfJD8LeYgcOnJCE=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-TgwX4dhJTP/gszngKahJmBkt6oDIgfJD8LeYgcOnJCE="}]}, {"Route": "monitor.html", "AssetFile": "/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.Web/wwwroot/monitor.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11499"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"TgwX4dhJTP/gszngKahJmBkt6oDIgfJD8LeYgcOnJCE=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jul 2025 10:11:32 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-TgwX4dhJTP/gszngKahJmBkt6oDIgfJD8LeYgcOnJCE="}]}, {"Route": "monitor.html.gz", "AssetFile": "/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.Web/obj/Debug/net9.0/compressed/t26f15851i-mjblqvtjyw.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3149"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"XsB+PYIJS1duDl7jyvF02XBT1nVVsvJS07wS8wr8EAo=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jul 2025 16:08:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XsB+PYIJS1duDl7jyvF02XBT1nVVsvJS07wS8wr8EAo="}]}, {"Route": "monitor.mjblqvtjyw.html", "AssetFile": "/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.Web/obj/Debug/net9.0/compressed/t26f15851i-mjblqvtjyw.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000317460317"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3149"}, {"Name": "ETag", "Value": "\"XsB+PYIJS1duDl7jyvF02XBT1nVVsvJS07wS8wr8EAo=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jul 2025 16:08:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"TgwX4dhJTP/gszngKahJmBkt6oDIgfJD8LeYgcOnJCE=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mjblqvtjyw"}, {"Name": "label", "Value": "monitor.html"}, {"Name": "integrity", "Value": "sha256-TgwX4dhJTP/gszngKahJmBkt6oDIgfJD8LeYgcOnJCE="}]}, {"Route": "monitor.mjblqvtjyw.html", "AssetFile": "/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.Web/wwwroot/monitor.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11499"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"TgwX4dhJTP/gszngKahJmBkt6oDIgfJD8LeYgcOnJCE=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jul 2025 10:11:32 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mjblqvtjyw"}, {"Name": "label", "Value": "monitor.html"}, {"Name": "integrity", "Value": "sha256-TgwX4dhJTP/gszngKahJmBkt6oDIgfJD8LeYgcOnJCE="}]}, {"Route": "monitor.mjblqvtjyw.html.gz", "AssetFile": "/home/<USER>/devWorks/undecProjects/HexaGrid/HexaGrid.Web/obj/Debug/net9.0/compressed/t26f15851i-mjblqvtjyw.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3149"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"XsB+PYIJS1duDl7jyvF02XBT1nVVsvJS07wS8wr8EAo=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jul 2025 16:08:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mjblqvtjyw"}, {"Name": "label", "Value": "monitor.html.gz"}, {"Name": "integrity", "Value": "sha256-XsB+PYIJS1duDl7jyvF02XBT1nVVsvJS07wS8wr8EAo="}]}]}