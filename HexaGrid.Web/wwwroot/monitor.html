<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HexaGrid Monitor</title>
    <script src="https://unpkg.com/@microsoft/signalr@latest/dist/browser/signalr.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.running { background: #e3f2fd; color: #1976d2; }
        .status.success { background: #e8f5e8; color: #2e7d32; }
        .status.error { background: #ffebee; color: #c62828; }
        .status.warning { background: #fff3e0; color: #f57c00; }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4caf50, #8bc34a);
            transition: width 0.3s ease;
        }
        .node-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;
        }
        .node-item {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            background: #fafafa;
        }
        .log-container {
            max-height: 400px;
            overflow-y: auto;
            background: #1e1e1e;
            color: #ffffff;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        .log-entry.info { color: #81c784; }
        .log-entry.warning { color: #ffb74d; }
        .log-entry.error { color: #e57373; }
        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 15px;
            border-radius: 20px;
            font-weight: bold;
        }
        .connection-status.connected { background: #4caf50; color: white; }
        .connection-status.disconnected { background: #f44336; color: white; }
    </style>
</head>
<body>
    <div class="connection-status" id="connectionStatus">Disconnected</div>
    
    <div class="container">
        <div class="header">
            <h1>🔷 HexaGrid Monitor</h1>
            <p>Real-time workflow execution monitoring</p>
        </div>

        <div class="card">
            <h2>Graph Execution Status</h2>
            <div id="graphStatus">
                <p>No active graph execution</p>
            </div>
        </div>

        <div class="card">
            <h2>Progress</h2>
            <div id="progressContainer" style="display: none;">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill" style="width: 0%"></div>
                </div>
                <div id="progressText">0% complete</div>
            </div>
        </div>

        <div class="card">
            <h2>Nodes</h2>
            <div class="node-list" id="nodeList">
                <p>No nodes to display</p>
            </div>
        </div>

        <div class="card">
            <h2>Execution Log</h2>
            <div class="log-container" id="logContainer">
                <div class="log-entry info">Monitor ready. Waiting for graph execution...</div>
            </div>
        </div>
    </div>

    <script>
        const connection = new signalR.HubConnectionBuilder()
            .withUrl("/monitoring")
            .build();

        let currentGraphId = null;
        let nodes = new Map();

        // Connection management
        connection.start().then(function () {
            updateConnectionStatus(true);
            addLog("info", "Connected to monitoring hub");
        }).catch(function (err) {
            updateConnectionStatus(false);
            addLog("error", "Failed to connect: " + err.toString());
        });

        connection.onclose(function () {
            updateConnectionStatus(false);
            addLog("warning", "Connection lost. Attempting to reconnect...");
            setTimeout(() => connection.start(), 5000);
        });

        // Event handlers
        connection.on("GraphStarted", function (graphId, eventData) {
            currentGraphId = graphId;
            updateGraphStatus("running", `Graph "${eventData.graphName}" started with ${eventData.totalNodes} nodes`);
            addLog("info", `Graph execution started: ${eventData.graphName}`);
            showProgress();
        });

        connection.on("GraphCompleted", function (graphId, eventData) {
            const status = eventData.success ? "success" : "error";
            updateGraphStatus(status, `Graph completed in ${formatDuration(eventData.duration)}. Success rate: ${(eventData.successRate * 100).toFixed(1)}%`);
            addLog(eventData.success ? "info" : "error", `Graph execution ${eventData.success ? "completed successfully" : "failed"}`);
            hideProgress();
        });

        connection.on("NodeStarted", function (graphId, eventData) {
            updateNode(eventData.nodeId, {
                id: eventData.nodeId,
                type: eventData.nodeType,
                name: eventData.nodeName,
                status: "running",
                startTime: eventData.startTime
            });
            addLog("info", `Node ${eventData.nodeId} (${eventData.nodeType}) started`);
        });

        connection.on("NodeCompleted", function (graphId, eventData) {
            updateNode(eventData.nodeId, {
                status: eventData.success ? "success" : "error",
                duration: eventData.duration,
                attemptCount: eventData.attemptCount,
                errorMessage: eventData.errorMessage
            });
            const status = eventData.success ? "info" : "error";
            addLog(status, `Node ${eventData.nodeId} ${eventData.success ? "completed" : "failed"} in ${formatDuration(eventData.duration)}`);
        });

        connection.on("NodeRetrying", function (graphId, eventData) {
            addLog("warning", `Node ${eventData.nodeId} retry ${eventData.attemptNumber}/${eventData.maxAttempts}: ${eventData.errorMessage}`);
        });

        connection.on("GraphProgress", function (graphId, eventData) {
            updateProgress(eventData.progressPercentage, eventData.completedNodes, eventData.totalNodes);
        });

        connection.on("LogMessage", function (graphId, eventData) {
            addLog(eventData.level, eventData.message);
        });

        // Helper functions
        function updateConnectionStatus(connected) {
            const status = document.getElementById("connectionStatus");
            status.textContent = connected ? "Connected" : "Disconnected";
            status.className = "connection-status " + (connected ? "connected" : "disconnected");
        }

        function updateGraphStatus(status, message) {
            const container = document.getElementById("graphStatus");
            container.innerHTML = `<span class="status ${status}">${status.toUpperCase()}</span> ${message}`;
        }

        function showProgress() {
            document.getElementById("progressContainer").style.display = "block";
        }

        function hideProgress() {
            document.getElementById("progressContainer").style.display = "none";
        }

        function updateProgress(percentage, completed, total) {
            document.getElementById("progressFill").style.width = percentage + "%";
            document.getElementById("progressText").textContent = `${percentage.toFixed(1)}% complete (${completed}/${total} nodes)`;
        }

        function updateNode(nodeId, data) {
            if (!nodes.has(nodeId)) {
                nodes.set(nodeId, { id: nodeId });
            }
            Object.assign(nodes.get(nodeId), data);
            renderNodes();
        }

        function renderNodes() {
            const container = document.getElementById("nodeList");
            if (nodes.size === 0) {
                container.innerHTML = "<p>No nodes to display</p>";
                return;
            }

            container.innerHTML = Array.from(nodes.values()).map(node => `
                <div class="node-item">
                    <h4>${node.name || node.id} <span class="status ${node.status || 'pending'}">${(node.status || 'pending').toUpperCase()}</span></h4>
                    <p><strong>Type:</strong> ${node.type || 'Unknown'}</p>
                    ${node.duration ? `<p><strong>Duration:</strong> ${formatDuration(node.duration)}</p>` : ''}
                    ${node.attemptCount > 1 ? `<p><strong>Attempts:</strong> ${node.attemptCount}</p>` : ''}
                    ${node.errorMessage ? `<p><strong>Error:</strong> ${node.errorMessage}</p>` : ''}
                </div>
            `).join('');
        }

        function addLog(level, message) {
            const container = document.getElementById("logContainer");
            const timestamp = new Date().toLocaleTimeString();
            const entry = document.createElement("div");
            entry.className = `log-entry ${level}`;
            entry.textContent = `[${timestamp}] ${message}`;
            container.appendChild(entry);
            container.scrollTop = container.scrollHeight;
        }

        function formatDuration(duration) {
            if (typeof duration === 'string') {
                // Parse .NET TimeSpan format
                const match = duration.match(/(\d+):(\d+):(\d+)\.?(\d+)?/);
                if (match) {
                    const hours = parseInt(match[1]);
                    const minutes = parseInt(match[2]);
                    const seconds = parseInt(match[3]);
                    const ms = match[4] ? parseInt(match[4].substring(0, 3)) : 0;
                    
                    if (hours > 0) return `${hours}h ${minutes}m ${seconds}s`;
                    if (minutes > 0) return `${minutes}m ${seconds}s`;
                    if (seconds > 0) return `${seconds}.${ms.toString().padStart(3, '0')}s`;
                    return `${ms}ms`;
                }
            }
            return duration.toString();
        }

        // Auto-join monitoring for demo purposes
        // In a real app, you'd join specific graph executions
        window.addEventListener('load', function() {
            // You can modify this to join specific graph monitoring
            // connection.invoke("JoinGraphExecution", "demo-graph");
        });
    </script>
</body>
</html>
