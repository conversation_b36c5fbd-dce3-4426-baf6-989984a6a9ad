#!/bin/bash
set -e

# HexaGrid Installation Script
# Usage: curl -sfL https://get.hexagrid.dev | sh -
# Or: wget -qO- https://get.hexagrid.dev | sh -

HEXAGRID_VERSION="${HEXAGRID_VERSION:-latest}"
HEXAGRID_URL="${HEXAGRID_URL:-https://github.com/your-org/hexagrid/releases}"
INSTALL_DIR="${INSTALL_DIR:-/usr/local/bin}"
SYSTEMD_DIR="${SYSTEMD_DIR:-/etc/systemd/system}"
CONFIG_DIR="${CONFIG_DIR:-/etc/hexagrid}"
DATA_DIR="${DATA_DIR:-/var/lib/hexagrid}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# Check if running as root
check_root() {
    if [ "$EUID" -ne 0 ]; then
        error "This script must be run as root"
        exit 1
    fi
}

# Detect system architecture
detect_arch() {
    case $(uname -m) in
        x86_64)
            ARCH="amd64"
            ;;
        aarch64)
            ARCH="arm64"
            ;;
        armv7l)
            ARCH="arm"
            ;;
        *)
            error "Unsupported architecture: $(uname -m)"
            exit 1
            ;;
    esac
}

# Detect operating system
detect_os() {
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        OS=$ID
        VERSION=$VERSION_ID
    else
        error "Cannot detect operating system"
        exit 1
    fi
}

# Download HexaGrid binary
download_hexagrid() {
    info "Downloading HexaGrid ${HEXAGRID_VERSION} for ${OS}/${ARCH}..."
    
    # TODO: Replace with actual download URL
    DOWNLOAD_URL="${HEXAGRID_URL}/download/${HEXAGRID_VERSION}/hexagrid-${OS}-${ARCH}"
    
    # For now, we'll build from source
    if command -v dotnet >/dev/null 2>&1; then
        info "Building HexaGrid from source..."
        
        # Clone or use existing source
        if [ ! -d "/tmp/hexagrid-build" ]; then
            git clone https://github.com/your-org/hexagrid.git /tmp/hexagrid-build || {
                error "Failed to clone HexaGrid repository"
                exit 1
            }
        fi
        
        cd /tmp/hexagrid-build
        dotnet publish HexaGrid.CLI/HexaGrid.CLI.csproj -c Release -r linux-x64 --self-contained -o /tmp/hexagrid-output
        
        cp /tmp/hexagrid-output/hexagrid "$INSTALL_DIR/hexagrid"
        chmod +x "$INSTALL_DIR/hexagrid"
    else
        error ".NET SDK not found. Please install .NET 10.0 or later"
        exit 1
    fi
}

# Create directories
create_directories() {
    info "Creating directories..."
    mkdir -p "$CONFIG_DIR"
    mkdir -p "$DATA_DIR"
    mkdir -p "$SYSTEMD_DIR"
}

# Create systemd service files
create_systemd_services() {
    info "Creating systemd service files..."
    
    # HexaGrid Server (Controller) service
    cat > "$SYSTEMD_DIR/hexagrid-server.service" << EOF
[Unit]
Description=HexaGrid Controller
Documentation=https://github.com/your-org/hexagrid
Wants=network-online.target
After=network-online.target
AssertFileIsExecutable=$INSTALL_DIR/hexagrid

[Service]
Type=notify
EnvironmentFile=-$CONFIG_DIR/hexagrid-server
KillMode=process
Delegate=yes
LimitNOFILE=1048576
LimitNPROC=infinity
LimitCORE=infinity
TasksMax=infinity
TimeoutStartSec=0
Restart=always
RestartSec=5s
ExecStartPre=/bin/sh -xc '! /usr/bin/systemctl is-enabled --quiet hexagrid-agent'
ExecStart=$INSTALL_DIR/hexagrid server \\
    --data-dir $DATA_DIR \\
    --bind-address 0.0.0.0 \\
    --port 6443

[Install]
WantedBy=multi-user.target
EOF

    # HexaGrid Agent service
    cat > "$SYSTEMD_DIR/hexagrid-agent.service" << EOF
[Unit]
Description=HexaGrid Agent
Documentation=https://github.com/your-org/hexagrid
Wants=network-online.target
After=network-online.target
AssertFileIsExecutable=$INSTALL_DIR/hexagrid

[Service]
Type=notify
EnvironmentFile=-$CONFIG_DIR/hexagrid-agent
KillMode=process
Delegate=yes
LimitNOFILE=1048576
LimitNPROC=infinity
LimitCORE=infinity
TasksMax=infinity
TimeoutStartSec=0
Restart=always
RestartSec=5s
ExecStartPre=/bin/sh -xc '! /usr/bin/systemctl is-enabled --quiet hexagrid-server'
ExecStart=$INSTALL_DIR/hexagrid agent \\
    --server \${HEXAGRID_SERVER} \\
    --token \${HEXAGRID_TOKEN} \\
    --data-dir $DATA_DIR

[Install]
WantedBy=multi-user.target
EOF
}

# Create configuration files
create_config_files() {
    info "Creating configuration files..."
    
    # Server configuration
    if [ ! -f "$CONFIG_DIR/hexagrid-server" ]; then
        cat > "$CONFIG_DIR/hexagrid-server" << EOF
# HexaGrid Server Configuration
# HEXAGRID_TOKEN=your-cluster-token-here
# HEXAGRID_DATA_DIR=$DATA_DIR
# HEXAGRID_BIND_ADDRESS=0.0.0.0
# HEXAGRID_PORT=6443
EOF
    fi
    
    # Agent configuration template
    if [ ! -f "$CONFIG_DIR/hexagrid-agent" ]; then
        cat > "$CONFIG_DIR/hexagrid-agent" << EOF
# HexaGrid Agent Configuration
# HEXAGRID_SERVER=https://your-controller:6443
# HEXAGRID_TOKEN=your-cluster-token-here
# HEXAGRID_NODE_NAME=\$(hostname)
# HEXAGRID_DATA_DIR=$DATA_DIR
EOF
    fi
}

# Set up shell completion
setup_completion() {
    info "Setting up shell completion..."
    
    # Bash completion
    if [ -d "/etc/bash_completion.d" ]; then
        "$INSTALL_DIR/hexagrid" completion bash > /etc/bash_completion.d/hexagrid
    fi
    
    # Zsh completion
    if [ -d "/usr/share/zsh/site-functions" ]; then
        "$INSTALL_DIR/hexagrid" completion zsh > /usr/share/zsh/site-functions/_hexagrid
    fi
}

# Main installation function
main() {
    info "🔷 HexaGrid Installation Script"
    info "=============================="
    
    check_root
    detect_arch
    detect_os
    
    info "System: ${OS} ${VERSION} (${ARCH})"
    
    create_directories
    download_hexagrid
    create_systemd_services
    create_config_files
    setup_completion
    
    # Reload systemd
    systemctl daemon-reload
    
    success "HexaGrid installation completed!"
    echo
    info "Next steps:"
    echo "  1. Start HexaGrid server:"
    echo "     systemctl enable --now hexagrid-server"
    echo
    echo "  2. Get the cluster token:"
    echo "     journalctl -u hexagrid-server | grep 'Generated cluster token'"
    echo
    echo "  3. On agent nodes, configure and start the agent:"
    echo "     # Edit /etc/hexagrid/hexagrid-agent with server and token"
    echo "     systemctl enable --now hexagrid-agent"
    echo
    echo "  4. Verify cluster status:"
    echo "     hexagrid get nodes"
    echo "     hexagrid status"
    echo
    info "Documentation: https://github.com/your-org/hexagrid"
}

# Run main function
main "$@"
