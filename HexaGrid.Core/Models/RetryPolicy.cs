using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace HexaGrid.Core.Models
{
    public class RetryPolicy
    {
        public int MaxAttempts { get; set; } = 3;
        public TimeSpan InitialDelay { get; set; } = TimeSpan.FromSeconds(1);
        public TimeSpan MaxDelay { get; set; } = TimeSpan.FromMinutes(5);
        public double BackoffMultiplier { get; set; } = 2.0;
        public RetryStrategy Strategy { get; set; } = RetryStrategy.ExponentialBackoff;
        public List<Type> RetryableExceptions { get; set; } = new();
        public Func<Exception, bool>? ShouldRetry { get; set; }

        public static RetryPolicy Default => new()
        {
            MaxAttempts = 3,
            InitialDelay = TimeSpan.FromSeconds(1),
            Strategy = RetryStrategy.ExponentialBackoff
        };

        public static RetryPolicy NoRetry => new()
        {
            MaxAttempts = 1
        };

        public static RetryPolicy HttpRetry => new()
        {
            MaxAttempts = 5,
            InitialDelay = TimeSpan.FromSeconds(2),
            MaxDelay = TimeSpan.FromMinutes(2),
            Strategy = RetryStrategy.ExponentialBackoff,
            ShouldRetry = ex => ex is HttpRequestException || ex is TaskCanceledException
        };
    }

    public enum RetryStrategy
    {
        FixedDelay,
        LinearBackoff,
        ExponentialBackoff,
        Jittered
    }

    public class RetryExecutor
    {
        public static async Task<T> ExecuteWithRetry<T>(
            Func<Task<T>> operation,
            RetryPolicy policy,
            Action<int, Exception>? onRetry = null)
        {
            Exception? lastException = null;
            
            for (int attempt = 1; attempt <= policy.MaxAttempts; attempt++)
            {
                try
                {
                    return await operation();
                }
                catch (Exception ex)
                {
                    lastException = ex;
                    
                    // Check if we should retry this exception
                    if (!ShouldRetryException(ex, policy))
                    {
                        throw;
                    }
                    
                    // Don't delay on the last attempt
                    if (attempt == policy.MaxAttempts)
                    {
                        break;
                    }
                    
                    onRetry?.Invoke(attempt, ex);
                    
                    var delay = CalculateDelay(attempt, policy);
                    await Task.Delay(delay);
                }
            }
            
            throw lastException ?? new InvalidOperationException("Retry operation failed");
        }

        public static async Task ExecuteWithRetry(
            Func<Task> operation,
            RetryPolicy policy,
            Action<int, Exception>? onRetry = null)
        {
            await ExecuteWithRetry(async () =>
            {
                await operation();
                return true;
            }, policy, onRetry);
        }

        private static bool ShouldRetryException(Exception ex, RetryPolicy policy)
        {
            // Use custom predicate if provided
            if (policy.ShouldRetry != null)
            {
                return policy.ShouldRetry(ex);
            }
            
            // Check against retryable exception types
            if (policy.RetryableExceptions.Count > 0)
            {
                return policy.RetryableExceptions.Contains(ex.GetType());
            }
            
            // Default: retry most exceptions except for critical ones
            return ex is not (ArgumentException or ArgumentNullException or InvalidOperationException);
        }

        private static TimeSpan CalculateDelay(int attempt, RetryPolicy policy)
        {
            var delay = policy.Strategy switch
            {
                RetryStrategy.FixedDelay => policy.InitialDelay,
                RetryStrategy.LinearBackoff => TimeSpan.FromMilliseconds(policy.InitialDelay.TotalMilliseconds * attempt),
                RetryStrategy.ExponentialBackoff => TimeSpan.FromMilliseconds(
                    policy.InitialDelay.TotalMilliseconds * Math.Pow(policy.BackoffMultiplier, attempt - 1)),
                RetryStrategy.Jittered => TimeSpan.FromMilliseconds(
                    policy.InitialDelay.TotalMilliseconds * Math.Pow(policy.BackoffMultiplier, attempt - 1) * 
                    (0.5 + Random.Shared.NextDouble() * 0.5)),
                _ => policy.InitialDelay
            };
            
            return delay > policy.MaxDelay ? policy.MaxDelay : delay;
        }
    }

    public class ExecutionResult<T>
    {
        public bool Success { get; set; }
        public T? Value { get; set; }
        public Exception? Exception { get; set; }
        public int AttemptCount { get; set; }
        public TimeSpan TotalDuration { get; set; }
        public List<Exception> AllExceptions { get; set; } = new();

        public static ExecutionResult<T> CreateSuccess(T value, int attemptCount, TimeSpan duration)
        {
            return new ExecutionResult<T>
            {
                Success = true,
                Value = value,
                AttemptCount = attemptCount,
                TotalDuration = duration
            };
        }

        public static ExecutionResult<T> CreateFailure(Exception exception, int attemptCount, TimeSpan duration, List<Exception> allExceptions)
        {
            return new ExecutionResult<T>
            {
                Success = false,
                Exception = exception,
                AttemptCount = attemptCount,
                TotalDuration = duration,
                AllExceptions = allExceptions
            };
        }
    }
}
