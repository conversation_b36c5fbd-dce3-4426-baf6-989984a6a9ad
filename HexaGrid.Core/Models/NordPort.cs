using System.Collections.Generic;

namespace HexaGrid.Core.Models
{
    public enum PortType
    {
        Input,
        Output
    }

    public class NodePort(string name, PortType type, Type? dataType = null)
    {
        public string Name { get; set; } = name;
        public PortType Type { get; set; } = type;
        public Type DataType { get; set; } = dataType ?? typeof(object);
        public object? DefaultValue { get; set; }
        public bool IsRequired { get; set; } = false;
        public string Description { get; set; } = string.Empty;
    }

    public class NodePortValue(string portName, object? value = null)
    {
        public string PortName { get; set; } = portName;
        public object? Value { get; set; } = value;
        public Type ValueType { get; set; } = value?.GetType() ?? typeof(object);

        public T? GetValue<T>()
        {
            if (Value is T typedValue)
                return typedValue;

            try
            {
                return (T?)Convert.ChangeType(Value, typeof(T));
            }
            catch
            {
                return default(T);
            }
        }
    }
}