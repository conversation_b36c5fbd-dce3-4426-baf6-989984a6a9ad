using HexaGrid.Core.Models;
using HexaGrid.Core.Nodes;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading.Tasks;

namespace HexaGrid.Core.Graph
{
    public class GraphExecutor(INodeFactory nodeFactory)
    {
        private readonly INodeFactory _nodeFactory = nodeFactory;

        public static async Task<GraphExecutionResult> Execute(NodeGraph graph)
        {
            var executor = new GraphExecutor(new DefaultNodeFactory());
            return await executor.ExecuteAsync(graph);
        }

        public async Task<GraphExecutionResult> ExecuteAsync(NodeGraph graph, GraphExecutionOptions? options = null)
        {
            options ??= GraphExecutionOptions.Default;
            var result = new GraphExecutionResult();
            var context = new Dictionary<string, object>(graph.Variables);
            var stopwatch = Stopwatch.StartNew();

            try
            {
                var executionOrder = graph.GetExecutionOrder();
                result.TotalNodes = executionOrder.Count;

                foreach (var nodeDefinition in executionOrder)
                {
                    var nodeInstance = _nodeFactory.CreateNode(nodeDefinition.Type);
                    if (nodeInstance == null)
                    {
                        result.Success = false;
                        result.ErrorMessage = $"Unknown node type: {nodeDefinition.Type}";
                        return result;
                    }

                    // Configure node with inputs from connections and node definition
                    ConfigureNodeInputs(nodeInstance, nodeDefinition, graph, context);

                    // Execute the node with retry logic
                    var nodeResult = await ExecuteNodeWithRetryAsync(nodeInstance, nodeDefinition, context, options);

                    result.NodeResults[nodeDefinition.Id] = nodeResult;
                    result.ExecutedNodes++;

                    if (!nodeResult.Success)
                    {
                        result.Success = false;
                        result.ErrorMessage = $"Node {nodeDefinition.Id} ({nodeDefinition.Type}) failed: {nodeResult.ErrorMessage}";

                        if (options.StopOnFirstError)
                        {
                            return result;
                        }

                        // Continue execution if configured to do so
                        result.FailedNodes++;
                        continue;
                    }

                    // Update context with node outputs
                    foreach (var output in nodeResult.Outputs)
                    {
                        context[$"{nodeDefinition.Id}.{output.Key}"] = output.Value;
                    }

                    result.SuccessfulNodes++;
                }

                result.Success = result.FailedNodes == 0;
                result.FinalContext = context;
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = ex.Message;
                result.Exception = ex;
            }
            finally
            {
                stopwatch.Stop();
                result.ExecutionDuration = stopwatch.Elapsed;
            }

            return result;
        }

        private void ConfigureNodeInputs(INode nodeInstance, Node nodeDefinition, NodeGraph graph, Dictionary<string, object> context)
        {
            // Set inputs from node definition
            if (nodeInstance is IConfigurableNode configurableNode)
            {
                configurableNode.Configure(nodeDefinition.Inputs);
            }

            // Set inputs from connections
            var incomingConnections = graph.GetIncomingConnections(nodeDefinition.Id);
            foreach (var connection in incomingConnections)
            {
                var sourceKey = $"{connection.FromNodeId}.{connection.FromPort}";
                if (context.ContainsKey(sourceKey))
                {
                    if (nodeInstance is IConfigurableNode configurable)
                    {
                        configurable.SetInput(connection.ToPort, context[sourceKey]);
                    }
                }
            }
        }

        private async Task<NodeResult> ExecuteNodeWithRetryAsync(INode node, Node nodeDefinition, Dictionary<string, object> context, GraphExecutionOptions options)
        {
            var retryPolicy = GetRetryPolicyForNode(nodeDefinition, options);
            var nodeStopwatch = Stopwatch.StartNew();
            var allExceptions = new List<Exception>();

            try
            {
                var result = await RetryExecutor.ExecuteWithRetry(
                    async () => await ExecuteNodeAsync(node, context),
                    retryPolicy,
                    (attempt, ex) =>
                    {
                        allExceptions.Add(ex);
                        Console.WriteLine($"   ⚠️ Node {nodeDefinition.Id} attempt {attempt} failed: {ex.Message}");
                        if (attempt < retryPolicy.MaxAttempts)
                        {
                            Console.WriteLine($"   🔄 Retrying in {CalculateNextDelay(attempt, retryPolicy)}...");
                        }
                    });

                nodeStopwatch.Stop();
                result.ExecutionDuration = nodeStopwatch.Elapsed;
                result.AttemptCount = allExceptions.Count + 1;
                return result;
            }
            catch (Exception ex)
            {
                nodeStopwatch.Stop();
                allExceptions.Add(ex);

                var failedResult = NodeResult.CreateError(ex.Message);
                failedResult.ExecutionDuration = nodeStopwatch.Elapsed;
                failedResult.AttemptCount = allExceptions.Count;
                failedResult.AllExceptions = allExceptions;
                return failedResult;
            }
        }

        private async Task<NodeResult> ExecuteNodeAsync(INode node, Dictionary<string, object> context)
        {
            try
            {
                await node.ExecuteAsync(context);

                var outputs = new Dictionary<string, object>();
                if (node is IOutputNode outputNode)
                {
                    outputs = outputNode.GetOutputs();
                }

                return NodeResult.CreateSuccess(outputs);
            }
            catch (Exception ex)
            {
                return NodeResult.CreateError(ex.Message);
            }
        }

        private static RetryPolicy GetRetryPolicyForNode(Node nodeDefinition, GraphExecutionOptions options)
        {
            // Check if node has custom retry policy
            if (nodeDefinition.Properties.TryGetValue("retryPolicy", out var policyObj) && policyObj is RetryPolicy customPolicy)
            {
                return customPolicy;
            }

            // Use node-type specific policies
            return nodeDefinition.Type switch
            {
                "HttpRequestNode" => options.HttpRetryPolicy ?? RetryPolicy.HttpRetry,
                "WaitNode" => RetryPolicy.NoRetry, // Wait nodes shouldn't retry
                _ => options.DefaultRetryPolicy ?? RetryPolicy.Default
            };
        }

        private static TimeSpan CalculateNextDelay(int attempt, RetryPolicy policy)
        {
            return policy.Strategy switch
            {
                RetryStrategy.FixedDelay => policy.InitialDelay,
                RetryStrategy.LinearBackoff => TimeSpan.FromMilliseconds(policy.InitialDelay.TotalMilliseconds * attempt),
                RetryStrategy.ExponentialBackoff => TimeSpan.FromMilliseconds(
                    policy.InitialDelay.TotalMilliseconds * Math.Pow(policy.BackoffMultiplier, attempt - 1)),
                _ => policy.InitialDelay
            };
        }
    }

    public class GraphExecutionOptions
    {
        public bool StopOnFirstError { get; set; } = true;
        public RetryPolicy? DefaultRetryPolicy { get; set; }
        public RetryPolicy? HttpRetryPolicy { get; set; }
        public TimeSpan? MaxExecutionTime { get; set; }
        public bool EnableDetailedLogging { get; set; } = true;

        public static GraphExecutionOptions Default => new()
        {
            StopOnFirstError = true,
            DefaultRetryPolicy = RetryPolicy.Default,
            HttpRetryPolicy = RetryPolicy.HttpRetry,
            EnableDetailedLogging = true
        };

        public static GraphExecutionOptions ContinueOnError => new()
        {
            StopOnFirstError = false,
            DefaultRetryPolicy = RetryPolicy.Default,
            HttpRetryPolicy = RetryPolicy.HttpRetry,
            EnableDetailedLogging = true
        };
    }

    public class GraphExecutionResult
    {
        public bool Success { get; set; } = true;
        public string? ErrorMessage { get; set; }
        public Exception? Exception { get; set; }
        public Dictionary<string, NodeResult> NodeResults { get; set; } = new();
        public Dictionary<string, object> FinalContext { get; set; } = new();
        public TimeSpan ExecutionDuration { get; set; }
        public int TotalNodes { get; set; }
        public int ExecutedNodes { get; set; }
        public int SuccessfulNodes { get; set; }
        public int FailedNodes { get; set; }
        public DateTime StartTime { get; set; } = DateTime.UtcNow;
        public DateTime? EndTime { get; set; }

        public double SuccessRate => TotalNodes > 0 ? (double)SuccessfulNodes / TotalNodes : 0;
        public bool IsPartialSuccess => SuccessfulNodes > 0 && FailedNodes > 0;
    }
}