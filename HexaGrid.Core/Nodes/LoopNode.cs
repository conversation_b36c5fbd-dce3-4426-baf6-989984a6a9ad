using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace HexaGrid.Core.Nodes
{
    public class LoopNode : INode, IConfigurableNode, IOutputNode
    {
        public string Name => "LoopNode";
        
        private string _loopType = "count"; // count, while, foreach
        private int _count = 1;
        private int _maxIterations = 1000; // Safety limit
        private object? _condition;
        private string _conditionOperator = "istrue";
        private object? _compareValue;
        private List<object> _items = new();
        private readonly Dictionary<string, object> _outputs = new();

        public Task ExecuteAsync(Dictionary<string, object> context)
        {
            int iterations = 0;
            var results = new List<Dictionary<string, object>>();
            
            Console.WriteLine($"🔄 Starting {_loopType} loop");

            try
            {
                switch (_loopType.ToLower())
                {
                    case "count":
                        iterations = ExecuteCountLoop(context, results);
                        break;
                    case "while":
                        iterations = ExecuteWhileLoop(context, results);
                        break;
                    case "foreach":
                        iterations = ExecuteForEachLoop(context, results);
                        break;
                    default:
                        throw new InvalidOperationException($"Unknown loop type: {_loopType}");
                }

                _outputs["success"] = true;
                _outputs["iterations"] = iterations;
                _outputs["results"] = results;
                _outputs["loopType"] = _loopType;
                _outputs["completed"] = true;

                Console.WriteLine($"   ✅ Loop completed after {iterations} iterations");
            }
            catch (Exception ex)
            {
                _outputs["success"] = false;
                _outputs["error"] = ex.Message;
                _outputs["iterations"] = iterations;
                _outputs["results"] = results;
                Console.WriteLine($"   ❌ Loop failed after {iterations} iterations: {ex.Message}");
                throw;
            }
            
            return Task.CompletedTask;
        }

        private int ExecuteCountLoop(Dictionary<string, object> context, List<Dictionary<string, object>> results)
        {
            for (int i = 0; i < _count && i < _maxIterations; i++)
            {
                var iterationContext = new Dictionary<string, object>(context)
                {
                    ["loopIndex"] = i,
                    ["loopCount"] = _count,
                    ["isFirstIteration"] = i == 0,
                    ["isLastIteration"] = i == _count - 1
                };

                var iterationResult = new Dictionary<string, object>
                {
                    ["index"] = i,
                    ["context"] = iterationContext
                };

                results.Add(iterationResult);
                
                // Update main context with loop variables
                context["loopIndex"] = i;
                context["loopCount"] = _count;
                context["currentIteration"] = i + 1;
            }

            return Math.Min(_count, _maxIterations);
        }

        private int ExecuteWhileLoop(Dictionary<string, object> context, List<Dictionary<string, object>> results)
        {
            int iterations = 0;
            
            while (iterations < _maxIterations && EvaluateCondition(context))
            {
                var iterationContext = new Dictionary<string, object>(context)
                {
                    ["loopIndex"] = iterations,
                    ["isFirstIteration"] = iterations == 0
                };

                var iterationResult = new Dictionary<string, object>
                {
                    ["index"] = iterations,
                    ["context"] = iterationContext,
                    ["conditionMet"] = true
                };

                results.Add(iterationResult);
                
                // Update main context with loop variables
                context["loopIndex"] = iterations;
                context["currentIteration"] = iterations + 1;
                
                iterations++;
            }

            return iterations;
        }

        private int ExecuteForEachLoop(Dictionary<string, object> context, List<Dictionary<string, object>> results)
        {
            int iterations = 0;
            
            foreach (var item in _items)
            {
                if (iterations >= _maxIterations) break;
                
                var iterationContext = new Dictionary<string, object>(context)
                {
                    ["loopIndex"] = iterations,
                    ["currentItem"] = item,
                    ["totalItems"] = _items.Count,
                    ["isFirstIteration"] = iterations == 0,
                    ["isLastIteration"] = iterations == _items.Count - 1
                };

                var iterationResult = new Dictionary<string, object>
                {
                    ["index"] = iterations,
                    ["item"] = item,
                    ["context"] = iterationContext
                };

                results.Add(iterationResult);
                
                // Update main context with loop variables
                context["loopIndex"] = iterations;
                context["currentItem"] = item;
                context["currentIteration"] = iterations + 1;
                
                iterations++;
            }

            return iterations;
        }

        private bool EvaluateCondition(Dictionary<string, object> context)
        {
            // Get the current value of the condition (might be a context variable)
            var currentCondition = _condition;
            if (_condition is string conditionKey && context.ContainsKey(conditionKey))
            {
                currentCondition = context[conditionKey];
            }

            try
            {
                return _conditionOperator.ToLower() switch
                {
                    "istrue" => IsTrue(currentCondition),
                    "isfalse" => !IsTrue(currentCondition),
                    "isnull" => currentCondition == null,
                    "isnotnull" => currentCondition != null,
                    "equals" or "eq" => AreEqual(currentCondition, _compareValue),
                    "notequals" or "ne" => !AreEqual(currentCondition, _compareValue),
                    "greaterthan" or "gt" => CompareValues(currentCondition, _compareValue) > 0,
                    "lessthan" or "lt" => CompareValues(currentCondition, _compareValue) < 0,
                    _ => false
                };
            }
            catch
            {
                return false;
            }
        }

        private static bool IsTrue(object? value)
        {
            if (value == null) return false;
            if (value is bool boolValue) return boolValue;
            if (value is string stringValue) 
                return stringValue.ToLower() is "true" or "yes" or "1" or "on";
            if (IsNumeric(value)) return Convert.ToDouble(value) != 0;
            return false;
        }

        private static bool AreEqual(object? a, object? b)
        {
            if (a == null && b == null) return true;
            if (a == null || b == null) return false;
            
            if (IsNumeric(a) && IsNumeric(b))
            {
                return Convert.ToDouble(a) == Convert.ToDouble(b);
            }
            
            return a.ToString() == b.ToString();
        }

        private static int CompareValues(object? a, object? b)
        {
            if (a == null && b == null) return 0;
            if (a == null) return -1;
            if (b == null) return 1;
            
            if (IsNumeric(a) && IsNumeric(b))
            {
                return Convert.ToDouble(a).CompareTo(Convert.ToDouble(b));
            }
            
            return string.Compare(a.ToString(), b.ToString(), StringComparison.OrdinalIgnoreCase);
        }

        private static bool IsNumeric(object? value)
        {
            return value != null && (value is int || value is long || value is float || value is double || value is decimal ||
                   double.TryParse(value.ToString(), out _));
        }

        public void Configure(Dictionary<string, object> inputs)
        {
            if (inputs.TryGetValue("loopType", out var loopType))
                _loopType = loopType?.ToString() ?? "count";
            
            if (inputs.TryGetValue("count", out var count))
                _count = Convert.ToInt32(count);
            
            if (inputs.TryGetValue("maxIterations", out var maxIter))
                _maxIterations = Convert.ToInt32(maxIter);
            
            if (inputs.TryGetValue("condition", out var condition))
                _condition = condition;
            
            if (inputs.TryGetValue("conditionOperator", out var condOp))
                _conditionOperator = condOp?.ToString() ?? "istrue";
            
            if (inputs.TryGetValue("compareValue", out var compareValue))
                _compareValue = compareValue;
            
            if (inputs.TryGetValue("items", out var items))
            {
                if (items is List<object> itemList)
                {
                    _items = new List<object>(itemList);
                }
                else if (items is object[] itemArray)
                {
                    _items = new List<object>(itemArray);
                }
            }
        }

        public void SetInput(string portName, object value)
        {
            switch (portName.ToLower())
            {
                case "looptype":
                case "type":
                    _loopType = value?.ToString() ?? "count";
                    break;
                case "count":
                case "iterations":
                    _count = Convert.ToInt32(value);
                    break;
                case "maxiterations":
                case "max":
                    _maxIterations = Convert.ToInt32(value);
                    break;
                case "condition":
                    _condition = value;
                    break;
                case "conditionoperator":
                case "operator":
                    _conditionOperator = value?.ToString() ?? "istrue";
                    break;
                case "comparevalue":
                case "compare":
                    _compareValue = value;
                    break;
                case "items":
                case "collection":
                    if (value is List<object> itemList)
                    {
                        _items = new List<object>(itemList);
                    }
                    else if (value is object[] itemArray)
                    {
                        _items = new List<object>(itemArray);
                    }
                    break;
            }
        }

        public Dictionary<string, object> GetOutputs()
        {
            return new Dictionary<string, object>(_outputs);
        }
    }
}
