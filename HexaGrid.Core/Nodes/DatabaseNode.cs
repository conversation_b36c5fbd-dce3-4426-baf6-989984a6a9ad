using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Threading.Tasks;

namespace HexaGrid.Core.Nodes
{
    public class DatabaseNode : INode, IConfigurableNode, IOutputNode
    {
        public string Name => "DatabaseNode";
        
        private string _connectionString = "";
        private string _providerName = "System.Data.SqlClient";
        private string _query = "";
        private string _operation = "select"; // select, execute, scalar
        private Dictionary<string, object> _parameters = new();
        private int _commandTimeout = 30;
        private readonly Dictionary<string, object> _outputs = new();

        public async Task ExecuteAsync(Dictionary<string, object> context)
        {
            if (string.IsNullOrEmpty(_connectionString))
            {
                throw new ArgumentException("Connection string is required");
            }

            if (string.IsNullOrEmpty(_query))
            {
                throw new ArgumentException("Query is required");
            }

            try
            {
                Console.WriteLine($"🗄️ Database operation '{_operation}': {_query.Substring(0, Math.Min(50, _query.Length))}...");

                var factory = GetDbProviderFactory();
                using var connection = factory.CreateConnection();
                connection!.ConnectionString = _connectionString;
                
                await connection.OpenAsync();
                
                using var command = connection.CreateCommand();
                command.CommandText = _query;
                command.CommandTimeout = _commandTimeout;
                
                // Add parameters
                foreach (var param in _parameters)
                {
                    var dbParam = command.CreateParameter();
                    dbParam.ParameterName = param.Key.StartsWith("@") ? param.Key : "@" + param.Key;
                    dbParam.Value = param.Value ?? DBNull.Value;
                    command.Parameters.Add(dbParam);
                }

                switch (_operation.ToLower())
                {
                    case "select":
                        await ExecuteSelectAsync(command);
                        break;
                    case "execute":
                        await ExecuteNonQueryAsync(command);
                        break;
                    case "scalar":
                        await ExecuteScalarAsync(command);
                        break;
                    default:
                        throw new InvalidOperationException($"Unknown database operation: {_operation}");
                }

                _outputs["success"] = true;
                Console.WriteLine($"   ✅ Database operation completed successfully");
            }
            catch (Exception ex)
            {
                _outputs["success"] = false;
                _outputs["error"] = ex.Message;
                Console.WriteLine($"   ❌ Database operation failed: {ex.Message}");
                throw;
            }
        }

        private async Task ExecuteSelectAsync(DbCommand command)
        {
            using var reader = await command.ExecuteReaderAsync();
            var results = new List<Dictionary<string, object>>();
            var columnNames = new List<string>();

            // Get column names
            for (int i = 0; i < reader.FieldCount; i++)
            {
                columnNames.Add(reader.GetName(i));
            }

            // Read data
            while (await reader.ReadAsync())
            {
                var row = new Dictionary<string, object>();
                for (int i = 0; i < reader.FieldCount; i++)
                {
                    var value = reader.IsDBNull(i) ? null : reader.GetValue(i);
                    row[columnNames[i]] = value;
                }
                results.Add(row);
            }

            _outputs["results"] = results;
            _outputs["rowCount"] = results.Count;
            _outputs["columns"] = columnNames;
            _outputs["operation"] = "select";
        }

        private async Task ExecuteNonQueryAsync(DbCommand command)
        {
            var rowsAffected = await command.ExecuteNonQueryAsync();
            
            _outputs["rowsAffected"] = rowsAffected;
            _outputs["operation"] = "execute";
        }

        private async Task ExecuteScalarAsync(DbCommand command)
        {
            var result = await command.ExecuteScalarAsync();
            
            _outputs["result"] = result;
            _outputs["operation"] = "scalar";
        }

        private DbProviderFactory GetDbProviderFactory()
        {
            // This is a simplified version. In a real implementation, you'd want to:
            // 1. Register providers in DI container
            // 2. Support multiple database types (SQL Server, PostgreSQL, MySQL, SQLite, etc.)
            // 3. Handle provider-specific connection string formats
            
            return _providerName.ToLower() switch
            {
                "system.data.sqlclient" or "sqlserver" => throw new NotSupportedException("SQL Server provider not available in this demo. Use SQLite or add appropriate NuGet packages."),
                "microsoft.data.sqlite" or "sqlite" => throw new NotSupportedException("SQLite provider not available in this demo. Add Microsoft.Data.Sqlite NuGet package."),
                "npgsql" or "postgresql" => throw new NotSupportedException("PostgreSQL provider not available in this demo. Add Npgsql NuGet package."),
                "mysql.data.mysqlclient" or "mysql" => throw new NotSupportedException("MySQL provider not available in this demo. Add MySql.Data NuGet package."),
                _ => throw new NotSupportedException($"Database provider '{_providerName}' is not supported. Add the appropriate NuGet package and register the provider.")
            };
        }

        public void Configure(Dictionary<string, object> inputs)
        {
            if (inputs.TryGetValue("connectionString", out var connStr))
                _connectionString = connStr?.ToString() ?? "";
            
            if (inputs.TryGetValue("providerName", out var provider))
                _providerName = provider?.ToString() ?? "System.Data.SqlClient";
            
            if (inputs.TryGetValue("query", out var query))
                _query = query?.ToString() ?? "";
            
            if (inputs.TryGetValue("operation", out var operation))
                _operation = operation?.ToString() ?? "select";
            
            if (inputs.TryGetValue("commandTimeout", out var timeout))
                _commandTimeout = Convert.ToInt32(timeout);
            
            if (inputs.TryGetValue("parameters", out var parameters))
            {
                if (parameters is Dictionary<string, object> paramDict)
                {
                    _parameters = new Dictionary<string, object>(paramDict);
                }
            }
        }

        public void SetInput(string portName, object value)
        {
            switch (portName.ToLower())
            {
                case "connectionstring":
                case "connection":
                    _connectionString = value?.ToString() ?? "";
                    break;
                case "providername":
                case "provider":
                    _providerName = value?.ToString() ?? "System.Data.SqlClient";
                    break;
                case "query":
                case "sql":
                    _query = value?.ToString() ?? "";
                    break;
                case "operation":
                case "op":
                    _operation = value?.ToString() ?? "select";
                    break;
                case "commandtimeout":
                case "timeout":
                    _commandTimeout = Convert.ToInt32(value);
                    break;
                case "parameters":
                case "params":
                    if (value is Dictionary<string, object> paramDict)
                    {
                        _parameters = new Dictionary<string, object>(paramDict);
                    }
                    break;
            }
        }

        public Dictionary<string, object> GetOutputs()
        {
            return new Dictionary<string, object>(_outputs);
        }
    }
}
