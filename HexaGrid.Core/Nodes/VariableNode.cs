using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace HexaGrid.Core.Nodes
{
    public class VariableNode : INode, IConfigurableNode, IOutputNode
    {
        public string Name => "VariableNode";
        
        private string _variableName = "";
        private object? _value;
        private string _operation = "set"; // set, get, increment, decrement, append
        private readonly Dictionary<string, object> _outputs = new();

        public Task ExecuteAsync(Dictionary<string, object> context)
        {
            switch (_operation.ToLower())
            {
                case "set":
                    context[_variableName] = _value;
                    _outputs["value"] = _value;
                    Console.WriteLine($"📝 Set variable '{_variableName}' = {_value}");
                    break;
                    
                case "get":
                    var retrievedValue = context.TryGetValue(_variableName, out var val) ? val : null;
                    _outputs["value"] = retrievedValue;
                    Console.WriteLine($"📖 Get variable '{_variableName}' = {retrievedValue}");
                    break;
                    
                case "increment":
                    if (context.TryGetValue(_variableName, out var currentVal) && IsNumeric(currentVal))
                    {
                        var numValue = Convert.ToDouble(currentVal);
                        var incrementBy = IsNumeric(_value) ? Convert.ToDouble(_value) : 1;
                        var newValue = numValue + incrementBy;
                        context[_variableName] = newValue;
                        _outputs["value"] = newValue;
                        Console.WriteLine($"➕ Increment '{_variableName}': {numValue} + {incrementBy} = {newValue}");
                    }
                    else
                    {
                        context[_variableName] = _value ?? 1;
                        _outputs["value"] = _value ?? 1;
                        Console.WriteLine($"➕ Initialize '{_variableName}' = {_value ?? 1}");
                    }
                    break;
                    
                case "decrement":
                    if (context.TryGetValue(_variableName, out var currentVal2) && IsNumeric(currentVal2))
                    {
                        var numValue = Convert.ToDouble(currentVal2);
                        var decrementBy = IsNumeric(_value) ? Convert.ToDouble(_value) : 1;
                        var newValue = numValue - decrementBy;
                        context[_variableName] = newValue;
                        _outputs["value"] = newValue;
                        Console.WriteLine($"➖ Decrement '{_variableName}': {numValue} - {decrementBy} = {newValue}");
                    }
                    else
                    {
                        context[_variableName] = _value ?? -1;
                        _outputs["value"] = _value ?? -1;
                        Console.WriteLine($"➖ Initialize '{_variableName}' = {_value ?? -1}");
                    }
                    break;
                    
                case "append":
                    if (context.TryGetValue(_variableName, out var existingVal))
                    {
                        var newValue = existingVal?.ToString() + _value?.ToString();
                        context[_variableName] = newValue;
                        _outputs["value"] = newValue;
                        Console.WriteLine($"📎 Append to '{_variableName}': '{existingVal}' + '{_value}' = '{newValue}'");
                    }
                    else
                    {
                        context[_variableName] = _value?.ToString() ?? "";
                        _outputs["value"] = _value?.ToString() ?? "";
                        Console.WriteLine($"📎 Initialize '{_variableName}' = '{_value}'");
                    }
                    break;
                    
                case "delete":
                    var existed = context.Remove(_variableName);
                    _outputs["existed"] = existed;
                    _outputs["value"] = null;
                    Console.WriteLine($"🗑️ Delete variable '{_variableName}' (existed: {existed})");
                    break;
            }
            
            _outputs["variableName"] = _variableName;
            _outputs["operation"] = _operation;
            _outputs["exists"] = context.ContainsKey(_variableName);
            
            return Task.CompletedTask;
        }

        private static bool IsNumeric(object? value)
        {
            return value != null && (value is int || value is long || value is float || value is double || value is decimal ||
                   double.TryParse(value.ToString(), out _));
        }

        public void Configure(Dictionary<string, object> inputs)
        {
            if (inputs.TryGetValue("variableName", out var varName))
                _variableName = varName?.ToString() ?? "";
            
            if (inputs.TryGetValue("value", out var value))
                _value = value;
            
            if (inputs.TryGetValue("operation", out var operation))
                _operation = operation?.ToString() ?? "set";
        }

        public void SetInput(string portName, object value)
        {
            switch (portName.ToLower())
            {
                case "variablename":
                case "name":
                case "key":
                    _variableName = value?.ToString() ?? "";
                    break;
                case "value":
                case "data":
                    _value = value;
                    break;
                case "operation":
                case "op":
                case "action":
                    _operation = value?.ToString() ?? "set";
                    break;
            }
        }

        public Dictionary<string, object> GetOutputs()
        {
            return new Dictionary<string, object>(_outputs);
        }
    }
}
