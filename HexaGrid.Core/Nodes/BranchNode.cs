using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace HexaGrid.Core.Nodes
{
    public class BranchNode : INode, IConfigurableNode, IOutputNode
    {
        public string Name => "BranchNode";
        
        private object? _condition;
        private string _operator = "istrue";
        private object? _compareValue;
        private readonly Dictionary<string, object> _outputs = new();

        public Task ExecuteAsync(Dictionary<string, object> context)
        {
            bool conditionResult = EvaluateCondition();
            
            Console.WriteLine($"🔀 Branch condition evaluated to: {conditionResult}");
            
            _outputs["condition"] = conditionResult;
            _outputs["true"] = conditionResult;
            _outputs["false"] = !conditionResult;
            _outputs["truePort"] = conditionResult ? "execute" : null;
            _outputs["falsePort"] = !conditionResult ? "execute" : null;
            
            // Set execution paths
            if (conditionResult)
            {
                _outputs["executeTrueBranch"] = true;
                _outputs["executeFalseBranch"] = false;
            }
            else
            {
                _outputs["executeTrueBranch"] = false;
                _outputs["executeFalseBranch"] = true;
            }
            
            return Task.CompletedTask;
        }

        private bool EvaluateCondition()
        {
            try
            {
                return _operator.ToLower() switch
                {
                    "istrue" => IsTrue(_condition),
                    "isfalse" => !IsTrue(_condition),
                    "isnull" => _condition == null,
                    "isnotnull" => _condition != null,
                    "isempty" => string.IsNullOrEmpty(_condition?.ToString()),
                    "isnotempty" => !string.IsNullOrEmpty(_condition?.ToString()),
                    "equals" or "eq" => AreEqual(_condition, _compareValue),
                    "notequals" or "ne" => !AreEqual(_condition, _compareValue),
                    "greaterthan" or "gt" => CompareValues(_condition, _compareValue) > 0,
                    "greaterthanorequal" or "gte" => CompareValues(_condition, _compareValue) >= 0,
                    "lessthan" or "lt" => CompareValues(_condition, _compareValue) < 0,
                    "lessthanorequal" or "lte" => CompareValues(_condition, _compareValue) <= 0,
                    "contains" => _condition?.ToString()?.Contains(_compareValue?.ToString() ?? "") ?? false,
                    "startswith" => _condition?.ToString()?.StartsWith(_compareValue?.ToString() ?? "") ?? false,
                    "endswith" => _condition?.ToString()?.EndsWith(_compareValue?.ToString() ?? "") ?? false,
                    _ => false
                };
            }
            catch
            {
                return false;
            }
        }

        private static bool IsTrue(object? value)
        {
            if (value == null) return false;
            if (value is bool boolValue) return boolValue;
            if (value is string stringValue) 
                return stringValue.ToLower() is "true" or "yes" or "1" or "on";
            if (IsNumeric(value)) return Convert.ToDouble(value) != 0;
            return false;
        }

        private static bool AreEqual(object? a, object? b)
        {
            if (a == null && b == null) return true;
            if (a == null || b == null) return false;
            
            if (IsNumeric(a) && IsNumeric(b))
            {
                return Convert.ToDouble(a) == Convert.ToDouble(b);
            }
            
            return a.ToString() == b.ToString();
        }

        private static int CompareValues(object? a, object? b)
        {
            if (a == null && b == null) return 0;
            if (a == null) return -1;
            if (b == null) return 1;
            
            if (IsNumeric(a) && IsNumeric(b))
            {
                return Convert.ToDouble(a).CompareTo(Convert.ToDouble(b));
            }
            
            return string.Compare(a.ToString(), b.ToString(), StringComparison.OrdinalIgnoreCase);
        }

        private static bool IsNumeric(object? value)
        {
            return value != null && (value is int || value is long || value is float || value is double || value is decimal ||
                   double.TryParse(value.ToString(), out _));
        }

        public void Configure(Dictionary<string, object> inputs)
        {
            if (inputs.TryGetValue("condition", out var condition))
                _condition = condition;
            
            if (inputs.TryGetValue("operator", out var op))
                _operator = op?.ToString() ?? "istrue";
            
            if (inputs.TryGetValue("compareValue", out var compareValue))
                _compareValue = compareValue;
        }

        public void SetInput(string portName, object value)
        {
            switch (portName.ToLower())
            {
                case "condition":
                case "value":
                case "input":
                    _condition = value;
                    break;
                case "operator":
                case "op":
                    _operator = value?.ToString() ?? "istrue";
                    break;
                case "comparevalue":
                case "compare":
                case "target":
                    _compareValue = value;
                    break;
            }
        }

        public Dictionary<string, object> GetOutputs()
        {
            return new Dictionary<string, object>(_outputs);
        }
    }
}
