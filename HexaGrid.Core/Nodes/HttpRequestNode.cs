using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace HexaGrid.Core.Nodes
{
    public class HttpRequestNode : INode, IConfigurableNode, IOutputNode
    {
        public string Name => "HttpRequestNode";
        
        private string _url = "";
        private string _method = "GET";
        private string _body = "";
        private Dictionary<string, string> _headers = new();
        private int _timeoutSeconds = 30;
        private readonly Dictionary<string, object> _outputs = new();
        private static readonly HttpClient _httpClient = new();

        public async Task ExecuteAsync(Dictionary<string, object> context)
        {
            try
            {
                Console.WriteLine($"🌐 HTTP {_method}: {_url}");
                
                using var request = new HttpRequestMessage(new HttpMethod(_method), _url);
                
                // Add headers
                foreach (var header in _headers)
                {
                    request.Headers.TryAddWithoutValidation(header.Key, header.Value);
                }
                
                // Add body for POST/PUT/PATCH
                if (!string.IsNullOrEmpty(_body) && (_method.ToUpper() is "POST" or "PUT" or "PATCH"))
                {
                    request.Content = new StringContent(_body, Encoding.UTF8, "application/json");
                }
                
                // Set timeout
                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(_timeoutSeconds));
                
                var response = await _httpClient.SendAsync(request, cts.Token);
                var responseBody = await response.Content.ReadAsStringAsync();
                
                _outputs["statusCode"] = (int)response.StatusCode;
                _outputs["statusText"] = response.StatusCode.ToString();
                _outputs["isSuccess"] = response.IsSuccessStatusCode;
                _outputs["responseBody"] = responseBody;
                _outputs["contentType"] = response.Content.Headers.ContentType?.ToString() ?? "";
                _outputs["headers"] = GetResponseHeaders(response);
                
                // Try to parse JSON response
                if (response.Content.Headers.ContentType?.MediaType?.Contains("json") == true)
                {
                    try
                    {
                        var jsonData = JsonSerializer.Deserialize<object>(responseBody);
                        _outputs["jsonData"] = jsonData;
                    }
                    catch
                    {
                        // Not valid JSON, keep as string
                    }
                }
                
                Console.WriteLine($"   Status: {response.StatusCode} ({(int)response.StatusCode})");
                
                if (!response.IsSuccessStatusCode)
                {
                    Console.WriteLine($"   Error: {responseBody}");
                }
            }
            catch (TaskCanceledException)
            {
                _outputs["statusCode"] = 408;
                _outputs["statusText"] = "Request Timeout";
                _outputs["isSuccess"] = false;
                _outputs["responseBody"] = "Request timed out";
                _outputs["error"] = "Request timed out";
                Console.WriteLine($"   ⏰ Request timed out after {_timeoutSeconds} seconds");
            }
            catch (Exception ex)
            {
                _outputs["statusCode"] = 0;
                _outputs["statusText"] = "Error";
                _outputs["isSuccess"] = false;
                _outputs["responseBody"] = "";
                _outputs["error"] = ex.Message;
                Console.WriteLine($"   ❌ Error: {ex.Message}");
            }
        }

        private static Dictionary<string, string> GetResponseHeaders(HttpResponseMessage response)
        {
            var headers = new Dictionary<string, string>();
            
            foreach (var header in response.Headers)
            {
                headers[header.Key] = string.Join(", ", header.Value);
            }
            
            foreach (var header in response.Content.Headers)
            {
                headers[header.Key] = string.Join(", ", header.Value);
            }
            
            return headers;
        }

        public void Configure(Dictionary<string, object> inputs)
        {
            if (inputs.TryGetValue("url", out var url))
                _url = url?.ToString() ?? "";
            
            if (inputs.TryGetValue("method", out var method))
                _method = method?.ToString()?.ToUpper() ?? "GET";
            
            if (inputs.TryGetValue("body", out var body))
                _body = body?.ToString() ?? "";
            
            if (inputs.TryGetValue("timeout", out var timeout))
                _timeoutSeconds = Convert.ToInt32(timeout);
            
            if (inputs.TryGetValue("headers", out var headers))
            {
                if (headers is Dictionary<string, object> headerDict)
                {
                    _headers = new Dictionary<string, string>();
                    foreach (var kvp in headerDict)
                    {
                        _headers[kvp.Key] = kvp.Value?.ToString() ?? "";
                    }
                }
            }
        }

        public void SetInput(string portName, object value)
        {
            switch (portName.ToLower())
            {
                case "url":
                    _url = value?.ToString() ?? "";
                    break;
                case "method":
                    _method = value?.ToString()?.ToUpper() ?? "GET";
                    break;
                case "body":
                case "data":
                    _body = value?.ToString() ?? "";
                    break;
                case "timeout":
                    _timeoutSeconds = Convert.ToInt32(value);
                    break;
                case "headers":
                    if (value is Dictionary<string, object> headerDict)
                    {
                        _headers = new Dictionary<string, string>();
                        foreach (var kvp in headerDict)
                        {
                            _headers[kvp.Key] = kvp.Value?.ToString() ?? "";
                        }
                    }
                    break;
            }
        }

        public Dictionary<string, object> GetOutputs()
        {
            return new Dictionary<string, object>(_outputs);
        }
    }
}
