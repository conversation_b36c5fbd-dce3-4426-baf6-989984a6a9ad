using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace HexaGrid.Core.Nodes
{
    public class ConditionalNode : INode, IConfigurableNode, IOutputNode
    {
        public string Name => "ConditionalNode";
        
        private object? _valueA;
        private object? _valueB;
        private string _operator = "equals";
        private readonly Dictionary<string, object> _outputs = new();

        public Task ExecuteAsync(Dictionary<string, object> context)
        {
            bool result = EvaluateCondition();
            
            Console.WriteLine($"🔀 Condition: {_valueA} {_operator} {_valueB} = {result}");
            
            _outputs["result"] = result;
            _outputs["valueA"] = _valueA;
            _outputs["valueB"] = _valueB;
            _outputs["operator"] = _operator;
            _outputs["true"] = result;
            _outputs["false"] = !result;
            
            return Task.CompletedTask;
        }

        private bool EvaluateCondition()
        {
            try
            {
                return _operator.ToLower() switch
                {
                    "equals" or "eq" or "==" => AreEqual(_valueA, _valueB),
                    "notequals" or "ne" or "!=" => !AreEqual(_valueA, _valueB),
                    "greaterthan" or "gt" or ">" => CompareValues(_valueA, _valueB) > 0,
                    "greaterthanorequal" or "gte" or ">=" => CompareValues(_valueA, _valueB) >= 0,
                    "lessthan" or "lt" or "<" => CompareValues(_valueA, _valueB) < 0,
                    "lessthanorequal" or "lte" or "<=" => CompareValues(_valueA, _valueB) <= 0,
                    "contains" => _valueA?.ToString()?.Contains(_valueB?.ToString() ?? "") ?? false,
                    "startswith" => _valueA?.ToString()?.StartsWith(_valueB?.ToString() ?? "") ?? false,
                    "endswith" => _valueA?.ToString()?.EndsWith(_valueB?.ToString() ?? "") ?? false,
                    "isnull" => _valueA == null,
                    "isnotnull" => _valueA != null,
                    "isempty" => string.IsNullOrEmpty(_valueA?.ToString()),
                    "isnotempty" => !string.IsNullOrEmpty(_valueA?.ToString()),
                    "istrue" => IsTrue(_valueA),
                    "isfalse" => !IsTrue(_valueA),
                    _ => false
                };
            }
            catch
            {
                return false;
            }
        }

        private static bool AreEqual(object? a, object? b)
        {
            if (a == null && b == null) return true;
            if (a == null || b == null) return false;
            
            // Try numeric comparison first
            if (IsNumeric(a) && IsNumeric(b))
            {
                return Convert.ToDouble(a) == Convert.ToDouble(b);
            }
            
            return a.ToString() == b.ToString();
        }

        private static int CompareValues(object? a, object? b)
        {
            if (a == null && b == null) return 0;
            if (a == null) return -1;
            if (b == null) return 1;
            
            // Try numeric comparison first
            if (IsNumeric(a) && IsNumeric(b))
            {
                return Convert.ToDouble(a).CompareTo(Convert.ToDouble(b));
            }
            
            return string.Compare(a.ToString(), b.ToString(), StringComparison.OrdinalIgnoreCase);
        }

        private static bool IsNumeric(object? value)
        {
            return value != null && (value is int || value is long || value is float || value is double || value is decimal ||
                   double.TryParse(value.ToString(), out _));
        }

        private static bool IsTrue(object? value)
        {
            if (value == null) return false;
            if (value is bool boolValue) return boolValue;
            if (value is string stringValue) 
                return stringValue.ToLower() is "true" or "yes" or "1" or "on";
            if (IsNumeric(value)) return Convert.ToDouble(value) != 0;
            return false;
        }

        public void Configure(Dictionary<string, object> inputs)
        {
            if (inputs.TryGetValue("valueA", out var valueA))
                _valueA = valueA;
            
            if (inputs.TryGetValue("valueB", out var valueB))
                _valueB = valueB;
            
            if (inputs.TryGetValue("operator", out var op))
                _operator = op?.ToString() ?? "equals";
        }

        public void SetInput(string portName, object value)
        {
            switch (portName.ToLower())
            {
                case "valuea":
                case "a":
                case "left":
                    _valueA = value;
                    break;
                case "valueb":
                case "b":
                case "right":
                    _valueB = value;
                    break;
                case "operator":
                case "op":
                case "condition":
                    _operator = value?.ToString() ?? "equals";
                    break;
            }
        }

        public Dictionary<string, object> GetOutputs()
        {
            return new Dictionary<string, object>(_outputs);
        }
    }
}
