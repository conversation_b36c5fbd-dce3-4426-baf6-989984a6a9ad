using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Mail;
using System.Threading.Tasks;

namespace HexaGrid.Core.Nodes
{
    public class EmailNode : INode, IConfigurableNode, IOutputNode
    {
        public string Name => "EmailNode";
        
        private string _smtpHost = "";
        private int _smtpPort = 587;
        private string _username = "";
        private string _password = "";
        private bool _enableSsl = true;
        private string _from = "";
        private string _to = "";
        private string _cc = "";
        private string _bcc = "";
        private string _subject = "";
        private string _body = "";
        private bool _isHtml = false;
        private List<string> _attachments = new();
        private readonly Dictionary<string, object> _outputs = new();

        public async Task ExecuteAsync(Dictionary<string, object> context)
        {
            if (string.IsNullOrEmpty(_smtpHost))
            {
                throw new ArgumentException("SMTP host is required");
            }

            if (string.IsNullOrEmpty(_from))
            {
                throw new ArgumentException("From address is required");
            }

            if (string.IsNullOrEmpty(_to))
            {
                throw new ArgumentException("To address is required");
            }

            try
            {
                Console.WriteLine($"📧 Sending email to: {_to}");
                Console.WriteLine($"   Subject: {_subject}");

                using var client = new SmtpClient(_smtpHost, _smtpPort);
                client.EnableSsl = _enableSsl;
                
                if (!string.IsNullOrEmpty(_username))
                {
                    client.Credentials = new NetworkCredential(_username, _password);
                }

                using var message = new MailMessage();
                message.From = new MailAddress(_from);
                
                // Add recipients
                foreach (var email in _to.Split(',', ';'))
                {
                    if (!string.IsNullOrWhiteSpace(email))
                    {
                        message.To.Add(email.Trim());
                    }
                }

                // Add CC recipients
                if (!string.IsNullOrEmpty(_cc))
                {
                    foreach (var email in _cc.Split(',', ';'))
                    {
                        if (!string.IsNullOrWhiteSpace(email))
                        {
                            message.CC.Add(email.Trim());
                        }
                    }
                }

                // Add BCC recipients
                if (!string.IsNullOrEmpty(_bcc))
                {
                    foreach (var email in _bcc.Split(',', ';'))
                    {
                        if (!string.IsNullOrWhiteSpace(email))
                        {
                            message.Bcc.Add(email.Trim());
                        }
                    }
                }

                message.Subject = _subject;
                message.Body = _body;
                message.IsBodyHtml = _isHtml;

                // Add attachments
                foreach (var attachmentPath in _attachments)
                {
                    if (System.IO.File.Exists(attachmentPath))
                    {
                        message.Attachments.Add(new Attachment(attachmentPath));
                    }
                    else
                    {
                        Console.WriteLine($"   ⚠️ Attachment not found: {attachmentPath}");
                    }
                }

                await client.SendMailAsync(message);

                _outputs["success"] = true;
                _outputs["sentTo"] = _to;
                _outputs["subject"] = _subject;
                _outputs["sentAt"] = DateTime.UtcNow;
                _outputs["attachmentCount"] = message.Attachments.Count;

                Console.WriteLine($"   ✅ Email sent successfully");
            }
            catch (Exception ex)
            {
                _outputs["success"] = false;
                _outputs["error"] = ex.Message;
                Console.WriteLine($"   ❌ Failed to send email: {ex.Message}");
                throw;
            }
        }

        public void Configure(Dictionary<string, object> inputs)
        {
            if (inputs.TryGetValue("smtpHost", out var host))
                _smtpHost = host?.ToString() ?? "";
            
            if (inputs.TryGetValue("smtpPort", out var port))
                _smtpPort = Convert.ToInt32(port);
            
            if (inputs.TryGetValue("username", out var username))
                _username = username?.ToString() ?? "";
            
            if (inputs.TryGetValue("password", out var password))
                _password = password?.ToString() ?? "";
            
            if (inputs.TryGetValue("enableSsl", out var ssl))
                _enableSsl = Convert.ToBoolean(ssl);
            
            if (inputs.TryGetValue("from", out var from))
                _from = from?.ToString() ?? "";
            
            if (inputs.TryGetValue("to", out var to))
                _to = to?.ToString() ?? "";
            
            if (inputs.TryGetValue("cc", out var cc))
                _cc = cc?.ToString() ?? "";
            
            if (inputs.TryGetValue("bcc", out var bcc))
                _bcc = bcc?.ToString() ?? "";
            
            if (inputs.TryGetValue("subject", out var subject))
                _subject = subject?.ToString() ?? "";
            
            if (inputs.TryGetValue("body", out var body))
                _body = body?.ToString() ?? "";
            
            if (inputs.TryGetValue("isHtml", out var isHtml))
                _isHtml = Convert.ToBoolean(isHtml);
            
            if (inputs.TryGetValue("attachments", out var attachments))
            {
                if (attachments is List<string> attachmentList)
                {
                    _attachments = new List<string>(attachmentList);
                }
                else if (attachments is string attachmentString)
                {
                    _attachments = new List<string>(attachmentString.Split(',', ';'));
                }
            }
        }

        public void SetInput(string portName, object value)
        {
            switch (portName.ToLower())
            {
                case "smtphost":
                case "host":
                    _smtpHost = value?.ToString() ?? "";
                    break;
                case "smtpport":
                case "port":
                    _smtpPort = Convert.ToInt32(value);
                    break;
                case "username":
                case "user":
                    _username = value?.ToString() ?? "";
                    break;
                case "password":
                case "pass":
                    _password = value?.ToString() ?? "";
                    break;
                case "enablessl":
                case "ssl":
                    _enableSsl = Convert.ToBoolean(value);
                    break;
                case "from":
                case "sender":
                    _from = value?.ToString() ?? "";
                    break;
                case "to":
                case "recipient":
                    _to = value?.ToString() ?? "";
                    break;
                case "cc":
                    _cc = value?.ToString() ?? "";
                    break;
                case "bcc":
                    _bcc = value?.ToString() ?? "";
                    break;
                case "subject":
                    _subject = value?.ToString() ?? "";
                    break;
                case "body":
                case "message":
                    _body = value?.ToString() ?? "";
                    break;
                case "ishtml":
                case "html":
                    _isHtml = Convert.ToBoolean(value);
                    break;
                case "attachments":
                    if (value is List<string> attachmentList)
                    {
                        _attachments = new List<string>(attachmentList);
                    }
                    else if (value is string attachmentString)
                    {
                        _attachments = new List<string>(attachmentString.Split(',', ';'));
                    }
                    break;
            }
        }

        public Dictionary<string, object> GetOutputs()
        {
            return new Dictionary<string, object>(_outputs);
        }
    }
}
