using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Threading.Tasks;

namespace HexaGrid.Core.Nodes
{
    public class FileNode : INode, IConfigurableNode, IOutputNode
    {
        public string Name => "FileNode";
        
        private string _filePath = "";
        private string _operation = "read"; // read, write, append, delete, exists, copy, move
        private string _content = "";
        private string _encoding = "utf-8";
        private string _destinationPath = "";
        private readonly Dictionary<string, object> _outputs = new();

        public async Task ExecuteAsync(Dictionary<string, object> context)
        {
            try
            {
                switch (_operation.ToLower())
                {
                    case "read":
                        await ReadFileAsync();
                        break;
                    case "write":
                        await WriteFileAsync();
                        break;
                    case "append":
                        await AppendFileAsync();
                        break;
                    case "delete":
                        DeleteFile();
                        break;
                    case "exists":
                        CheckFileExists();
                        break;
                    case "copy":
                        CopyFile();
                        break;
                    case "move":
                        MoveFile();
                        break;
                    case "info":
                        GetFileInfo();
                        break;
                    default:
                        throw new InvalidOperationException($"Unknown file operation: {_operation}");
                }
                
                Console.WriteLine($"📁 File operation '{_operation}' completed: {_filePath}");
            }
            catch (Exception ex)
            {
                _outputs["success"] = false;
                _outputs["error"] = ex.Message;
                Console.WriteLine($"❌ File operation failed: {ex.Message}");
                throw;
            }
        }

        private async Task ReadFileAsync()
        {
            if (!File.Exists(_filePath))
            {
                throw new FileNotFoundException($"File not found: {_filePath}");
            }

            var encoding = GetEncoding();
            var content = await File.ReadAllTextAsync(_filePath, encoding);
            var fileInfo = new FileInfo(_filePath);

            _outputs["content"] = content;
            _outputs["size"] = fileInfo.Length;
            _outputs["lastModified"] = fileInfo.LastWriteTime;
            _outputs["success"] = true;
            _outputs["operation"] = "read";
        }

        private async Task WriteFileAsync()
        {
            var directory = Path.GetDirectoryName(_filePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            var encoding = GetEncoding();
            await File.WriteAllTextAsync(_filePath, _content, encoding);

            var fileInfo = new FileInfo(_filePath);
            _outputs["success"] = true;
            _outputs["operation"] = "write";
            _outputs["size"] = fileInfo.Length;
            _outputs["path"] = _filePath;
        }

        private async Task AppendFileAsync()
        {
            var directory = Path.GetDirectoryName(_filePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            var encoding = GetEncoding();
            await File.AppendAllTextAsync(_filePath, _content, encoding);

            var fileInfo = new FileInfo(_filePath);
            _outputs["success"] = true;
            _outputs["operation"] = "append";
            _outputs["size"] = fileInfo.Length;
            _outputs["path"] = _filePath;
        }

        private void DeleteFile()
        {
            var existed = File.Exists(_filePath);
            if (existed)
            {
                File.Delete(_filePath);
            }

            _outputs["success"] = true;
            _outputs["operation"] = "delete";
            _outputs["existed"] = existed;
            _outputs["path"] = _filePath;
        }

        private void CheckFileExists()
        {
            var exists = File.Exists(_filePath);
            
            _outputs["exists"] = exists;
            _outputs["success"] = true;
            _outputs["operation"] = "exists";
            _outputs["path"] = _filePath;

            if (exists)
            {
                var fileInfo = new FileInfo(_filePath);
                _outputs["size"] = fileInfo.Length;
                _outputs["lastModified"] = fileInfo.LastWriteTime;
                _outputs["created"] = fileInfo.CreationTime;
            }
        }

        private void CopyFile()
        {
            if (string.IsNullOrEmpty(_destinationPath))
            {
                throw new ArgumentException("Destination path is required for copy operation");
            }

            if (!File.Exists(_filePath))
            {
                throw new FileNotFoundException($"Source file not found: {_filePath}");
            }

            var destinationDirectory = Path.GetDirectoryName(_destinationPath);
            if (!string.IsNullOrEmpty(destinationDirectory) && !Directory.Exists(destinationDirectory))
            {
                Directory.CreateDirectory(destinationDirectory);
            }

            File.Copy(_filePath, _destinationPath, true);

            _outputs["success"] = true;
            _outputs["operation"] = "copy";
            _outputs["sourcePath"] = _filePath;
            _outputs["destinationPath"] = _destinationPath;
        }

        private void MoveFile()
        {
            if (string.IsNullOrEmpty(_destinationPath))
            {
                throw new ArgumentException("Destination path is required for move operation");
            }

            if (!File.Exists(_filePath))
            {
                throw new FileNotFoundException($"Source file not found: {_filePath}");
            }

            var destinationDirectory = Path.GetDirectoryName(_destinationPath);
            if (!string.IsNullOrEmpty(destinationDirectory) && !Directory.Exists(destinationDirectory))
            {
                Directory.CreateDirectory(destinationDirectory);
            }

            File.Move(_filePath, _destinationPath);

            _outputs["success"] = true;
            _outputs["operation"] = "move";
            _outputs["sourcePath"] = _filePath;
            _outputs["destinationPath"] = _destinationPath;
        }

        private void GetFileInfo()
        {
            if (!File.Exists(_filePath))
            {
                throw new FileNotFoundException($"File not found: {_filePath}");
            }

            var fileInfo = new FileInfo(_filePath);
            
            _outputs["success"] = true;
            _outputs["operation"] = "info";
            _outputs["path"] = _filePath;
            _outputs["name"] = fileInfo.Name;
            _outputs["directory"] = fileInfo.DirectoryName;
            _outputs["extension"] = fileInfo.Extension;
            _outputs["size"] = fileInfo.Length;
            _outputs["created"] = fileInfo.CreationTime;
            _outputs["lastModified"] = fileInfo.LastWriteTime;
            _outputs["lastAccessed"] = fileInfo.LastAccessTime;
            _outputs["isReadOnly"] = fileInfo.IsReadOnly;
        }

        private Encoding GetEncoding()
        {
            return _encoding.ToLower() switch
            {
                "utf-8" or "utf8" => Encoding.UTF8,
                "ascii" => Encoding.ASCII,
                "unicode" or "utf-16" => Encoding.Unicode,
                "utf-32" or "utf32" => Encoding.UTF32,
                _ => Encoding.UTF8
            };
        }

        public void Configure(Dictionary<string, object> inputs)
        {
            if (inputs.TryGetValue("filePath", out var filePath))
                _filePath = filePath?.ToString() ?? "";
            
            if (inputs.TryGetValue("operation", out var operation))
                _operation = operation?.ToString() ?? "read";
            
            if (inputs.TryGetValue("content", out var content))
                _content = content?.ToString() ?? "";
            
            if (inputs.TryGetValue("encoding", out var encoding))
                _encoding = encoding?.ToString() ?? "utf-8";
            
            if (inputs.TryGetValue("destinationPath", out var destPath))
                _destinationPath = destPath?.ToString() ?? "";
        }

        public void SetInput(string portName, object value)
        {
            switch (portName.ToLower())
            {
                case "filepath":
                case "path":
                    _filePath = value?.ToString() ?? "";
                    break;
                case "operation":
                case "op":
                    _operation = value?.ToString() ?? "read";
                    break;
                case "content":
                case "data":
                    _content = value?.ToString() ?? "";
                    break;
                case "encoding":
                    _encoding = value?.ToString() ?? "utf-8";
                    break;
                case "destinationpath":
                case "destination":
                case "dest":
                    _destinationPath = value?.ToString() ?? "";
                    break;
            }
        }

        public Dictionary<string, object> GetOutputs()
        {
            return new Dictionary<string, object>(_outputs);
        }
    }
}
